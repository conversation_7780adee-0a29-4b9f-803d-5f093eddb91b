export interface Product {
  id: number;
  name: string;
  price: number;
  type: 'physical' | 'license' | 'internal';
  quantity?: number;
  listed: boolean;
  product_description?: string;
  image_url?: string;
}

export interface ProductFormData {
  name: string;
  price: number;
  type: 'physical' | 'license' | 'internal';
  quantity?: number;
  listed?: boolean;
  product_description?: string;
  image_url?: string;
}
