/**
 * Types for skills, skill areas, and skill levels
 */

export interface SkillArea {
  id: number;
  name_key: string;
  color_theme: string;
  icon_name: string;
  created_at: string;
  updated_at: string;
}

export interface Skill {
  id: number;
  skill_area_id: number;
  name_key: string;
  created_at: string;
  updated_at: string;
}

export interface SkillLevel {
  id: number;
  name_key: string;
  level_order: number;
  created_at: string;
  updated_at: string;
}

export interface ChildSkillProgress {
  id: number;
  child_id: number;
  skill_id: number;
  skill_level_id: number | null;
  progress: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Additional fields when joined with other tables
  skill?: Skill;
  skill_level?: SkillLevel;
  skill_area?: SkillArea;
}

// Type for the skill progress with all related data
export interface SkillProgressWithDetails {
  progress: ChildSkillProgress;
  skill: Skill;
  skill_area: SkillArea;
  skill_level: SkillLevel | null;
}

// Type for the skill area with skills and progress
export interface SkillAreaWithSkills extends SkillArea {
  skills: SkillWithProgress[];
}

// Type for the skill with progress
export interface SkillWithProgress extends Skill {
  progress: number;
  level: SkillLevel | null;
}
