/**
 * Types for learning events
 */

import { Skill, SkillLevel } from './skill';

export interface LearningEvent {
  id: number;
  child_id: number;
  event_date: string;
  event_type: string;
  description_key: string | null;
  achievement_details_key: string | null;
  related_skill_id: number | null;
  related_skill_ids?: number[]; // Array of skill IDs
  skill_level_achieved_id: number | null;
  skill_progress_at_event: number | null;
  created_at: string;
  updated_at: string;
}

// Type for learning event with related data
export interface LearningEventWithDetails extends LearningEvent {
  skill?: Skill;
  skills?: Skill[]; // Array of related skills
  skill_level_achieved?: SkillLevel;
  child_name?: string;
}
