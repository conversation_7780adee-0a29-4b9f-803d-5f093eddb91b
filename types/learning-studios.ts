export interface Room {
  id: number;
  name: string;
  learning_studio_id: number;
  max_seats: number;
  price: number;
  description?: string;
  door_image_url?: string;
  atmosphere_image_url?: string;
  table_image_url?: string;
  wall_image_url?: string;
}

export interface LearningStudio {
  id: number;
  name: string;
  description?: string;
  header_image_url?: string;
  checkin_image_url?: string;
  waiting_zone_image_url?: string;
  atmosphere_image_url?: string;
  phone?: string;
  address?: string;
  field?: string;
  email?: string;
  rooms?: Room[];
}

export interface LearningStudioFormData {
  name: string;
  description?: string;
  header_image_url?: string;
  checkin_image_url?: string;
  waiting_zone_image_url?: string;
  atmosphere_image_url?: string;
  phone?: string;
  address?: string;
  field?: string;
  email?: string;
}

export interface OpeningHour {
  id: number;
  studio_id: number;
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday';
  morning_open: string;
  morning_close: string;
  afternoon_open: string | null;
  afternoon_close: string | null;
}

export interface OpeningHourFormData {
  studio_id: number;
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday';
  morning_open: string;
  morning_close: string;
  afternoon_open: string | null;
  afternoon_close: string | null;
}

export interface HolidayClosure {
  id: number;
  studio_id: number;
  start_date: string;
  end_date: string;
  reason: string | null;
}

export interface HolidayClosureFormData {
  studio_id: number;
  start_date: string;
  end_date: string;
  reason: string | null;
}

export interface Booking {
  id: number;
  parent_id: number;
  child_id: number;
  child_name: string;
  start_time: string;
  end_time: string;
  room_id: number;
  price: number;
  learning_studios_id: number;
}
