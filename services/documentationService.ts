/**
 * Service for documentation-related API calls in the parents app
 */

import { apiService } from './api';

/**
 * Interface for documentation data
 */
export interface Documentation {
  id?: number;
  child_id: number;
  general_notes?: string;
  learning_goals?: string;
  strengths?: string;
  areas_for_improvement?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Service for documentation-related API calls
 */
export class DocumentationService {
  /**
   * Get documentation for a specific child
   * @param childId - The child ID
   * @returns The documentation for the child
   */
  async getDocumentation(childId: number): Promise<Documentation | null> {
    try {
      const response = await apiService.get<Documentation>(`children/${childId}/documentation`);
      return response;
    } catch (error) {
      // If 404, return null (no documentation found)
      if ((error as Error).message.includes('404')) {
        return null;
      }
      console.error('Error fetching documentation:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const documentationService = new DocumentationService();
