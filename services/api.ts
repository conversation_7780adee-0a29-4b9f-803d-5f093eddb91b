/**
 * Base API service for making requests to the server
 */

// Get the API URL from environment variables
const API_URL = process.env.NEXT_PUBLIC_API_URL || '';

/**
 * Base API class with methods for making HTTP requests
 */
export class ApiService {
  private baseUrl: string;

  constructor() {
    // Always use the API proxy through Next.js
    this.baseUrl = '/api/';
  }

  /**
   * Get the authorization header with the token
   */
  private getAuthHeader(): Headers {
    const token = typeof window !== 'undefined' ? localStorage.getItem('userToken') : null;
    const headers = new Headers({
      'Content-Type': 'application/json',
    });

    if (token) {
      headers.append('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Make a GET request to the API
   * @param endpoint - The API endpoint
   * @returns The response data
   */
  async get<T>(endpoint: string): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeader(),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    try {
      const data = await response.json();
      return data as T;
    } catch (error) {
      throw new Error(`Error parsing API response: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Make a POST request to the API
   * @param endpoint - The API endpoint
   * @param data - The data to send
   * @returns The response data
   */
  async post<T>(endpoint: string, data: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeader(),
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json() as Promise<T>;
  }

  /**
   * Make a PUT request to the API
   * @param endpoint - The API endpoint
   * @param data - The data to send
   * @returns The response data
   */
  async put<T>(endpoint: string, data: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getAuthHeader(),
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json() as Promise<T>;
  }

  /**
   * Make a PATCH request to the API
   * @param endpoint - The API endpoint
   * @param data - The data to send
   * @returns The response data
   */
  async patch<T>(endpoint: string, data: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: 'PATCH',
      headers: this.getAuthHeader(),
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json() as Promise<T>;
  }

  /**
   * Make a DELETE request to the API
   * @param endpoint - The API endpoint
   * @returns The response data
   */
  async delete<T>(endpoint: string): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getAuthHeader(),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json() as Promise<T>;
  }
}

// Export a singleton instance
export const apiService = new ApiService();
