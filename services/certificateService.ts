/**
 * Service for certificate-related API calls
 */

import { apiService } from './api';
import { Certificate, CertificateWithSkills } from '../types/certificate';
import { getApiUrl } from '../utils/apiConfig';

/**
 * Helper function to clean Google redirect URLs
 * @param url - The URL to clean
 * @returns The cleaned URL
 */
const cleanGoogleRedirectUrl = (url: string | undefined): string | undefined => {
  if (!url) return undefined;

  // Check if it's a Google redirect URL
  if (url.includes('google.com/url?') && url.includes('&url=')) {
    try {
      // Extract the actual URL from the Google redirect
      const match = url.match(/&url=([^&]+)/);
      if (match && match[1]) {
        // Decode the URL
        return decodeURIComponent(match[1]);
      }
    } catch (e) {
      console.error('Error parsing Google redirect URL:', e);
    }
  }
  return url;
};

/**
 * Service for certificate-related API calls
 */
export class CertificateService {
  private baseUrl: string;

  constructor() {
    // Get the base API URL without any path
    this.baseUrl = getApiUrl();

    // Ensure we have a valid base URL
    if (!this.baseUrl || this.baseUrl === '') {
      this.baseUrl = 'https://server-beta-fkw4.onrender.com';
      console.warn('No API URL found, using fallback:', this.baseUrl);
    }

    console.log('Certificate service initialized with base URL:', this.baseUrl);
  }

  /**
   * Get the authorization header with the token
   */
  private getAuthHeader(): Headers {
    const token = typeof window !== 'undefined' ? localStorage.getItem('userToken') : null;
    const headers = new Headers({
      'Content-Type': 'application/json',
    });

    if (token) {
      headers.append('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Get all certificates
   * @returns List of certificates
   */
  async getCertificates(): Promise<Certificate[]> {
    try {
      // Try using the direct API call first
      const url = `${this.baseUrl}/certificates`;
      console.log('Fetching certificates from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeader(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error with direct API call, falling back to proxy:', error);
      // Fall back to the proxy if direct call fails
      return apiService.get<Certificate[]>('/certificates');
    }
  }

  /**
   * Get a specific certificate
   * @param id - The certificate ID
   * @returns The certificate
   */
  async getCertificate(id: number): Promise<Certificate> {
    try {
      // Try using the direct API call first
      const url = `${this.baseUrl}/certificates/${id}`;
      console.log('Fetching certificate from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeader(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error with direct API call, falling back to proxy:', error);
      // Fall back to the proxy if direct call fails
      return apiService.get<Certificate>(`/certificates/${id}`);
    }
  }

  /**
   * Get all certificates for a specific child
   * @param childId - The child ID
   * @returns List of certificates with skills
   */
  async getChildCertificates(childId: number): Promise<CertificateWithSkills[]> {
    try {
      // Try using the direct API call first
      const url = `${this.baseUrl}/children/${childId}/certificates`;
      console.log('Fetching child certificates from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeader(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error with direct API call, falling back to proxy:', error);
      // Fall back to the proxy if direct call fails
      return apiService.get<CertificateWithSkills[]>(`/children/${childId}/certificates`);
    }
  }

  /**
   * Get all skills for a specific certificate
   * @param certificateId - The certificate ID
   * @returns List of skill IDs
   */
  async getCertificateSkills(certificateId: number): Promise<number[]> {
    try {
      // Try using the direct API call first
      const url = `${this.baseUrl}/certificates/${certificateId}/skills`;
      console.log('Fetching certificate skills from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeader(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error with direct API call, falling back to proxy:', error);
      // Fall back to the proxy if direct call fails
      return apiService.get<number[]>(`/certificates/${certificateId}/skills`);
    }
  }
}

// Export a singleton instance
export const certificateService = new CertificateService();
