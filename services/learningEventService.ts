/**
 * Service for learning event-related API calls
 */

import { apiService } from './api';
import { LearningEvent, LearningEventWithDetails } from '../types/learningEvent';

/**
 * Service for learning event-related API calls
 */
export class LearningEventService {
  /**
   * Get all learning events
   * @param limit - Maximum number of events to return
   * @param offset - Number of events to skip
   * @returns List of learning events
   */
  async getLearningEvents(limit?: number, offset?: number): Promise<LearningEvent[]> {
    let endpoint = '/learning-events';
    const params = [];

    if (limit !== undefined) {
      params.push(`limit=${limit}`);
    }

    if (offset !== undefined) {
      params.push(`offset=${offset}`);
    }

    if (params.length > 0) {
      endpoint += `?${params.join('&')}`;
    }

    return apiService.get<LearningEvent[]>(endpoint);
  }

  /**
   * Get a specific learning event
   * @param id - The learning event ID
   * @returns The learning event
   */
  async getLearningEvent(id: number): Promise<LearningEvent> {
    return apiService.get<LearningEvent>(`/learning-events/${id}`);
  }

  /**
   * Get all learning events for a specific child
   * @param childId - The child ID
   * @param limit - Maximum number of events to return
   * @param offset - Number of events to skip
   * @param startDate - Filter events after this date (ISO string)
   * @param endDate - Filter events before this date (ISO string)
   * @returns List of learning events with details
   */
  async getChildLearningEvents(
    childId: number,
    limit?: number,
    offset?: number,
    startDate?: string,
    endDate?: string
  ): Promise<LearningEventWithDetails[]> {
    // Check for token
    const token = typeof window !== 'undefined' ? localStorage.getItem('userToken') : null;
    if (!token) {
      console.error('LearningEventService: No authentication token found');
      return Promise.reject('No authentication token found');
    }

    // Try direct API call first (like in assistants app)
    try {
      // Build direct API URL
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      if (!apiUrl) {
        throw new Error('API URL is not configured');
      }

      // Create the direct URL
      let directUrl = `${apiUrl}/children/${childId}/learning-events`;

      // Add query parameters if provided
      const queryParams = [];
      if (limit !== undefined) {
        queryParams.push(`limit=${limit}`);
      }
      if (offset !== undefined) {
        queryParams.push(`offset=${offset}`);
      }
      if (startDate) {
        queryParams.push(`start_date=${encodeURIComponent(startDate)}`);
      }
      if (endDate) {
        queryParams.push(`end_date=${encodeURIComponent(endDate)}`);
      }

      if (queryParams.length > 0) {
        directUrl += `?${queryParams.join('&')}`;
      }

      // Make the direct request
      const response = await fetch(directUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors',
        credentials: 'include'
      });

      if (!response.ok) {
        if (response.status === 404) {
          return [];
        }
        throw new Error(`Failed to fetch learning events: ${response.status} ${response.statusText}`);
      }

      // Parse response
      const events = await response.json();
      return events;
    } catch (directError) {
      // If direct API call fails, fall back to the Next.js API route
      console.warn('LearningEventService: Direct API call failed, falling back to Next.js API route');

      try {
        // Use the dedicated learning-events API route
        let endpoint = `/learning-events`;
        const params = [`childId=${childId}`];

        if (limit !== undefined) {
          params.push(`limit=${limit}`);
        }

        if (offset !== undefined) {
          params.push(`offset=${offset}`);
        }

        if (startDate) {
          params.push(`start_date=${encodeURIComponent(startDate)}`);
        }

        if (endDate) {
          params.push(`end_date=${encodeURIComponent(endDate)}`);
        }

        // Always add params since we at least have childId
        endpoint += `?${params.join('&')}`;

        const events = await apiService.get<LearningEventWithDetails[]>(endpoint);

        // Ensure we're returning an array
        if (!Array.isArray(events)) {
          return [];
        }

        return events;
      } catch (fallbackError) {
        console.error('LearningEventService: Both API approaches failed');
        throw fallbackError;
      }
    }
  }
}

// Export a singleton instance
export const learningEventService = new LearningEventService();
