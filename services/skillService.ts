/**
 * Service for skill-related API calls
 */

import { apiService } from './api';
import {
  SkillArea,
  Skill,
  SkillLevel,
  ChildSkillProgress,
  SkillProgressWithDetails
} from '../types/skill';

/**
 * Service for skill-related API calls
 */
export class SkillService {
  /**
   * Get all skill areas
   * @returns List of skill areas
   */
  async getSkillAreas(): Promise<SkillArea[]> {
    return apiService.get<SkillArea[]>('/skill-areas');
  }

  /**
   * Get a specific skill area
   * @param id - The skill area ID
   * @returns The skill area
   */
  async getSkillArea(id: number): Promise<SkillArea> {
    return apiService.get<SkillArea>(`/skill-areas/${id}`);
  }

  /**
   * Get all skills for a specific skill area
   * @param skillAreaId - The skill area ID
   * @returns List of skills
   */
  async getSkillsByArea(skillAreaId: number): Promise<Skill[]> {
    return apiService.get<Skill[]>(`/skill-areas/${skillAreaId}/skills`);
  }

  /**
   * Get all skill levels
   * @returns List of skill levels
   */
  async getSkillLevels(): Promise<SkillLevel[]> {
    return apiService.get<SkillLevel[]>('/skill-levels');
  }

  /**
   * Get all skill progress for a specific child
   * @param childId - The child ID
   * @returns List of skill progress with details
   */
  async getChildSkillProgress(childId: number): Promise<SkillProgressWithDetails[]> {
    return apiService.get<SkillProgressWithDetails[]>(`/children/${childId}/skill-progress`);
  }

  /**
   * Get all skill progress for a specific child and skill area
   * @param childId - The child ID
   * @param skillAreaId - The skill area ID
   * @returns List of skill progress with details
   */
  async getChildSkillProgressByArea(childId: number, skillAreaId: number): Promise<SkillProgressWithDetails[]> {
    return apiService.get<SkillProgressWithDetails[]>(`/children/${childId}/skill-areas/${skillAreaId}/progress`);
  }

  /**
   * Update a child's skill progress
   * @param progressId - The progress ID
   * @param progress - The new progress value (0-100)
   * @param skillLevelId - The new skill level ID (optional)
   * @returns The updated progress
   */
  async updateChildSkillProgress(
    progressId: number,
    progress: number,
    skillLevelId?: number
  ): Promise<ChildSkillProgress> {
    return apiService.patch<ChildSkillProgress>(`/child-skill-progress/${progressId}`, {
      progress,
      skill_level_id: skillLevelId
    });
  }

  /**
   * Get progress history for a specific child and skill
   * @param childId - The child ID
   * @param skillId - The skill ID
   * @returns List of historical progress records
   */
  async getChildSkillProgressHistory(childId: number, skillId: number): Promise<any[]> {
    return apiService.get<any[]>(`/children/${childId}/skills/${skillId}/progress-history`);
  }
}

// Export a singleton instance
export const skillService = new SkillService();
