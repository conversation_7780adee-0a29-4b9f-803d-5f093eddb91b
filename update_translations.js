const fs = require('fs');
const path = require('path');

// List of all language files
const languages = [
  'ar', 'bn', 'cs', 'da', 'de', 'el', 'en', 'es', 'fr', 'he', 
  'hi', 'it', 'ja', 'ko', 'nl', 'pl', 'pt', 'tr', 'uk', 'zh'
];

// Translations for "Last updated" in different languages
const translations = {
  ar: 'آخر تحديث',
  bn: 'সর্বশেষ আপডেট',
  cs: 'Poslední aktualizace',
  da: 'Sidst opdateret',
  de: 'Zuletzt aktualisiert',
  el: 'Τελευταία ενημέρωση',
  en: 'Last updated',
  es: 'Última actualización',
  fr: 'Dernière mise à jour',
  he: 'עדכון אחרון',
  hi: 'आखिरी अपडेट',
  it: 'Ultimo aggiornamento',
  ja: '最終更新',
  ko: '마지막 업데이트',
  nl: 'Laatst bijgewerkt',
  pl: 'Ostatnia aktualizacja',
  pt: 'Última atualização',
  tr: 'Son güncelleme',
  uk: 'Останнє оновлення',
  zh: '最后更新'
};

// Process each language file
languages.forEach(lang => {
  const filePath = path.join('translations', 'messages', `${lang}.json`);
  
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const jsonContent = JSON.parse(fileContent);
    
    // Check if the LearningDocumentation.Progress path exists
    if (jsonContent.LearningDocumentation && jsonContent.LearningDocumentation.Progress) {
      // Add the lastUpdatedPrefix translation
      jsonContent.LearningDocumentation.Progress.lastUpdatedPrefix = translations[lang] || translations.en;
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2), 'utf8');
      console.log(`Updated ${lang}.json`);
    } else {
      console.log(`Skipping ${lang}.json - LearningDocumentation.Progress path not found`);
    }
  } catch (error) {
    console.error(`Error processing ${lang}.json:`, error.message);
  }
});

console.log('Translation update complete!');
