import type { Metadata } from "next";
import { Inter, <PERSON>o_Mono, Noto_Sans_Arabic, Noto_Sans_Hebrew } from "next/font/google";
import "./globals.css";

// Latin font
const inter = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
});

// Monospace font
const robotoMono = Roboto_Mono({
  variable: "--font-mono",
  subsets: ["latin"],
});

// Arabic font
const notoSansArabic = Noto_Sans_Arabic({
  variable: "--font-arabic",
  subsets: ["arabic"],
  weight: ["400", "700"],
  display: "swap",
});

// Hebrew font
const notoSansHebrew = Noto_Sans_Hebrew({
  variable: "--font-hebrew",
  subsets: ["hebrew"],
  weight: ["400", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Parents App",
  description: "Learning Studios Parents Application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${robotoMono.variable} ${notoSansArabic.variable} ${notoSansHebrew.variable}`}>
        {children}
      </body>
    </html>
  );
}
