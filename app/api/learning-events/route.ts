import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

/**
 * Handle GET requests to the learning events API
 * This route specifically handles fetching learning events for a child
 */
export async function GET(request: NextRequest) {
  try {
    console.log('API route (GET learning-events): Received GET request to /api/learning-events');
    console.log('API route (GET learning-events): Request URL:', request.url);

    // Get the URL parameters
    const url = new URL(request.url);
    const childId = url.searchParams.get('childId');
    console.log(`API route (GET learning-events): Request for child ID: ${childId}`);
    console.log('API route (GET learning-events): All URL parameters:', JSON.stringify(Object.fromEntries(url.searchParams.entries()), null, 2));

    // Check if childId is provided
    if (!childId) {
      console.error('API route (GET learning-events): Missing childId parameter');
      return NextResponse.json(
        { error: 'Missing required parameter: childId' },
        { status: 400 }
      );
    }

    // Get the authorization token from the request headers
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      console.error('API route (GET learning-events): Missing authorization header');
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }
    console.log(`API route (GET learning-events): Auth header exists (first 15 chars): ${authHeader.substring(0, 15)}...`);

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      console.error('API route (GET learning-events): API URL is not configured');
      return NextResponse.json(
        { error: 'API URL is not configured' },
        { status: 500 }
      );
    }
    console.log(`API route (GET learning-events): Using API URL: ${apiUrl}`);

    // Build the server URL with query parameters
    let serverUrl = `${apiUrl}/children/${childId}/learning-events`;

    // Forward any query parameters from the original request
    const queryParams = [];
    for (const [key, value] of url.searchParams.entries()) {
      if (key !== 'childId') { // Skip the childId as it's part of the path
        queryParams.push(`${key}=${encodeURIComponent(value)}`);
      }
    }

    if (queryParams.length > 0) {
      serverUrl += `?${queryParams.join('&')}`;
    }

    console.log(`API route (GET learning-events): Proxying GET request to: ${serverUrl}`);

    // Make the request to the server
    try {
      const response = await fetch(serverUrl, {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log(`API route (GET learning-events): Server response status: ${response.status} ${response.statusText}`);

      // If the endpoint doesn't exist yet, return an empty array instead of an error
      if (response.status === 404) {
        console.log('API route (GET learning-events): Endpoint not found, returning empty array');
        return NextResponse.json([]);
      }

      // If there was an error, try to get more details
      if (!response.ok) {
        let errorJson = null;
        let errorText = '';

        try {
          errorJson = await response.json();
        } catch (e) {
          try {
            errorText = await response.text();
          } catch (textError) {
            console.error('API route (GET learning-events): Failed to get response text:', textError);
          }
        }

        console.error('API route (GET learning-events): Error response from server:', errorJson || errorText || 'No error details available');

        return NextResponse.json(
          {
            error: 'Error from server',
            status: response.status,
            statusText: response.statusText,
            details: errorJson || errorText || 'No error details available'
          },
          { status: response.status }
        );
      }

      // Parse and return the data
      try {
        const data = await response.json();
        console.log(`API route (GET learning-events): Successfully received ${data.length} learning events for child ID ${childId}`);

        // Log the first event if available for debugging
        if (data.length > 0) {
          console.log('API route (GET learning-events): First event:', JSON.stringify(data[0], null, 2));
        } else {
          console.log('API route (GET learning-events): No events found for this child. This is normal if no events have been created yet.');
        }



        return NextResponse.json(data);
      } catch (e) {
        console.error('API route (GET learning-events): Failed to parse response JSON:', e);
        return NextResponse.json(
          { error: 'Failed to parse server response' },
          { status: 500 }
        );
      }
    } catch (fetchError) {
      console.error('API route (GET learning-events): Fetch error:', fetchError);
      return NextResponse.json(
        {
          error: 'Failed to connect to server',
          message: fetchError instanceof Error ? fetchError.message : String(fetchError)
        },
        { status: 502 }
      );
    }
  } catch (error) {
    console.error('API route (GET learning-events): Unhandled error:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: errorMessage,
        apiUrl: process.env.NEXT_PUBLIC_API_URL || 'Not configured'
      },
      { status: 500 }
    );
  }
}
