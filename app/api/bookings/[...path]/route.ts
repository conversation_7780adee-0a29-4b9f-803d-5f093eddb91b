import { NextRequest, NextResponse } from 'next/server';

// Get the API URL from environment variables
const API_URL = process.env.NEXT_PUBLIC_API_URL || '';

// Validate that the API URL is set
if (!API_URL) {
  console.error('NEXT_PUBLIC_API_URL environment variable is not set');
}

/**
 * Handle GET requests to the bookings API
 * @param request - The incoming request
 * @param context - The context containing params
 * @returns The response from the API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the path from the params
    const path = params.path.join('/');

    // Get the URL search params
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Get the token from the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader ? authHeader.split(' ')[1] : null;

    // Create headers for the API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add the authorization header if a token is provided
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Make the request to the API
    const response = await fetch(`${API_URL}bookings/${path}${queryString ? `?${queryString}` : ''}`, {
      method: 'GET',
      headers,
      credentials: 'include',
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, {
      status: response.status,
    });
  } catch (error) {
    console.error('Error in bookings API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch data from the API' },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests to the bookings API
 * @param request - The incoming request
 * @param context - The context containing params
 * @returns The response from the API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the path from the params
    const path = params.path.join('/');

    // Get the request body
    const body = await request.json();

    // Get the token from the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader ? authHeader.split(' ')[1] : null;

    // Create headers for the API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add the authorization header if a token is provided
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Make the request to the API
    const response = await fetch(`${API_URL}bookings/${path}`, {
      method: 'POST',
      headers,
      credentials: 'include',
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, {
      status: response.status,
    });
  } catch (error) {
    console.error('Error in bookings API route:', error);
    return NextResponse.json(
      { error: 'Failed to send data to the API' },
      { status: 500 }
    );
  }
}

/**
 * Handle PUT requests to the bookings API
 * @param request - The incoming request
 * @param context - The context containing params
 * @returns The response from the API
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the path from the params
    const path = params.path.join('/');

    // Get the request body
    const body = await request.json();

    // Get the URL search params
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Get the token from the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader ? authHeader.split(' ')[1] : null;

    // Create headers for the API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add the authorization header if a token is provided
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Make the request to the API
    const response = await fetch(`${API_URL}bookings/${path}${queryString ? `?${queryString}` : ''}`, {
      method: 'PUT',
      headers,
      credentials: 'include',
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, {
      status: response.status,
    });
  } catch (error) {
    console.error('Error in bookings API route:', error);
    return NextResponse.json(
      { error: 'Failed to update data in the API' },
      { status: 500 }
    );
  }
}

/**
 * Handle DELETE requests to the bookings API
 * @param request - The incoming request
 * @param context - The context containing params
 * @returns The response from the API
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the path from the params
    const path = params.path.join('/');

    // Get the URL search params
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Get the token from the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader ? authHeader.split(' ')[1] : null;

    // Create headers for the API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add the authorization header if a token is provided
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Make the request to the API
    const response = await fetch(`${API_URL}bookings/${path}${queryString ? `?${queryString}` : ''}`, {
      method: 'DELETE',
      headers,
      credentials: 'include',
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, {
      status: response.status,
    });
  } catch (error) {
    console.error('Error in bookings API route:', error);
    return NextResponse.json(
      { error: 'Failed to delete data from the API' },
      { status: 500 }
    );
  }
}
