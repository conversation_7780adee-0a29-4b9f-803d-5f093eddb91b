"use client";

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

const BetaTag: React.FC = () => {
  const [isMounted, setIsMounted] = useState(false);
  const [isBetaEnabled, setIsBetaEnabled] = useState(false);

  // Wrap the useTranslations hook in a try-catch to handle potential errors
  let betaTagText = 'Beta';
  try {
    // Get translations
    const t = useTranslations('common');
    betaTagText = t('betaTag');
  } catch (error) {
    console.warn('Failed to load translation for BetaTag:', error);
    // Fallback to default text
  }

  useEffect(() => {
    setIsMounted(true);
    const betaFlag = process.env.NEXT_PUBLIC_BETA_ENABLED;
    setIsBetaEnabled(!!betaFlag && betaFlag.toLowerCase() === 'true');
  }, []);

  // Only render on client-side and if beta is enabled
  if (!isMounted || !isBetaEnabled) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: 10,
      left: 10,
      backgroundColor: '#ff4081',
      color: 'white',
      padding: '4px 8px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 1000
    }}>
      {betaTagText}
    </div>
  );
};

export default BetaTag;