"use client";

import React from 'react';
import { useTranslations } from 'next-intl';

interface ProgressCardProps {
  skillName: string;
  skillArea: string;
  skillLevel?: string;
  progress: number;
  notes?: string;
  isLoading?: boolean;
  lastUpdated?: string | null;
  onClick: () => void;
}

const ProgressCard: React.FC<ProgressCardProps> = ({
  skillName,
  skillArea,
  skillLevel,
  progress,
  notes,
  isLoading = false,
  lastUpdated = null,
  onClick
}) => {
  const t = useTranslations('LearningDocumentation.Progress');

  // Ensure progress is a valid number
  let safeProgress = 0;

  if (progress !== undefined && progress !== null) {
    // Convert to number if it's a string
    const numProgress = typeof progress === 'string' ? parseInt(progress, 10) : progress;
    // Ensure it's a valid number between 0 and 100
    safeProgress = typeof numProgress === 'number' && !isNaN(numProgress) ?
      Math.min(100, Math.max(0, numProgress)) : 0;
  }

  // Log the progress value for debugging
  console.log(`ProgressCard for ${skillName}: original progress=${progress} (${typeof progress}), safeProgress=${safeProgress}`);

  return (
    <div
      className="bg-white/5 backdrop-blur-xl border border-white/10 hover:border-yellow-400 rounded-lg p-4 shadow-md transition-all duration-200 cursor-pointer"
      onClick={onClick}
    >
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-yellow-400">{skillName}</h3>
            <p className="text-sm text-gray-300">({skillArea})</p>
          </div>
          <div className="flex items-center">
            {isLoading ? (
              <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-yellow-400 border-r-2 border-b-2 border-transparent"></div>
              </div>
            ) : (
              <div className="relative w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                <div className="absolute inset-0 rounded-full"
                  style={{
                    background: `conic-gradient(#f59e0b ${safeProgress}%, transparent ${safeProgress}%)`,
                    clipPath: 'circle(50% at center)'
                  }}
                />
                <div className="absolute inset-1 rounded-full bg-purple-900 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">{safeProgress}%</span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col space-y-1">
          {skillLevel && (
            <div className="flex items-center">
              <span className="text-xs text-gray-400">{t('levelPrefix')}: </span>
              <span className="text-xs text-gray-200 ml-1">{skillLevel}</span>
            </div>
          )}

          {notes && (
            <div className="flex items-start">
              <span className="text-xs text-gray-400">{t('notesPrefix')}: </span>
              <span className="text-xs text-gray-200 ml-1 line-clamp-2">{notes}</span>
            </div>
          )}

          {isLoading && (
            <div className="flex items-center">
              <span className="text-xs text-gray-400 italic">Loading latest progress...</span>
            </div>
          )}

          {!isLoading && lastUpdated && (
            <div className="flex items-center">
              <span className="text-xs text-gray-400">{t('lastUpdatedPrefix', {defaultValue: 'Last updated'})}: </span>
              <span className="text-xs text-gray-200 ml-1">{lastUpdated}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProgressCard;
