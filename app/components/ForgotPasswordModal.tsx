"use client";

import React, { useState } from 'react';

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  locale?: string; // Add locale prop
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  isOpen,
  onClose,
  locale = 'en' // Default locale
}) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  if (!isOpen) return null;

  // Translations object
  const translations = {
    en: {
      title: "Forgot Password",
      close: "Close",
      successMessage: "Password reset instructions have been sent to your email.",
      errorMessage: "An unexpected error occurred. Please try again.",
      returnToLogin: "Return to Login",
      prompt: "Enter your email address and we will send you instructions to reset your password.",
      emailLabel: "Email Address",
      emailPlaceholder: "Enter your email address",
      cancel: "Cancel",
      sending: "Sending...",
      sendLink: "Send Reset Link"
    },
    de: {
      title: "Passwort vergessen",
      close: "Schließen",
      successMessage: "Anweisungen zum Zurücksetzen des Passworts wurden an Ihre E-Mail gesendet.",
      errorMessage: "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.",
      returnToLogin: "Zurück zum Login",
      prompt: "Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen Anweisungen zum Zurücksetzen Ihres Passworts.",
      emailLabel: "E-Mail-Adresse",
      emailPlaceholder: "Geben Sie Ihre E-Mail-Adresse ein",
      cancel: "Abbrechen",
      sending: "Senden...",
      sendLink: "Link zum Zurücksetzen senden"
    },
    es: {
      title: "Olvidé mi Contraseña",
      close: "Cerrar",
      successMessage: "Se han enviado instrucciones para restablecer la contraseña a tu correo electrónico.",
      errorMessage: "Ocurrió un error inesperado. Por favor, inténtalo de nuevo.",
      returnToLogin: "Volver al Inicio de Sesión",
      prompt: "Introduce tu dirección de correo electrónico y te enviaremos instrucciones para restablecer tu contraseña.",
      emailLabel: "Dirección de Correo Electrónico",
      emailPlaceholder: "Introduce tu dirección de correo electrónico",
      cancel: "Cancelar",
      sending: "Enviando...",
      sendLink: "Enviar Enlace de Restablecimiento"
    },
    fr: {
      title: "Mot de Passe Oublié",
      close: "Fermer",
      successMessage: "Les instructions de réinitialisation du mot de passe ont été envoyées à votre adresse e-mail.",
      errorMessage: "Une erreur inattendue s'est produite. Veuillez réessayer.",
      returnToLogin: "Retour à la Connexion",
      prompt: "Entrez votre adresse e-mail et nous vous enverrons des instructions pour réinitialiser votre mot de passe.",
      emailLabel: "Adresse E-mail",
      emailPlaceholder: "Entrez votre adresse e-mail",
      cancel: "Annuler",
      sending: "Envoi en cours...",
      sendLink: "Envoyer le Lien de Réinitialisation"
    },
    zh: {
      title: "忘记密码",
      close: "关闭",
      successMessage: "密码重置说明已发送到您的电子邮件。",
      errorMessage: "发生意外错误。请重试。",
      returnToLogin: "返回登录",
      prompt: "输入您的电子邮件地址，我们将向您发送重置密码的说明。",
      emailLabel: "电子邮件地址",
      emailPlaceholder: "输入您的电子邮件地址",
      cancel: "取消",
      sending: "发送中...",
      sendLink: "发送重置链接"
    },
    ja: {
      title: "パスワードをお忘れですか",
      close: "閉じる",
      successMessage: "パスワードリセット手順がメールアドレスに送信されました。",
      errorMessage: "予期しないエラーが発生しました。もう一度お試しください。",
      returnToLogin: "ログインに戻る",
      prompt: "メールアドレスを入力してください。パスワードをリセットするための手順をお送りします。",
      emailLabel: "メールアドレス",
      emailPlaceholder: "メールアドレスを入力してください",
      cancel: "キャンセル",
      sending: "送信中...",
      sendLink: "リセットリンクを送信"
    }
  };

  const t = translations[locale as keyof typeof translations] || translations.en;
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setStatus('idle');
    setMessage('');
    
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulating success response
      setStatus('success');
      setMessage(t.successMessage);
      
      // In a real implementation, you would call an API endpoint:
      // const response = await fetch('/api/reset-password', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ email })
      // });
      // const data = await response.json();
      // if (data.success) {
      //   setStatus('success');
      //   setMessage(data.message);
      // } else {
      //   setStatus('error');
      //   setMessage(data.error || 'Something went wrong. Please try again.');
      // }
    } catch (error) {
      setStatus('error');
      setMessage(t.errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-black rounded-xl p-6 max-w-md w-full border border-white/20 backdrop-blur-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)]">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-yellow-500">{t.title}</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            aria-label={t.close}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {status === 'success' ? (
          <div className="space-y-6">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 text-white">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>{message}</span>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
            >
              {t.returnToLogin}
            </button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <p className="text-gray-300 mb-4">
              {t.prompt}
            </p>
            
            <div>
              <label className="block mb-2 font-medium text-gray-200">{t.emailLabel}</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <input
                  type="email"
                  placeholder={t.emailPlaceholder}
                  className="w-full py-3 pl-10 pr-4 rounded-xl bg-white/5 border border-white/10 focus:border-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-400/50 text-white placeholder-gray-400 transition-all duration-200"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            
            {status === 'error' && (
              <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-white">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span>{message}</span>
                </div>
              </div>
            )}
            
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200"
              >
                {t.cancel}
              </button>
              <button 
                type="submit" 
                className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none flex justify-center items-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-purple-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t.sending}
                  </>
                ) : t.sendLink}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ForgotPasswordModal; 