import React from 'react';
import { useTranslations } from 'next-intl'; // Import useTranslations

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Translation props removed, will use useTranslations hook
}

const HelpModal: React.FC<HelpModalProps> = ({
  isOpen,
  onClose,
  // Destructured translation props removed
}) => {
  const t = useTranslations('helpModal'); // Initialize hook for 'helpModal' namespace
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal Content */}
      <div className="relative z-10 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl border border-white/10 rounded-2xl p-6 md:p-8 shadow-2xl max-w-3xl w-full m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6 sticky top-0 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl py-2 -mt-2 -mx-2 px-2">
          <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
            {t('title')}
          </h3>
          <button 
            onClick={onClose}
            className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="space-y-6">
          {/* Getting Started Section */}
          <div>
            <h4 className="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-11.25a.75.75 0 00-1.5 0v4.59l-2.22-2.22a.75.75 0 10-1.06 1.06l3.5 3.5a.75.75 0 001.06 0l3.5-3.5a.75.75 0 10-1.06-1.06L10.75 11.34V6.75z" clipRule="evenodd" />
              </svg>
              {t('gettingStartedTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 text-white/90">
              <p className="mb-3">{t('gettingStartedWelcome')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80">
                <li>{t('gettingStartedLi1')}</li>
                <li>{t('gettingStartedLi2')}</li>
                <li>{t('gettingStartedLi3')}</li>
                <li>{t('gettingStartedLi4')}</li>
              </ul>
            </div>
          </div>
          
          {/* Managing Children Section */}
          <div>
            <h4 className="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
              </svg>
              {t('managingChildrenTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 text-white/90">
              <p className="mb-3">{t('managingChildrenIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80">
                <li><span className="text-yellow-300 font-medium">{t('managingChildrenEdit')}</span>{t('managingChildrenEditDesc')}</li>
                <li><span className="text-yellow-300 font-medium">{t('managingChildrenDelete')}</span>{t('managingChildrenDeleteDesc')}</li>
                <li><span className="text-yellow-300 font-medium">{t('managingChildrenBook')}</span>{t('managingChildrenBookDesc')}</li>
                <li><span className="text-yellow-300 font-medium">{t('managingChildrenAddBudget')}</span>{t('managingChildrenAddBudgetDesc')}</li>
              </ul>
            </div>
          </div>
          
          {/* Booking Process Section */}
          <div>
            <h4 className="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>
              {t('bookingProcessTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 text-white/90">
              <p className="mb-3">{t('bookingProcessIntro')}</p>
              <ol className="list-decimal list-inside space-y-2 text-white/80">
                <li>{t('bookingProcessLi1')}</li>
                <li>{t('bookingProcessLi2')}</li>
                <li>{t('bookingProcessLi3')}</li>
                <li>{t('bookingProcessLi4')}</li>
                <li>{t('bookingProcessLi5')}</li>
              </ol>
            </div>
          </div>
          
          {/* FAQ Section */}
          <div>
            <h4 className="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              {t('faqTitle')}
            </h4>
            <div className="space-y-4">
              <div className="bg-white/5 rounded-xl p-4">
                <h5 className="font-medium text-yellow-200 mb-1">{t('faq1Question')}</h5>
                <p className="text-white/80">{t('faq1Answer')}</p>
              </div>
              <div className="bg-white/5 rounded-xl p-4">
                <h5 className="font-medium text-yellow-200 mb-1">{t('faq2Question')}</h5>
                <p className="text-white/80">{t('faq2Answer')}</p>
              </div>
              <div className="bg-white/5 rounded-xl p-4">
                <h5 className="font-medium text-yellow-200 mb-1">{t('faq3Question')}</h5>
                <p className="text-white/80">{t('faq3Answer')}</p>
              </div>
            </div>
          </div>
          
          {/* Contact Support Section */}
          <div>
            <h4 className="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              {t('needMoreHelpTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 text-white/90">
              <p className="mb-3">{t('needMoreHelpIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80">
                <li>{t('needMoreHelpEmail')} <span className="text-yellow-300"><EMAIL></span></li>
                <li>{t('needMoreHelpPhone')} <span className="text-yellow-300">+****************</span></li>
                <li>{t('needMoreHelpHours')}</li>
              </ul>
              <button className="mt-4 w-full py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold rounded-lg transition-all duration-200 transform hover:scale-[1.02]">
                {t('contactSupportButton')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpModal; 