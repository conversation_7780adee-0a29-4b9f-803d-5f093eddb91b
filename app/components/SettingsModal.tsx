import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl'; // Import useTranslations
import { useRouter, usePathname } from 'next/navigation'; // Import for navigation
import { locales } from '@/lib/i18n'; // Import locales

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Translation props removed
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  // Destructured translation props removed
}) => {
  const t = useTranslations('settingsModal'); // Initialize hook
  const router = useRouter();
  const pathname = usePathname();
  
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  
  // Get current locale from path
  const getCurrentLocale = () => {
    const pathParts = pathname.split('/');
    // Check if the first part after the initial slash is a valid locale
    if (pathParts.length > 1 && locales.includes(pathParts[1] as any)) {
      return pathParts[1];
    }
    return 'en'; // Default
  };
  
  const [language, setLanguage] = useState(getCurrentLocale());
  const [currency, setCurrency] = useState('usd');
  const [timezone, setTimezone] = useState('Europe/London');
  
  // Update language state if pathname changes
  useEffect(() => {
    setLanguage(getCurrentLocale());
  }, [pathname]);
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal Content */}
      <div className="relative z-10 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl border border-white/10 rounded-2xl p-6 md:p-8 shadow-2xl max-w-2xl w-full m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6 sticky top-0 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl py-2 -mt-2 -mx-2 px-2">
          <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
            {t('title')}
          </h3>
          <button 
            onClick={onClose}
            className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="space-y-8">
          {/* Profile Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              {t('profileSectionTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <div className="flex items-center space-x-4 mb-4">
                <div className="relative">
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-yellow-300 to-yellow-500 flex items-center justify-center text-indigo-900 text-2xl font-bold">
                    JD
                  </div>
                  <button className="absolute -bottom-1 -right-1 bg-yellow-400 rounded-full p-1 text-indigo-900 hover:bg-yellow-300 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                </div>
                
                <div>
                  {/* TODO: Replace hardcoded profile info with actual user data */}
                  <h5 className="text-white font-medium">{t('profileNamePlaceholder')}</h5> {/* Note: Still a placeholder */}
                  <p className="text-white/60 text-sm">{t('profileEmailPlaceholder')}</p> {/* Note: Still a placeholder */}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button className="w-full py-2 px-4 bg-white/10 hover:bg-white/15 transition-colors rounded-lg text-white/90 font-medium text-sm flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                  {t('editProfileButton')}
                </button>
                <button className="w-full py-2 px-4 bg-white/10 hover:bg-white/15 transition-colors rounded-lg text-white/90 font-medium text-sm flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                  </svg>
                  {t('changePasswordButton')}
                </button>
              </div>
            </div>
          </div>
          
          {/* Appearance Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" />
              </svg>
              {t('appearanceSectionTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('darkModeLabel')}</span>
                <button 
                  onClick={() => setDarkMode(!darkMode)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900 ${darkMode ? 'bg-yellow-400' : 'bg-gray-700'}`}
                >
                  <span 
                    className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${darkMode ? 'translate-x-6' : 'translate-x-1'}`} 
                  />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('highContrastLabel')}</span>
                <button 
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900"
                >
                  <span className="inline-block h-5 w-5 transform rounded-full bg-white transition-transform translate-x-1" />
                </button>
              </div>
              
              <div>
                <label className="block text-white/90 mb-2">{t('fontSizeLabel')}</label>
                <input 
                  type="range" 
                  min="1" 
                  max="5" 
                  defaultValue="3"
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-yellow-400"
                />
                <div className="flex justify-between text-xs text-white/60 mt-1">
                  <span>A</span>
                  <span>A</span>
                  <span>A</span>
                  <span>A</span>
                  <span>A</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Notifications Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
              </svg>
              {t('notificationsSectionTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('enableNotificationsLabel')}</span>
                <button 
                  onClick={() => setNotifications(!notifications)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900 ${notifications ? 'bg-yellow-400' : 'bg-gray-700'}`}
                >
                  <span 
                    className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${notifications ? 'translate-x-6' : 'translate-x-1'}`} 
                  />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('bookingRemindersLabel')}</span>
                <button 
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-yellow-400 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900"
                >
                  <span className="inline-block h-5 w-5 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('lowBudgetAlertsLabel')}</span>
                <button 
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-yellow-400 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900"
                >
                  <span className="inline-block h-5 w-5 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white/90">{t('marketingUpdatesLabel')}</span>
                <button 
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 focus:ring-offset-blue-900"
                >
                  <span className="inline-block h-5 w-5 transform rounded-full bg-white transition-transform translate-x-1" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Preferences Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              {t('preferencesSectionTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4 space-y-4">
              <div>
                <label className="block text-white/90 mb-2">{t('languageLabel')}</label>
                <select 
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full bg-white/10 text-white border border-white/20 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="en">{t('langEnglish')}</option>
                  <option value="es">{t('langSpanish')}</option>
                  <option value="de">{t('langGerman')}</option>
                  <option value="fr">{t('langFrench')}</option>
                  <option value="zh">{t('langChinese')}</option>
                  <option value="ja">{t('langJapanese')}</option>
                  <option value="hi">{t('langHindi')}</option>
                  <option value="ar">{t('langArabic')}</option>
                  <option value="he">{t('langHebrew')}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-white/90 mb-2">{t('currencyLabel')}</label>
                <select 
                  value={currency}
                  onChange={(e) => setCurrency(e.target.value)}
                  className="w-full bg-white/10 text-white border border-white/20 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="usd">{t('currencyUSD')}</option>
                  <option value="eur">{t('currencyEUR')}</option>
                  <option value="gbp">{t('currencyGBP')}</option>
                  <option value="jpy">{t('currencyJPY')}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-white/90 mb-2">{t('timezoneLabel')}</label>
                <select 
                  value={timezone}
                  onChange={(e) => setTimezone(e.target.value)}
                  className="w-full bg-white/10 text-white border border-white/20 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="Europe/London">{t('tzLondon')}</option>
                  <option value="Europe/Paris">{t('tzParis')}</option>
                  <option value="America/New_York">{t('tzNewYork')}</option>
                  <option value="America/Los_Angeles">{t('tzLosAngeles')}</option>
                  <option value="Asia/Tokyo">{t('tzTokyo')}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-2">
            <button 
              onClick={onClose}
              className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/15 text-white transition-colors"
            >
              {t('cancelButton')}
            </button>
            <button 
              onClick={() => {
                // Handle language change if it's different from current
                const currentLocale = getCurrentLocale();
                if (language !== currentLocale) {
                  // Create a new URL path with the selected language
                  const pathWithoutLocale = pathname.split('/').slice(2).join('/');
                  const newPath = `/${language}/${pathWithoutLocale}`;
                  
                  // Navigate to the new URL
                  router.push(newPath);
                }
                // Close the modal
                onClose();
              }}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-medium transition-all duration-200 transform hover:scale-[1.02]"
            >
              {t('saveButton')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal; 