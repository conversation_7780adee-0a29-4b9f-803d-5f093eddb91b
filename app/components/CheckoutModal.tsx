'use client';

import React, { useState } from 'react';
import { Product } from '../../types/products';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  cart: { id: number; name: string; price: number; quantity: number; type: string }[];
  onCheckout: () => void;
  isProcessing: boolean;
  locale?: string; // Add locale prop
}

const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onClose,
  cart,
  onCheckout,
  isProcessing,
  locale = 'en' // Default locale
}) => {
  if (!isOpen) return null;

  const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
  const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  const tax = subtotal * 0.19; // 19% VAT
  const total = subtotal + tax;

  // Translations object
  const translations = {
    en: {
      title: 'Checkout',
      orderSummary: 'Order Summary',
      quantity: 'Qty:',
      subtotal: 'Subtotal', // Add subtotal
      item: 'item',
      items: 'items',
      vat: 'VAT (19%)',
      total: 'Total',
      terms: 'By completing this purchase, you agree to our terms and conditions. Payment will be processed securely.',
      processing: 'Processing...',
      completePurchase: 'Complete Purchase'
    },
    de: {
      title: 'Zur Kasse',
      orderSummary: 'Bestellübersicht',
      quantity: 'Menge:',
      subtotal: 'Zwischensumme', // Add subtotal
      item: 'Artikel',
      items: 'Artikel',
      vat: 'MwSt. (19%)',
      total: 'Gesamt',
      terms: 'Mit Abschluss dieses Kaufs stimmen Sie unseren Allgemeinen Geschäftsbedingungen zu. Die Zahlung wird sicher verarbeitet.',
      processing: 'Wird bearbeitet...',
      completePurchase: 'Kauf abschließen'
    },
    es: {
      title: 'Pagar',
      orderSummary: 'Resumen del Pedido',
      quantity: 'Cant:',
      subtotal: 'Subtotal', // Add subtotal
      item: 'artículo',
      items: 'artículos',
      vat: 'IVA (19%)',
      total: 'Total',
      terms: 'Al completar esta compra, aceptas nuestros términos y condiciones. El pago se procesará de forma segura.',
      processing: 'Procesando...',
      completePurchase: 'Completar Compra'
    },
    fr: {
      title: 'Paiement',
      orderSummary: 'Résumé de la Commande',
      quantity: 'Qté :',
      subtotal: 'Sous-total',
      item: 'article',
      items: 'articles',
      vat: 'TVA (19%)',
      total: 'Total',
      terms: 'En finalisant cet achat, vous acceptez nos termes et conditions. Le paiement sera traité de manière sécurisée.',
      processing: 'Traitement...',
      completePurchase: 'Finaliser l\'Achat'
    },
    zh: {
      title: '结账',
      orderSummary: '订单摘要',
      quantity: '数量：',
      subtotal: '小计',
      item: '件商品',
      items: '件商品',
      vat: '增值税 (19%)',
      total: '总计',
      terms: '完成此购买即表示您同意我们的条款和条件。付款将安全处理。',
      processing: '处理中...',
      completePurchase: '完成购买'
    },
    ja: {
      title: 'チェックアウト',
      orderSummary: '注文概要',
      quantity: '数量：',
      subtotal: '小計',
      item: '点',
      items: '点',
      vat: '付加価値税 (19%)',
      total: '合計',
      terms: 'この購入を完了することにより、利用規約に同意したことになります。支払いは安全に処理されます。',
      processing: '処理中...',
      completePurchase: '購入を完了する'
    },
    ar: {
      title: 'الدفع',
      orderSummary: 'ملخص الطلب',
      quantity: 'الكمية:',
      subtotal: 'المجموع الفرعي',
      item: 'عنصر',
      items: 'عناصر',
      vat: 'ضريبة القيمة المضافة (19%)',
      total: 'المجموع',
      terms: 'بإتمام عملية الشراء هذه، فإنك توافق على الشروط والأحكام الخاصة بنا. سيتم معالجة الدفع بشكل آمن.',
      processing: 'جاري المعالجة...',
      completePurchase: 'إتمام الشراء'
    },
    he: {
      title: 'תשלום',
      orderSummary: 'סיכום הזמנה',
      quantity: 'כמות:',
      subtotal: 'סיכום ביניים',
      item: 'פריט',
      items: 'פריטים',
      vat: 'מע"מ (19%)',
      total: 'סה"כ',
      terms: 'על ידי השלמת רכישה זו, אתה מסכים לתנאים ולהגבלות שלנו. התשלום יעובד באופן מאובטח.',
      processing: 'מעבד...',
      completePurchase: 'השלם רכישה'
    },
    pt: {
      title: 'Checkout',
      orderSummary: 'Resumo do Pedido',
      quantity: 'Qtd:',
      subtotal: 'Subtotal',
      item: 'item',
      items: 'itens',
      vat: 'IVA (19%)',
      total: 'Total',
      terms: 'Ao concluir esta compra, você concorda com nossos termos e condições. O pagamento será processado com segurança.',
      processing: 'Processando...',
      completePurchase: 'Concluir Compra'
    }
  };

  const t = translations[locale as keyof typeof translations] || translations.en;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden transform transition-all">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 text-white">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-bold">{t.title}</h3>
            <button 
              onClick={onClose}
              className="text-white hover:text-yellow-300 transition-colors"
              disabled={isProcessing}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Body */}
        <div className="p-6 text-gray-800">
          <h4 className="font-bold text-lg mb-4">{t.orderSummary}</h4>
          
          {/* Cart Items */}
          <div className="max-h-60 overflow-y-auto mb-4">
            {cart.map((item) => (
              <div key={item.id} className="flex justify-between items-center py-2 border-b border-gray-200">
                <div>
                  <p className="font-medium">{item.name}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-gray-500">{t.quantity} {item.quantity}</p>
                    {item.type && (
                      <span className={`text-xs px-1.5 py-0.5 rounded ${
                        item.type === 'license' 
                          ? 'bg-blue-100 text-blue-800' 
                          : item.type === 'physical'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                      }`}>
                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                      </span>
                    )}
                  </div>
                </div>
                <p className="font-medium">€{(item.price * item.quantity).toFixed(2)}</p>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="space-y-2 mb-6">
            <div className="flex justify-between">
              <p>{t.subtotal} ({totalItems} {totalItems === 1 ? t.item : t.items})</p>
              <p>€{subtotal.toFixed(2)}</p>
            </div>
            <div className="flex justify-between">
              <p>{t.vat}</p>
              <p>€{tax.toFixed(2)}</p>
            </div>
            <div className="flex justify-between font-bold text-lg pt-2 border-t border-gray-200">
              <p>{t.total}</p>
              <p>€{total.toFixed(2)}</p>
            </div>
          </div>

          {/* Payment Info */}
          <div className="bg-gray-100 p-3 rounded-lg mb-6">
            <p className="text-sm text-gray-600">
              {t.terms}
            </p>
          </div>

          {/* Checkout Button */}
          <button
            onClick={onCheckout}
            disabled={isProcessing}
            className={`w-full ${
              isProcessing 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700'
            } text-white font-bold py-3 px-4 rounded-lg transition-colors flex items-center justify-center`}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                {t.processing}
              </>
            ) : (
              t.completePurchase
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CheckoutModal;
