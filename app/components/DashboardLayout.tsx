"use client"; // Layout components often need client-side interactivity (like router)

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useParams, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl'; // Import useTranslations
import { logout } from '../../utils/auth';
import { locales, Locale } from '../../lib/i18n';
import HelpModal from './HelpModal';
import SettingsModal from './SettingsModal';
import PrivacyModal from './PrivacyModal';
import ImprintModal from './ImprintModal';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const router = useRouter(); // Initialize router
  const params = useParams();
  const pathname = usePathname();
  const currentLocale = (params.locale as Locale) || 'en';
  // Get translations using next-intl hooks for different namespaces
  const tDashboard = useTranslations('dashboardLayout');
  const tHelp = useTranslations('helpModal');
  const tSettings = useTranslations('settingsModal');
  const tPrivacy = useTranslations('privacyModal');
  const tImprint = useTranslations('imprintModal');
  const tLanguages = useTranslations('languages');
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isPrivacyModalOpen, setIsPrivacyModalOpen] = useState(false);
  const [isImprintModalOpen, setIsImprintModalOpen] = useState(false);
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false); // State for language dropdown
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false); // State for mobile menu

  // Close mobile menu when window resizes to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) { // md breakpoint
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Function to get the path without the locale prefix
  const getPathWithoutLocale = (path: string, locale: string): string => {
    const segments = path.split('/');
    // Check if the second segment matches the locale
    if (segments.length > 1 && segments[1] === locale) {
      // Join the rest of the segments, handle root path case
      return segments.slice(2).join('/') || '';
    }
    // Fallback if the path doesn't start with /<locale>/
    return path.startsWith('/') ? path.substring(1) : path;
  };

  const basePath = getPathWithoutLocale(pathname, currentLocale);

  const toggleHelpModal = () => {
    setIsHelpModalOpen(!isHelpModalOpen);
  };

  const toggleSettingsModal = () => {
    setIsSettingsModalOpen(!isSettingsModalOpen);
  };

  const togglePrivacyModal = () => {
    setIsPrivacyModalOpen(!isPrivacyModalOpen);
  };

  const toggleImprintModal = () => {
    setIsImprintModalOpen(!isImprintModalOpen);
  };

  const toggleLangDropdown = () => setIsLangDropdownOpen(!isLangDropdownOpen); // Toggle function

  return (
    <div className="relative min-h-screen text-white">
      {/* Gradient background overlay with animation */}
      <div className="fixed inset-0 bg-gradient-to-br from-indigo-950 via-blue-950 to-slate-900 opacity-80 z-10 animate-gradientShift"></div>

      {/* Animated particles overlay - Temporarily Commented Out for Blur Debugging */}
      {/* <div className="fixed inset-0 z-5 opacity-30">
        <div className="absolute w-2 h-2 bg-blue-400 rounded-full top-1/4 left-1/3 animate-float"></div>
        <div className="absolute w-3 h-3 bg-purple-400 rounded-full top-3/4 left-1/4 animate-floatSlow"></div>
        <div className="absolute w-2 h-2 bg-indigo-400 rounded-full top-1/2 left-2/3 animate-floatMedium"></div>
        <div className="absolute w-1 h-1 bg-yellow-400 rounded-full top-1/3 left-3/4 animate-float"></div>
        <div className="absolute w-3 h-3 bg-cyan-400 rounded-full top-2/3 left-1/5 animate-floatSlow"></div>
      </div> */}

      {/* Background image with improved styling */}
      <div
        className="fixed inset-0 bg-cover bg-center z-0 filter contrast-125 brightness-50"
        style={{ backgroundImage: "url('/studio.jpg')" }}
      ></div>

      {/* Content Layer */}
      <div className="relative z-30 flex flex-col min-h-screen">
        {/* Enhanced Modern Navbar */}
        <nav className="bg-black/40 backdrop-blur-xl border-b border-white/20 fixed top-0 w-full z-50 shadow-xl">
          <div className="w-full px-[54px]">
            <div className="flex justify-between items-center h-16">
              {/* Logo & Title */}
              <Link href={`/${currentLocale}/dashboard`} className="flex items-center space-x-3 group">
                <Image
                  src="/globe.svg"
                  alt="Globe Icon"
                  width={30}
                  height={30}
                  className="filter brightness-0 invert group-hover:opacity-80 transition-opacity"
                />
                <span className="text-lg md:text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-500 group-hover:from-amber-400 group-hover:to-yellow-600 transition-all">
                  Booking Parents
                </span>
              </Link>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-6">
                <Link href={`/${currentLocale}/dashboard`} className="text-white/80 hover:text-yellow-300 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-yellow-300">
                  {tDashboard('navDashboard')}
                </Link>
                <Link href={`/${currentLocale}/learning-documentation`} className="text-white/80 hover:text-yellow-300 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-yellow-300">
                  {tDashboard('navLearningProgress')}
                </Link>
                <Link href={`/${currentLocale}/shop`} className="text-white/80 hover:text-yellow-300 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-yellow-300">
                  {tDashboard('navShop')}
                </Link>
                <Link href={`/${currentLocale}/purchases`} className="text-white/80 hover:text-yellow-300 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-yellow-300">
                  {tDashboard('navPurchases')}
                </Link>
                <Link href={`/${currentLocale}/learning-studios`} className="text-white/80 hover:text-yellow-300 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-yellow-300">
                  {tDashboard('navStudios')}
                </Link>

                {/* Help, Settings, and Privacy buttons - Removed grouping div and ml-2 */}
                  <button
                    onClick={toggleHelpModal}
                    className="text-white/60 hover:text-yellow-300 transition-colors p-1 rounded-full hover:bg-white/10"
                    aria-label="Help"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                  </button>
                  <button
                    onClick={toggleSettingsModal}
                    className="text-white/60 hover:text-yellow-300 transition-colors p-1 rounded-full hover:bg-white/10"
                    aria-label="Settings"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                    </svg>
                  </button>
                {/* End of ungrouped Help/Settings buttons */}

                {/* Language Switcher */}
                {/* Language Switcher - Click to open */}
                <div className="relative"> {/* Removed group class and ml-4 */}
                  <button
                    onClick={toggleLangDropdown} // Added onClick handler
                    className="text-white/60 hover:text-yellow-300 transition-colors p-1 rounded-full hover:bg-white/10 flex items-center space-x-1" // Added flex/space for icon+arrow
                    aria-label="Change Language"
                  >
                    {/* Globe Icon */}
                    <svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" className="h-5 w-5">
                      <g clipPath="url(#a)">
                        <path fillRule="evenodd" clipRule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="currentColor"/>
                      </g>
                      {/* <defs><clipPath id="a"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs> */}
                    </svg>
                     {/* Dropdown Arrow */}
                     <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M19 9l-7 7-7-7" />
                     </svg>
                  </button>
                  {/* Conditionally apply scale based on state */}
                  <div className={`absolute right-0 mt-2 w-36 bg-gray-900/80 backdrop-blur-md border border-white/20 rounded-md shadow-lg overflow-hidden z-50 transform ${isLangDropdownOpen ? 'scale-100' : 'scale-0'} transition-transform origin-top-right duration-150`}>
                    {locales.map((locale) => (
                      <Link
                        key={locale}
                        href={`/${locale}/${basePath}`}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-white/10 transition-colors ${
                          locale === currentLocale ? 'text-yellow-400 font-semibold' : 'text-white/90'
                        }`}
                      >
                        {tLanguages(locale)} {/* Use translated name */}
                      </Link>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => logout(router)}
                  className="px-4 py-2 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-medium transition-all duration-200 transform hover:scale-105 flex items-center" // Removed ml-4
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V9.5a.5.5 0 011 0V16a2 2 0 01-2 2H3a2 2 0 01-2-2V4a2 2 0 012-2h9.5a.5.5 0 010 1H3zm14.354.646a.5.5 0 010 .708l-7.5 7.5a.5.5 0 01-.708-.708l7.5-7.5a.5.5 0 01.708 0zm-.5 2.5a.5.5 0 01.5.5v3a.5.5 0 01-1 0V7.5a1.5 1.5 0 00-1.5-1.5H7.5a.5.5 0 010-1H12a2.5 2.5 0 012.5 2.5v.646a.5.5 0 01-.146.354l-7.5 7.5a.5.5 0 01-.708-.708l7.5-7.5A.5.5 0 0116.5 7.5V6.5a.5.5 0 01.354-.146z" clipRule="evenodd" />
                  </svg>
                  {tDashboard('navLogout')}
                </button>
              </div>

              {/* Mobile Menu Button */}
              <div className="md:hidden">
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="text-white p-2 rounded-md hover:bg-white/10 transition-colors"
                  aria-label={tDashboard('toggleMenu')}
                >
                  {isMobileMenuOpen ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Mobile Navigation Menu - Full overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed left-0 right-0 top-16 bottom-0 w-screen bg-black/95 text-white z-[100] md:hidden overflow-y-auto"
            data-menu-open={isMobileMenuOpen}
          >
            <div className="flex flex-col space-y-4 p-6 pt-8 max-w-screen-xl mx-auto">
              <Link
                href={`/${currentLocale}/dashboard`}
                className="text-white/80 hover:text-yellow-300 py-2 transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
                {tDashboard('navDashboard')}
              </Link>
              <Link
                href={`/${currentLocale}/learning-documentation`}
                className="text-white/80 hover:text-yellow-300 py-2 transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                </svg>
                {tDashboard('navLearningProgress')}
              </Link>
              <Link
                href={`/${currentLocale}/shop`}
                className="text-white/80 hover:text-yellow-300 py-2 transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                </svg>
                {tDashboard('navShop')}
              </Link>
              <Link
                href={`/${currentLocale}/purchases`}
                className="text-white/80 hover:text-yellow-300 py-2 transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clipRule="evenodd" />
                </svg>
                {tDashboard('navPurchases')}
              </Link>
              <Link
                href={`/${currentLocale}/learning-studios`}
                className="text-white/80 hover:text-yellow-300 py-2 transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                </svg>
                {tDashboard('navStudios')}
              </Link>

              {/* Language Selector for Mobile Menu */}
              <div className="mt-8 pt-6 border-t border-gray-700">
                <h3 className="text-sm font-medium mb-3 flex items-center gap-2 text-white">
                  <svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" className="h-4 w-4">
                    <g clipPath="url(#a)">
                      <path fillRule="evenodd" clipRule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="currentColor"/>
                    </g>
                  </svg>
                  {tDashboard('selectLanguage')}
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {locales.map((locale) => (
                    <Link
                      key={locale}
                      href={`/${locale}/${basePath}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`py-2 px-3 text-sm rounded-md text-left transition-colors ${
                        locale === currentLocale
                          ? "bg-yellow-400/20 text-yellow-300 font-medium border border-yellow-400/30"
                          : "hover:bg-white/10 text-white/80 hover:text-white"
                      }`}
                    >
                      {tLanguages(locale)}
                    </Link>
                  ))}
                </div>
              </div>

              {/* Help, Settings, Privacy buttons */}
              <div className="mt-4 pt-4 border-t border-gray-700 grid grid-cols-3 gap-2">
                <button
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    toggleHelpModal();
                  }}
                  className="flex flex-col items-center justify-center p-3 rounded-md bg-white/10 hover:bg-white/20 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mb-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                  </svg>
                  <span className="text-xs">{tDashboard('help')}</span>
                </button>
                <button
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    toggleSettingsModal();
                  }}
                  className="flex flex-col items-center justify-center p-3 rounded-md bg-white/10 hover:bg-white/20 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mb-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                  <span className="text-xs">{tDashboard('settings')}</span>
                </button>
                <button
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    togglePrivacyModal();
                  }}
                  className="flex flex-col items-center justify-center p-3 rounded-md bg-white/10 hover:bg-white/20 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mb-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-xs">{tDashboard('privacy')}</span>
                </button>
              </div>

              {/* Logout Button */}
              <button
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  logout(router);
                }}
                className="w-full py-2 mt-4 text-center rounded-md bg-red-600/20 text-red-300 border border-red-500/30 hover:bg-red-600/30 transition-colors flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V9.5a.5.5 0 011 0V16a2 2 0 01-2 2H3a2 2 0 01-2-2V4a2 2 0 012-2h9.5a.5.5 0 010 1H3zm14.354.646a.5.5 0 010 .708l-7.5 7.5a.5.5 0 01-.708-.708l7.5-7.5a.5.5 0 01.708 0zm-.5 2.5a.5.5 0 01.5.5v3a.5.5 0 01-1 0V7.5a1.5 1.5 0 00-1.5-1.5H7.5a.5.5 0 010-1H12a2.5 2.5 0 012.5 2.5v.646a.5.5 0 01-.146.354l-7.5 7.5a.5.5 0 01-.708-.708l7.5-7.5A.5.5 0 0116.5 7.5V6.5a.5.5 0 01.354-.146z" clipRule="evenodd" />
                </svg>
                {tDashboard('navLogout')}
              </button>
            </div>
          </div>
        )}

        {/* Page Content */}
        <main className="w-full px-[54px] py-8 pt-24 flex-grow">
          {children}
        </main>

        {/* Enhanced Footer */}
        <footer className="bg-black/40 backdrop-blur-md border-t border-white/20 py-8 mt-auto">
          <div className="w-full px-[54px]">
            <div className="flex flex-wrap justify-center items-center gap-x-4 gap-y-2 text-sm">
              <span className="text-white/60">
                {tDashboard('footerCopyright', { year: new Date().getFullYear().toString() })}
              </span>
              <div className="flex items-center gap-x-4">
                <button
                  onClick={toggleImprintModal}
                  className="text-yellow-400 hover:text-yellow-300 transition-colors"
                >
                  {tDashboard('footerImprint')}
                </button>
                <button
                  onClick={togglePrivacyModal}
                  className="text-yellow-400 hover:text-yellow-300 transition-colors"
                >
                  {tDashboard('footerPrivacy')}
                </button>
              </div>
            </div>
          </div>
        </footer>

        {/* Help Modal - Pass translations */}
        <HelpModal
          isOpen={isHelpModalOpen}
          onClose={() => setIsHelpModalOpen(false)}
          // Translations will be handled internally by HelpModal using useTranslations
        />

        {/* Settings Modal */}
        <SettingsModal
          isOpen={isSettingsModalOpen}
          onClose={() => setIsSettingsModalOpen(false)}
          // Translations will be handled internally by SettingsModal using useTranslations
        />

        {/* Privacy Modal - Pass translations */}
        <PrivacyModal
          isOpen={isPrivacyModalOpen}
          onClose={() => setIsPrivacyModalOpen(false)}
          // Translations will be handled internally by PrivacyModal using useTranslations
        />

        {/* Imprint Modal - Pass translations */}
        <ImprintModal
          isOpen={isImprintModalOpen}
          onClose={() => setIsImprintModalOpen(false)}
          // Translations will be handled internally by ImprintModal using useTranslations
        />
      </div>
    </div>
  );
};

export default DashboardLayout;
