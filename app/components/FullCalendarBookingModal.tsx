"use client";
import React, { useRef, useEffect, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid/index.js';
import timeGridPlugin from '@fullcalendar/timegrid/index.js';
import interactionPlugin from '@fullcalendar/interaction/index.js'; // for selectable, draggable, resizable
import { DatesSetArg, EventInput } from '@fullcalendar/core/index.js';
import { getBrowserTimezone } from '../../utils/timezone';
import allLocales from '@fullcalendar/core/locales-all';
import { getTimeFormatConfig, getFirstDayOfWeek, formatPrice } from '../../utils/calendarFormatUtils';
import { API_URL } from '../../utils/apiConfig';
import { Room } from '../../types/learning-studios';
import BookingConfirmationModal from './BookingConfirmationModal';
import DeleteBookingModal from './DeleteBookingModal';
import { useTranslations } from 'next-intl';

interface Studio {
  id: number;
  name: string;
}

interface OpeningHour {
  id: number;
  studio_id: number;
  day_of_week: string; // 'Monday', 'Tuesday', etc.
  morning_open: string; // HH:MM:SS format
  morning_close: string; // HH:MM:SS format
  afternoon_open: string | null; // HH:MM:SS format or null
  afternoon_close: string | null; // HH:MM:SS format or null
}

interface BusinessHour {
  daysOfWeek: number[];
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

// Helper function to check if a time slot is within studio opening hours
const isTimeSlotWithinOpeningHours = (
  startTime: Date,
  endTime: Date,
  businessHours: BusinessHour[]
): boolean => {
  // If no business hours are defined, return false
  if (!businessHours || businessHours.length === 0) {
    return false;
  }

  // Get the day of the week for the start and end times (0 = Sunday, 1 = Monday, etc.)
  const startDay = startTime.getDay();
  const endDay = endTime.getDay();

  // If the booking spans multiple days, we need to check each day
  if (startDay !== endDay) {
    // For simplicity, we'll just check if both the start and end times are within business hours
    // A more complex implementation would check each day in between as well
    return (
      isTimeWithinBusinessHours(startTime, businessHours) &&
      isTimeWithinBusinessHours(endTime, businessHours)
    );
  }

  // For same-day bookings, check if the entire time slot is within business hours
  return isTimeWithinBusinessHours(startTime, businessHours) &&
         isTimeWithinBusinessHours(endTime, businessHours);
};

// Helper function to check if a specific time is within business hours
const isTimeWithinBusinessHours = (time: Date, businessHours: BusinessHour[]): boolean => {
  const day = time.getDay();
  const hours = time.getHours();
  const minutes = time.getMinutes();

  // Format the time as HH:MM for comparison with business hours
  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

  // Check if the time falls within any of the business hour ranges for this day
  return businessHours.some(hours => {
    // Check if this business hour applies to the current day
    if (!hours.daysOfWeek.includes(day)) {
      return false;
    }

    // Check if the time is within the business hour range
    return timeString >= hours.startTime && timeString <= hours.endTime;
  });
};

interface FullCalendarBookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  childName: string;
  childId: number; // Added childId prop
  parentId: string; // Added parentId prop
  preferredStudioId: number | null;
  studios: Studio[];
  // Translation Props
  locale: string; // e.g., 'en', 'de'
  titleText: string; // e.g., "Book Session for {0}"
  promptEventTitleText?: string; // e.g., "Enter a title for your event"
  alertEventAddedText?: string; // e.g., "Event {0} was added"
  alertSelectionCancelledText?: string; // e.g., "Selection cancelled"
  confirmViewDetailsText: string; // e.g., "View details for \"{0}\"? \nDescription: {1}"
  descriptionNoneText: string; // e.g., "None"
  alertEventMovedText: string; // e.g., "{0} was moved to {1}"
  alertEventResizedText: string; // e.g., "{0} was resized to end at {1}"
  // Studio Selection Props
  studioSelectorTitle?: string; // e.g., "Studio to book"
  selectStudioPlaceholder?: string; // e.g., "-- Select a Studio --"
  // Room Selection Props
  roomSelectorTitle?: string; // e.g., "Room"
  selectRoomPlaceholder?: string; // e.g., "-- Select a Room --"
}

const FullCalendarBookingModal: React.FC<FullCalendarBookingModalProps> = ({
  isOpen,
  onClose,
  childName,
  childId,
  parentId,
  preferredStudioId,
  studios,
  // Translation Props
  locale,
  titleText,
  promptEventTitleText,
  alertEventAddedText,
  alertSelectionCancelledText,
  confirmViewDetailsText,
  descriptionNoneText,
  alertEventMovedText,
  alertEventResizedText,
  // Studio Selection Props
  studioSelectorTitle,
  selectStudioPlaceholder = "-- Select a Studio --",
  // Room Selection Props
  roomSelectorTitle,
  selectRoomPlaceholder = "-- Select a Room --",
}) => {
  // Get translations for success messages
  const bookingConfirmationT = useTranslations('dashboardPage.bookingConfirmation');
  const calendarRef = useRef<FullCalendar>(null);
  const [selectedStudioId, setSelectedStudioId] = useState<number | null>(preferredStudioId);
  const [, setOpeningHours] = useState<OpeningHour[]>([]);
  const [businessHours, setBusinessHours] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<number | null>(null);

  // Booking confirmation state
  const [showBookingConfirmation, setShowBookingConfirmation] = useState<boolean>(false);
  const [bookingDetails, setBookingDetails] = useState<{
    startTime: string;
    endTime: string;
    availabilityInfo?: string;
  } | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState<boolean>(false);
  // Track whether the success is for a booking creation or deletion
  const [isDeleteSuccess, setIsDeleteSuccess] = useState<boolean>(false);

  // Delete booking state
  const [showDeleteBookingModal, setShowDeleteBookingModal] = useState<boolean>(false);
  const [deleteBookingDetails, setDeleteBookingDetails] = useState<{
    bookingId: number;
    startTime: string;
    endTime: string;
  } | null>(null);

  // State for storing bookings
  const [bookings, setBookings] = useState<any[]>([]);
  const [calendarEvents, setCalendarEvents] = useState<EventInput[]>([]);

  // Function to fetch opening hours for a studio
  const fetchOpeningHours = async (studioId: number) => {
    if (!studioId) return;

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      const timezone = getBrowserTimezone();
      const response = await fetch(`${API_URL}/opening-hours/studio/${studioId}?timezone=${timezone}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch studio hours: ${response.status}`);
      }

      const hoursData = await response.json();
      setOpeningHours(hoursData);

      // Transform opening hours into FullCalendar business hours format
      // Map day names to FullCalendar day numbers (0=Sunday, 1=Monday, etc.)
      const dayMap: Record<string, number> = {
        'Sunday': 0,
        'Monday': 1,
        'Tuesday': 2,
        'Wednesday': 3,
        'Thursday': 4,
        'Friday': 5,
        'Saturday': 6
      };

      const formattedHours = hoursData.flatMap((hour: OpeningHour) => {
        const dayNumber = dayMap[hour.day_of_week];
        if (dayNumber === undefined) return [];

        const hours = [];

        // Morning hours
        if (hour.morning_open && hour.morning_close) {
          hours.push({
            daysOfWeek: [dayNumber],
            startTime: hour.morning_open.substring(0, 5), // HH:MM format
            endTime: hour.morning_close.substring(0, 5), // HH:MM format
          });
        }

        // Afternoon hours (if available)
        if (hour.afternoon_open && hour.afternoon_close) {
          hours.push({
            daysOfWeek: [dayNumber],
            startTime: hour.afternoon_open.substring(0, 5), // HH:MM format
            endTime: hour.afternoon_close.substring(0, 5), // HH:MM format
          });
        }

        return hours;
      });

      setBusinessHours(formattedHours);
    } catch (err: any) {
      console.error('Error fetching opening hours:', err);
      setError(err.message || 'Failed to fetch studio hours');
    } finally {
      setLoading(false);
    }
  };

  // Update selectedStudioId when preferredStudioId changes
  useEffect(() => {
    setSelectedStudioId(preferredStudioId);
  }, [preferredStudioId]);

  // Function to fetch rooms for a studio
  const fetchRooms = async (studioId: number) => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      const response = await fetch(`${API_URL}/rooms/studio/${studioId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch rooms: ${response.status}`);
      }

      const roomsData = await response.json();
      setRooms(roomsData);

      // Select the first room by default if available
      if (roomsData.length > 0) {
        setSelectedRoomId(roomsData[0].id);
      } else {
        setSelectedRoomId(null);
      }
    } catch (err: any) {
      console.error('Error fetching rooms:', err);
      setError(err.message || 'Failed to fetch rooms');
    } finally {
      setLoading(false);
    }
  };

  // Fetch opening hours when selectedStudioId changes
  useEffect(() => {
    if (selectedStudioId) {
      fetchOpeningHours(selectedStudioId);
      fetchRooms(selectedStudioId);
    } else {
      // Clear data if no studio is selected
      setBusinessHours([]);
      setOpeningHours([]);
      setRooms([]);
      setSelectedRoomId(null);
    }
  }, [selectedStudioId]);

  // Fetch bookings when selectedRoomId or calendar date range changes
  useEffect(() => {
    if (selectedRoomId && calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      const startDate = calendarApi.view.activeStart.toISOString();
      const endDate = calendarApi.view.activeEnd.toISOString();
      fetchBookings(selectedRoomId, startDate, endDate);
    } else {
      // Clear events if no room is selected
      setCalendarEvents([]);
    }
  }, [selectedRoomId]);

  useEffect(() => {
    // Ensure calendar resizes correctly if the modal transition affects layout
    if (isOpen && calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      // Delay resize slightly to allow modal animation to complete
      setTimeout(() => {
        calendarApi.updateSize();
      }, 300); // Adjust timing based on modal animation duration
    }
  }, [isOpen]);

  // Function to format date range for display (for future use)
  // const formatDateRange = (view: string, start: Date, end: Date): string | null => {
  //   const startDate = new Date(start);
  //   const endDate = new Date(end);
  //
  //   if (view === 'timeGridWeek') {
  //     return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  //   }
  //   return null;
  // };

  // Function to fetch bookings for a room in a date range
  const fetchBookings = async (roomId: number, startDate: string, endDate: string) => {

    if (!roomId) return;

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      const timezone = getBrowserTimezone();
      const bookingsUrl = `${API_URL}/bookings/date-range?start_date=${startDate}&end_date=${endDate}&timezone=${timezone}&response_timezone=${timezone}&room_id=${roomId}`;

      const response = await fetch(bookingsUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch bookings: ${response.status}`);
      }

      const bookingsData = await response.json();
      // Ensure bookingsData is always an array, even if the API returns null or undefined
      const bookingsArray = Array.isArray(bookingsData) ? bookingsData : [];

      setBookings(bookingsArray);

      // Get the selected room to check max capacity
      const selectedRoom = rooms.find(r => r.id === roomId);
      if (!selectedRoom) {
        throw new Error('Selected room not found');
      }

      // Transform bookings into FullCalendar events
      const events: EventInput[] = [];

      // Separate own bookings from other bookings
      const ownBookings = bookingsArray.filter((booking: any) => booking.child_id === childId);
      const otherBookings = bookingsArray.filter((booking: any) => booking.child_id !== childId);

      // Add own bookings as full calendar events
      ownBookings.forEach((booking: any) => {
        events.push({
          id: `own-booking-${booking.id}`,
          title: `${childName}`,
          start: booking.start_time,
          end: booking.end_time,
          backgroundColor: '#3b82f6', // blue-500
          borderColor: '#2563eb', // blue-600
          textColor: '#ffffff',
          editable: true, // Can be edited
          classNames: ['own-booking-event'],
          extendedProps: {
            eventType: 'own-booking', // For two-column layout
            isOwnBooking: true,
            bookingId: booking.id,
            childId: booking.child_id,
            childName: childName
          }
        });
      });

      // Create a timeline of all booking start and end times to identify segments
      const timePoints: { time: Date, isStart: boolean, bookingId: number }[] = [];

      // Add all booking start and end times to the timeline
      bookingsArray.forEach((booking: any) => {
        const startTime = new Date(booking.start_time);
        const endTime = new Date(booking.end_time);

        timePoints.push({
          time: startTime,
          isStart: true,
          bookingId: booking.id
        });

        timePoints.push({
          time: endTime,
          isStart: false,
          bookingId: booking.id
        });
      });

      // Sort the timeline by time
      timePoints.sort((a, b) => {
        const timeDiff = a.time.getTime() - b.time.getTime();
        // If times are equal, put end times before start times
        if (timeDiff === 0) {
          return a.isStart ? 1 : -1;
        }
        return timeDiff;
      });

      // Skip if there are no time points
      if (timePoints.length === 0) {
        // Update the calendar with the new events (just own bookings)
        setCalendarEvents(events);

        // If calendar reference exists, update the events in the calendar directly
        if (calendarRef.current) {
          const calendarApi = calendarRef.current.getApi();
          // Remove all existing events
          calendarApi.removeAllEvents();
          // Add the new events
          calendarApi.addEventSource(events);
        }

        setLoading(false);
        return;
      }

      // Create time segments based on the timeline
      const activeBookings = new Set<number>();
      let lastTime = timePoints[0].time;

      for (let i = 0; i < timePoints.length; i++) {
        const point = timePoints[i];

        // Skip if this point is at the same time as the last one we processed
        if (i > 0 && point.time.getTime() === lastTime.getTime()) {
          // Just update the active bookings set
          if (point.isStart) {
            activeBookings.add(point.bookingId);
          } else {
            activeBookings.delete(point.bookingId);
          }
          continue;
        }

        // If there are active bookings and time has advanced, create a segment
        if (activeBookings.size > 0 && lastTime.getTime() < point.time.getTime()) {
          // Find the bookings that are active in this segment
          const segmentBookings = bookingsArray.filter(booking =>
            activeBookings.has(booking.id)
          );

          // Count the bookings in this segment
          const bookingsCount = segmentBookings.length;

          // Check if the segment is fully booked
          const isFullyBooked = bookingsCount >= selectedRoom.max_seats;

          // Calculate remaining seats
          const remainingSeats = selectedRoom.max_seats - bookingsCount;

          // Get the appropriate title and description based on locale and booking count
          let title = '';
          let description = '';

          if (isFullyBooked) {
            // Fully booked slot
            title = locale === 'de' ? 'Ausgebucht' : 'Fully Booked';
            description = locale === 'de'
              ? 'Dieser Zeitraum ist vollständig ausgebucht.'
              : 'This time slot is fully booked.';
          } else {
            // Partially booked slot - show count and remaining seats
            const personText = locale === 'de'
              ? (bookingsCount === 1 ? 'Person' : 'Personen')
              : (bookingsCount === 1 ? 'person' : 'people');

            const freeText = locale === 'de' ? 'frei' : 'free';
            title = `${bookingsCount} ${personText} | ${remainingSeats} ${freeText}`;
            description = locale === 'de'
              ? `${bookingsCount} ${personText} haben bereits gebucht. Noch ${remainingSeats} Plätze verfügbar.`
              : `${bookingsCount} ${personText} already booked. ${remainingSeats} seats still available.`;
          }

          // Create an event for this segment
          events.push({
            id: `segment-${lastTime.toISOString()}-${point.time.toISOString()}`,
            title: isFullyBooked ? 'X' : bookingsCount.toString(), // Show X for fully booked, otherwise show count
            start: lastTime,
            end: point.time,
            backgroundColor: isFullyBooked ? '#dc2626' : (remainingSeats === 1 ? '#fb923c' : '#f59e0b'), // Red for fully booked, darker amber for almost full, amber for partially booked
            borderColor: isFullyBooked ? '#b91c1c' : (remainingSeats === 1 ? '#ea580c' : '#d97706'),
            textColor: '#ffffff',
            editable: false, // Cannot be edited
            description: description,
            className: `compact-booking ${isFullyBooked ? 'fully-booked' : ''}`,
            extendedProps: {
              eventType: 'other-booking', // For two-column layout
              bookingsCount: bookingsCount,
              maxSeats: selectedRoom.max_seats,
              isFullyBooked: isFullyBooked,
              remainingSeats: remainingSeats,
              isCompactBooking: true
            }
          });
        }

        // Update the active bookings set
        if (point.isStart) {
          activeBookings.add(point.bookingId);
        } else {
          activeBookings.delete(point.bookingId);
        }

        // Update the last time
        lastTime = point.time;
      }

      // Update the calendar with the new events
      setCalendarEvents(events);

      // If calendar reference exists, update the events in the calendar directly
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        // Remove all existing events
        calendarApi.removeAllEvents();
        // Add the new events
        calendarApi.addEventSource(events);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch bookings');
    } finally {
      setLoading(false);
    }
  };

  // Handle view change
  const handleViewChange = (viewInfo: DatesSetArg): void => {
    if (calendarRef.current && selectedRoomId) {
      const startDate = viewInfo.view.activeStart.toISOString();
      const endDate = viewInfo.view.activeEnd.toISOString();
      fetchBookings(selectedRoomId, startDate, endDate);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal Content */}
        <div className="relative z-10 bg-gradient-to-br from-slate-800/90 via-purple-900/90 to-indigo-900/90 backdrop-blur-xl border border-white/10 rounded-2xl p-6 md:p-[54px] shadow-2xl w-full m-4 max-h-[90vh] flex flex-col">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 flex-shrink-0">
          <div className="flex justify-between w-full md:w-auto mb-4 md:mb-0">
            <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-400">
              {titleText.replace('{0}', childName)}
            </h3>
            <button
              onClick={onClose}
              className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white md:hidden"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Studio and Room Selection Dropdowns */}
          <div className="flex items-center space-x-4 w-full md:w-auto">
            {/* Studio Selection */}
            <div className="relative flex-grow md:w-64">
              <div className="flex items-center">
                <select
                  id="booking_studio"
                  value={selectedStudioId || ''}
                  onChange={(e) => setSelectedStudioId(e.target.value ? Number(e.target.value) : null)}
                  className="w-full p-2 rounded-lg border border-white/20 bg-black/30 backdrop-blur-md text-white appearance-none focus:outline-none focus:ring-2 focus:ring-amber-400/50 disabled:opacity-50 transition-all duration-200 text-sm"
                  disabled={studios.length === 0}
                  title="Nur für diese Buchung gültig"
                >
                  <option value="">{selectStudioPlaceholder}</option>
                  {studios.map(studio => (
                    <option key={studio.id} value={studio.id}>{studio.name}</option>
                  ))}
                </select>
                <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg className="h-4 w-4 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Room Selection */}
            <div className="relative flex-grow md:w-64">
              <div className="flex items-center">
                <select
                  id="booking_room"
                  value={selectedRoomId || ''}
                  onChange={(e) => setSelectedRoomId(e.target.value ? Number(e.target.value) : null)}
                  className="w-full p-2 rounded-lg border border-white/20 bg-black/30 backdrop-blur-md text-white appearance-none focus:outline-none focus:ring-2 focus:ring-amber-400/50 disabled:opacity-50 transition-all duration-200 text-sm"
                  disabled={!selectedStudioId || rooms.length === 0}
                  title="Select a room for booking"
                >
                  <option value="">{selectRoomPlaceholder}</option>
                  {rooms.map(room => {
                    // Get the correct text for "half hour" based on locale
                    const halfHourText = locale === 'de' ? 'halbe Stunde' : 'half hour';
                    // Get the correct text for "seats" based on locale
                    const seatsText = locale === 'de' ? 'Plätze' : 'seats';
                    return (
                      <option key={room.id} value={room.id}>
                        {room.name} ({formatPrice(room.price, locale)}/{halfHourText}, {room.max_seats} {seatsText})
                      </option>
                    );
                  })}
                </select>
                <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg className="h-4 w-4 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <button
              onClick={onClose}
              className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white hidden md:block"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* FullCalendar Container */}
        <div className="flex-grow overflow-hidden relative text-white calendar-container" style={{ height: "calc(80vh - 100px)" }}>
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center z-10 bg-black/30 backdrop-blur-sm">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 border-4 border-amber-400 border-t-transparent rounded-full animate-spin mb-2"></div>
                <p className="text-white/80 text-sm">Loading studio hours...</p>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="absolute top-2 left-2 right-2 z-10 bg-red-900/80 backdrop-blur-sm text-white p-3 rounded-lg shadow-lg">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-300 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-medium">Error loading studio hours</p>
                  <p className="text-sm text-white/80">{error}</p>
                </div>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto bg-red-800 hover:bg-red-700 p-1 rounded-full flex-shrink-0"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}
          {/* Add custom styling for FullCalendar elements here if needed */}
          <style jsx global>{`
            .fc { /* General FullCalendar container */
              color: rgba(255, 255, 255, 0.85); /* Softer text color */
              background-color: transparent; /* Ensure it uses modal background */
              height: 100%; /* Fill the container height */
              display: flex;
              flex-direction: column;
              font-family: 'Inter', sans-serif;
            }
            .fc .fc-toolbar { /* Toolbar (header) */
              background-color: rgba(255, 255, 255, 0.03);
              border-radius: 12px 12px 0 0;
              padding: 12px;
              margin-bottom: 0; /* Remove default margin */
              border-bottom: 1px solid rgba(255, 255, 255, 0.07);
            }
            .fc .fc-toolbar-title { /* Title (e.g., "April 2025") */
              color: rgba(255, 235, 160, 0.9); /* Softer yellow */
              font-size: 1.25em;
              font-weight: 500;
            }
            .fc .fc-button { /* Buttons (prev, next, today, view switchers) */
              background-color: rgba(255, 255, 255, 0.07);
              border: 1px solid rgba(255, 255, 255, 0.12);
              color: rgba(255, 255, 255, 0.85);
              transition: all 0.2s ease;
              text-transform: capitalize;
              border-radius: 8px;
              font-weight: 400;
              font-size: 0.9em;
              padding: 6px 12px;
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            .fc .fc-button:hover {
              background-color: rgba(255, 255, 255, 0.12);
              transform: translateY(-1px);
            }
            .fc .fc-button-primary:not(:disabled).fc-button-active,
            .fc .fc-button-primary:not(:disabled):active { /* Active view button */
              background-color: rgba(254, 215, 115, 0.3); /* Softer yellow */
              border-color: rgba(254, 215, 115, 0.5);
              color: rgba(255, 255, 255, 0.95);
            }
            .fc .fc-daygrid-day-number, .fc .fc-col-header-cell-cushion { /* Day numbers, column headers */
              color: rgba(255, 255, 255, 0.8);
            }
            .fc .fc-daygrid-day.fc-day-today { /* Today's date background */
              background-color: rgba(250, 204, 21, 0.08); /* Very light yellow highlight */
            }
            .fc .fc-daygrid-day-number {
              padding: 6px;
              font-size: 0.9em;
            }
            .fc .fc-daygrid-day:hover { /* Hover effect on days */
              background-color: rgba(255, 255, 255, 0.04);
            }
            .fc .fc-timegrid-slot-label { /* Time labels on the side */
              color: rgba(255, 255, 255, 0.6);
              font-size: 0.85em;
            }
            .fc .fc-timegrid-slot-lane { /* Background lanes for time slots */
              border-color: rgba(255, 255, 255, 0.03) !important;
            }
            .fc .fc-timegrid-slot { /* Time slots */
              height: 30px !important; /* Make time slots more compact */
            }
            .fc .fc-event { /* Event elements */
              cursor: pointer;
              border-width: 1px;
              font-size: 0.85em;
              padding: 2px 6px;
              border-radius: 6px;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
              opacity: 0.85;
              transition: opacity 0.2s, transform 0.2s, box-shadow 0.2s;
              /* Make events more compact to fit in 30-minute slots */
              min-height: 24px;
              height: auto !important;
              margin-bottom: 1px;
            }
            .fc .fc-event:hover {
              opacity: 0.95; /* Slightly less than full opacity on hover */
              transform: translateY(-1px);
              box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow */
            }
            /* Special styling for partially booked events to indicate they are clickable */
            .fc .fc-event:not(.fully-booked):hover {
              box-shadow: 0 3px 12px rgba(250, 204, 21, 0.4); /* Yellow glow for available slots */
              border-width: 2px;
            }
            .fc .fc-event:not(.fully-booked):hover::after {
              content: '+';
              position: absolute;
              right: 4px;
              top: 2px;
              font-weight: bold;
              font-size: 1.2em;
            }
            .fc .fc-event-main {
              color: white; /* Ensure event text is white */
              font-weight: 500;
            }
            .fc .fc-popover { /* Popover for "+more" links */
              background-color: rgba(30, 41, 59, 0.97); /* Darker background */
              border: 1px solid rgba(255, 255, 255, 0.1);
              color: white;
              backdrop-filter: blur(12px);
              border-radius: 6px;
              overflow: hidden;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
              font-size: 0.7em;
            }
            .fc .fc-popover-header {
              background-color: rgba(255, 255, 255, 0.07);
              color: rgba(254, 215, 115, 0.9);
              padding: 4px 8px;
              font-weight: 500;
            }
            .fc-v-event { /* Vertical events in timeGrid views */
              box-shadow: 0 1px 4px rgba(0,0,0,0.15);
            }
            .fc-daygrid-event { /* Events in month view */
              margin-top: 1px;
              margin-bottom: 1px;
            }
            .fc-view-harness { /* This contains the actual calendar grid */
              flex-grow: 1; /* Allow grid to take remaining space */
              overflow: auto; /* Add scrollbars if needed */
              background-color: rgba(0, 0, 0, 0.08); /* Barely visible darker grid background */
              border-radius: 0 0 12px 12px;
              min-height: 600px; /* Ensure minimum height to show content */
              border: 1px solid rgba(255, 255, 255, 0.03) !important;
            }
            /* Style for selected time slot */
            .fc .fc-highlight {
              background: rgba(250, 204, 21, 0.15) !important; /* Subtle yellow highlight for selection */
              opacity: 0.7;
            }
            /* Improve header styling */
            .fc-col-header-cell {
              background-color: rgba(255, 255, 255, 0.02);
              padding: 10px 0 !important;
              font-weight: 500;
            }
            /* Ensure weekend days are styled differently */
            .fc-day-sat, .fc-day-sun {
              background-color: rgba(0, 0, 0, 0.1);
            }
            /* Fix for the scrollbar issue */
            .fc-scroller {
              overflow: auto !important;
              scrollbar-width: thin;
              scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
            }
            .fc-scroller::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            .fc-scroller::-webkit-scrollbar-track {
              background: transparent;
            }
            .fc-scroller::-webkit-scrollbar-thumb {
              background-color: rgba(255, 255, 255, 0.2);
              border-radius: 6px;
            }
            /* Time grid specific styles */
            .fc-timegrid-cols {
              min-height: 600px;
            }
            /* Ensure the time axis is visible */
            .fc-timegrid-axis-cushion {
              color: rgba(255, 255, 255, 0.6);
              font-weight: normal;
              font-size: 0.85em;
            }
            /* Make the all-day section more compact */
            .fc-timegrid-axis-frame, .fc-timegrid-slot-label-frame {
              height: 100% !important;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            /* Soften cell borders */
            .fc td, .fc th {
              border-color: rgba(255, 255, 255, 0.03) !important; /* Very subtle borders for all cells */
            }
            /* Improve day header styling */
            .fc-col-header-cell-cushion {
              padding: 8px 4px;
              font-weight: 500;
            }
            /* Fix the white frame on left and top */
            .fc-theme-standard td, .fc-theme-standard th,
            .fc-theme-standard .fc-scrollgrid {
              border-color: rgba(255, 255, 255, 0.03) !important; /* Very subtle borders */
            }
            .fc-scrollgrid {
              border: 1px solid rgba(255, 255, 255, 0.03) !important; /* Very subtle outer border */
              background: transparent !important;
            }
            .fc-col-header, .fc-timegrid-body {
              border: 1px solid rgba(255, 255, 255, 0.03) !important; /* Very subtle borders */
            }
            /* Restore borders but with very low opacity */
            .fc-scrollgrid-section-header > th,
            .fc-scrollgrid-section-body > tr > td,
            .fc-timegrid-body, .fc-timegrid-slots {
              border: 1px solid rgba(255, 255, 255, 0.03) !important; /* Very subtle borders */
            }

            /* Business hours styling */
            .fc-timegrid-col-bg .fc-non-business {
              background-color: rgba(0, 0, 0, 0.3) !important; /* Darker background for non-business hours */
            }
            .fc-timegrid-col-bg .fc-business {
              background-color: rgba(250, 204, 21, 0.08) !important; /* Light yellow for business hours */
              opacity: 0.7;
            }
            .fc-daygrid-day.fc-day-past .fc-daygrid-day-frame {
              opacity: 0.7; /* Slightly fade past days */
            }
            /* Improve time slot selection styling */
            .fc-highlight {
              background-color: rgba(250, 204, 21, 0.25) !important; /* Light yellow highlight */
              border: 2px dashed rgba(250, 204, 21, 0.5) !important;
            }

            /* Two-column layout specific styles */
            .calendar-container .fc-timegrid-event-harness { position: absolute !important; }

            /* Make sure other bookings stay in the left column */
            .calendar-container .fc-timegrid-event-harness[data-event-type="other-booking"] {
              left: 0 !important;
              right: 85% !important;
              width: 15% !important;
            }

            /* Position other bookings on the very left side of their column */
            .calendar-container .fc-timegrid-event-harness[data-event-type="other-booking"] .fc-timegrid-event {
              margin-left: 0 !important;
              width: calc(100% - 2px) !important;
            }

            /* Make sure own bookings stay in the right column */
            .calendar-container .fc-timegrid-event-harness[data-event-type="own-booking"] {
              left: 15% !important;
              right: 0 !important;
              width: 85% !important;
            }

            /* Compact booking indicator styles */
            .compact-booking-indicator {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 2px 4px;
              height: 100%;
              width: 100%;
              color: white;
              font-weight: bold;
              font-size: 0.8rem;
            }

            .compact-booking-indicator.fully-booked {
              background-color: rgba(220, 38, 38, 0.7); /* Red with transparency */
            }

            .booking-icon {
              margin-right: 4px;
              font-size: 1rem;
            }

            .booking-count {
              font-size: 0.9rem;
            }
          `}</style>
          <FullCalendar
            ref={calendarRef}
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="timeGridWeek"
            locales={allLocales}
            locale={locale} // Pass locale to FullCalendar
            initialDate="2025-03-31" // Use the mock data date for demonstration
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay' // View switchers
            }}
            events={calendarEvents}
            businessHours={businessHours} // Display studio opening hours
            editable={true} // Allow dragging and resizing events
            selectable={true} // Allow selecting time slots
            selectMirror={true} // Show placeholder event while selecting
            dayMaxEvents={true} // Allow "more" link when too many events
            weekends={true} // Show weekends
            nowIndicator={true} // Show current time indicator
            allDaySlot={false} // Hide the all-day row
            slotMinTime="08:00:00" // Start time
            slotMaxTime="20:00:00" // End time
            slotDuration="00:30:00" // 30-minute slots
            slotLabelInterval="01:00:00" // Show hour labels
            snapDuration="00:30:00" // Snap to 30-minute intervals
            selectMinDistance={5} // Minimum distance in pixels to trigger a selection
            firstDay={getFirstDayOfWeek(locale)} // Set first day of week based on locale
            slotLabelFormat={getTimeFormatConfig(locale).slotLabelFormat}
            eventTimeFormat={getTimeFormatConfig(locale).eventTimeFormat}
            scrollTime="09:00:00" // Scroll to 9am by default
            height="100%" // Fill the container height
            expandRows={true} // Expand rows to fill height
            handleWindowResize={true} // Handle window resize
            datesSet={handleViewChange} // Track date/view changes
            eventDidMount={(info) => {
              // Skip background events
              if (info.event.display === 'background') return;

              // Set data-event-type for two-column layout
              if (info.event.extendedProps?.eventType === 'own-booking') {
                info.el.setAttribute('data-event-type', 'own-booking');
                const harness = info.el.closest('.fc-timegrid-event-harness');
                if (harness) {
                  harness.setAttribute('data-event-type', 'own-booking');
                  // Move to right column if not already there
                  const harnessElement = harness as HTMLElement;
                  if (!harnessElement.style.left || !harnessElement.style.left.includes('15%')) {
                    harnessElement.style.left = '15%';
                    harnessElement.style.right = '0';
                    harnessElement.style.width = '85%';
                  }
                }
              } else if (info.event.extendedProps?.eventType === 'other-booking') {
                info.el.setAttribute('data-event-type', 'other-booking');
                const harness = info.el.closest('.fc-timegrid-event-harness');
                if (harness) {
                  harness.setAttribute('data-event-type', 'other-booking');
                  // Move to left column if not already there
                  const harnessElement = harness as HTMLElement;
                  if (!harnessElement.style.left || harnessElement.style.left.includes('15%')) {
                    harnessElement.style.left = '0';
                    harnessElement.style.right = '85%';
                    harnessElement.style.width = '15%';
                  }
                }
              }
            }}

            // --- Interaction Callbacks ---
            selectAllow={(selectInfo) => {
              // Check if the selected time slot overlaps with any fully booked slots
              const selectedStart = new Date(selectInfo.start);
              const selectedEnd = new Date(selectInfo.end);

              // Get the selected room to check max capacity
              const selectedRoom = rooms.find(r => r.id === selectedRoomId);
              if (!selectedRoom) return false; // Safety check

              // Check if the selected time is within studio opening hours
              const isWithinOpeningHours = isTimeSlotWithinOpeningHours(selectedStart, selectedEnd, businessHours);
              if (!isWithinOpeningHours) {
                // Show error message in the appropriate language
                if (locale === 'de') {
                  setError('Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.');
                } else {
                  setError('Bookings are only possible within the studio opening hours.');
                }
                return false;
              }

              // Group bookings by time slot to count how many bookings exist for each slot
              const bookingsByTimeSlot: Record<string, any[]> = {};

              bookings.forEach(booking => {
                const startTime = new Date(booking.start_time).toISOString();
                if (!bookingsByTimeSlot[startTime]) {
                  bookingsByTimeSlot[startTime] = [];
                }
                bookingsByTimeSlot[startTime].push(booking);
              });

              // Check if any time slot in the selection range is fully booked
              const isAnySlotFullyBooked = Object.entries(bookingsByTimeSlot).some(([, slotBookings]) => {
                const bookingStart = new Date(slotBookings[0].start_time);
                const bookingEnd = new Date(slotBookings[0].end_time);

                // Count ALL bookings for this time slot, including the current child's bookings
                // This ensures we're checking the true capacity including the current child
                const bookingsCount = slotBookings.length;

                // Check if this time slot is fully booked
                const isFullyBooked = bookingsCount >= selectedRoom.max_seats;

                // Check if this fully booked slot overlaps with the selection
                const isOverlapping = (
                  (selectedStart >= bookingStart && selectedStart < bookingEnd) ||
                  (selectedEnd > bookingStart && selectedEnd <= bookingEnd) ||
                  (selectedStart <= bookingStart && selectedEnd >= bookingEnd)
                );

                // Only block selection if the slot is fully booked and overlaps
                return isFullyBooked && isOverlapping;
              });

              // Allow selection if no fully booked slots overlap with the selection
              return !isAnySlotFullyBooked;
            }}
            select={(selectInfo) => {
              // Handle time slot selection by opening the booking confirmation modal
              let calendarApi = selectInfo.view.calendar;
              calendarApi.unselect(); // clear date selection

              // Check if studio and room are selected
              if (!selectedStudioId || !selectedRoomId) {
                alert('Please select a studio and room before booking');
                return;
              }

              // Get the selected room to check max capacity
              const selectedRoom = rooms.find(r => r.id === selectedRoomId);
              if (!selectedRoom) {
                setError('Selected room not found');
                return;
              }

              // Check for overlap with fully booked time slots (double-check)
              const selectedStart = new Date(selectInfo.start);
              const selectedEnd = new Date(selectInfo.end);

              // Double-check that the selected time is within studio opening hours
              const isWithinOpeningHours = isTimeSlotWithinOpeningHours(selectedStart, selectedEnd, businessHours);
              if (!isWithinOpeningHours) {
                // Show error message in the appropriate language
                if (locale === 'de') {
                  setError('Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.');
                } else {
                  setError('Bookings are only possible within the studio opening hours.');
                }
                return;
              }

              // Group bookings by time slot to count how many bookings exist for each slot
              const bookingsByTimeSlot: Record<string, any[]> = {};

              bookings.forEach(booking => {
                const startTime = new Date(booking.start_time).toISOString();
                if (!bookingsByTimeSlot[startTime]) {
                  bookingsByTimeSlot[startTime] = [];
                }
                bookingsByTimeSlot[startTime].push(booking);
              });

              // Check if any time slot in the selection range is fully booked
              const fullyBookedSlots = Object.values(bookingsByTimeSlot).filter(slotBookings => {
                const bookingStart = new Date(slotBookings[0].start_time);
                const bookingEnd = new Date(slotBookings[0].end_time);

                // Count ALL bookings for this time slot, including the current child's bookings
                // This ensures we're checking the true capacity including the current child
                const bookingsCount = slotBookings.length;

                // Check if this time slot is fully booked
                const isFullyBooked = bookingsCount >= selectedRoom.max_seats;

                // Check if this fully booked slot overlaps with the selection
                const isOverlapping = (
                  (selectedStart >= bookingStart && selectedStart < bookingEnd) ||
                  (selectedEnd > bookingStart && selectedEnd <= bookingEnd) ||
                  (selectedStart <= bookingStart && selectedEnd >= bookingEnd)
                );

                // Only consider fully booked slots that overlap
                return isFullyBooked && isOverlapping;
              });

              if (fullyBookedSlots.length > 0) {
                // Show error message in the appropriate language
                if (locale === 'de') {
                  setError('Dieser Zeitraum ist bereits vollständig ausgebucht. Bitte wählen Sie eine andere Zeit.');
                } else {
                  setError('This time slot is already fully booked. Please select another time.');
                }
                return;
              }

              // Check if there are any partially booked slots in the selection range and collect information
              const partiallyBookedSlots = Object.values(bookingsByTimeSlot).filter(slotBookings => {
                const bookingStart = new Date(slotBookings[0].start_time);
                const bookingEnd = new Date(slotBookings[0].end_time);

                // Count ALL bookings for this time slot, including the current child's bookings
                // This ensures we're checking the true capacity including the current child
                const bookingsCount = slotBookings.length;

                // Check if this time slot is partially booked (not fully booked)
                // We need to account for the new booking we're about to make
                const isPartiallyBooked = bookingsCount > 0 && bookingsCount < selectedRoom.max_seats;

                // Check if this partially booked slot overlaps with the selection
                const isOverlapping = (
                  (selectedStart >= bookingStart && selectedStart < bookingEnd) ||
                  (selectedEnd > bookingStart && selectedEnd <= bookingEnd) ||
                  (selectedStart <= bookingStart && selectedEnd >= bookingEnd)
                );

                // Only consider partially booked slots that overlap
                return isPartiallyBooked && isOverlapping;
              });

              // Set booking details for confirmation modal
              // Include information about partially booked slots if any
              let availabilityInfo = '';
              if (partiallyBookedSlots.length > 0) {
                // Get the first partially booked slot for simplicity
                const firstSlot = partiallyBookedSlots[0];
                const bookingsCount = firstSlot.length;
                const remainingSeats = selectedRoom.max_seats - bookingsCount;

                // Set availability info based on locale
                if (locale === 'de') {
                  const personText = bookingsCount === 1 ? 'Person' : 'Personen';
                  availabilityInfo = `${bookingsCount} ${personText} bereits gebucht. Noch ${remainingSeats} Plätze verfügbar.`;
                } else {
                  const personText = bookingsCount === 1 ? 'person' : 'people';
                  availabilityInfo = `${bookingsCount} ${personText} already booked. ${remainingSeats} seats available.`;
                }
              }

              setBookingDetails({
                startTime: selectInfo.startStr,
                endTime: selectInfo.endStr,
                availabilityInfo: availabilityInfo
              });

              // Show booking confirmation modal
              setShowBookingConfirmation(true);
            }}
            eventClick={(clickInfo) => {
              // Get event details
              const eventProps = clickInfo.event.extendedProps;

              // Check if this is the user's own booking
              if (eventProps.isOwnBooking) {
                // Show delete booking modal for own bookings
                setDeleteBookingDetails({
                  bookingId: eventProps.bookingId,
                  startTime: clickInfo.event.startStr,
                  endTime: clickInfo.event.endStr
                });
                setShowDeleteBookingModal(true);
                return;
              }

              const isFullyBooked = eventProps.isFullyBooked;

              // If the event is fully booked, just show details
              if (isFullyBooked) {
                const description = eventProps.description || descriptionNoneText;
                alert(confirmViewDetailsText.replace('{0}', clickInfo.event.title).replace('{1}', description));
                return;
              }

              // If the event is not fully booked, allow booking for the same time slot
              // Get the selected room to check max capacity
              const selectedRoom = rooms.find(r => r.id === selectedRoomId);
              if (!selectedRoom) {
                setError('Selected room not found');
                return;
              }

              // Set booking details for confirmation modal
              const availabilityInfo = eventProps.description || '';

              setBookingDetails({
                startTime: clickInfo.event.startStr,
                endTime: clickInfo.event.endStr,
                availabilityInfo: availabilityInfo
              });

              // Show booking confirmation modal
              setShowBookingConfirmation(true);
            }}
            eventContent={(arg) => {
              // Custom rendering for compact booking indicators
              const { event } = arg;
              const extendedProps = event.extendedProps || {};

              // If it's a compact booking (other people's bookings)
              if (extendedProps.eventType === 'other-booking') {
                const isFullyBooked = extendedProps.isFullyBooked;
                const bookingsCount = extendedProps.bookingsCount;

                return {
                  html: `<div class="compact-booking-indicator ${isFullyBooked ? 'fully-booked' : ''}">
                    <span class="booking-icon">${isFullyBooked ? '✕' : '👤'}</span>
                    <span class="booking-count">${isFullyBooked ? '' : bookingsCount}</span>
                  </div>`
                };
              }

              // For own bookings, use default rendering
              return null;
            }}
            eventDrop={(dropInfo) => {
              // Handle event drag-and-drop
              alert(alertEventMovedText.replace('{0}', dropInfo.event.title).replace('{1}', dropInfo.event.startStr));

              // In a real implementation, you would update the event in your backend
              // const timezone = getBrowserTimezone();
              //
              // updateEvent({
              //   id: dropInfo.event.id,
              //   start: dropInfo.event.startStr,
              //   end: dropInfo.event.endStr,
              //   timezone: timezone
              // });
            }}
            eventResize={(resizeInfo) => {
              // Handle event resizing
              alert(alertEventResizedText.replace('{0}', resizeInfo.event.title).replace('{1}', resizeInfo.event.endStr));

              // In a real implementation, you would update the event in your backend
              // const timezone = getBrowserTimezone();
              //
              // updateEvent({
              //   id: resizeInfo.event.id,
              //   start: resizeInfo.event.startStr,
              //   end: resizeInfo.event.endStr,
              //   timezone: timezone
              // });
            }}
            // You can add more callbacks like dateClick, eventMouseEnter, eventMouseLeave etc.
          />
        </div>
        </div>
      </div>

      {/* Booking Confirmation Modal */}
      {showBookingConfirmation && bookingDetails && selectedStudioId && selectedRoomId && (
        <BookingConfirmationModal
          isOpen={showBookingConfirmation}
          onClose={() => setShowBookingConfirmation(false)}
          businessHours={businessHours}
          onSuccess={(createdBooking) => {
            // Close the booking confirmation modal
            setShowBookingConfirmation(false);

            // Show success message for booking creation
            setIsDeleteSuccess(false);
            setBookingSuccess(true);
            setTimeout(() => {
              setBookingSuccess(false);
            }, 5000); // Hide success message after 5 seconds

            // If we have the created booking data, add it directly to the calendar
            if (createdBooking && calendarRef.current) {
              const calendarApi = calendarRef.current.getApi();
              const selectedRoom = rooms.find(r => r.id === selectedRoomId);

              if (selectedRoom) {
                // Check if we have an array of bookings (recurring bookings) or a single booking
                const bookingsArray = Array.isArray(createdBooking) ? createdBooking : [createdBooking];

                // Create a map to track time slots that already have indicators
                const processedTimeSlots = new Map();

                // First pass: Add all bookings to the calendar
                bookingsArray.forEach(booking => {
                  // Add the user's own booking
                  const ownBookingEvent = {
                    id: `own-booking-${booking.id}`,
                    title: `${childName}`,
                    start: booking.start_time,
                    end: booking.end_time,
                    backgroundColor: '#3b82f6', // blue-500
                    borderColor: '#2563eb', // blue-600
                    textColor: '#ffffff',
                    editable: true,
                    classNames: ['own-booking-event'],
                    extendedProps: {
                      eventType: 'own-booking',
                      isOwnBooking: true,
                      bookingId: booking.id,
                      childId: childId,
                      childName: childName
                    }
                  };

                  // Add the booking to the calendar
                  calendarApi.addEvent(ownBookingEvent);
                });

                // Create a timeline of all booking start and end times to identify segments
                // Include both existing bookings and newly created bookings
                const allBookings = [...bookings, ...bookingsArray];
                const timePoints: { time: Date, isStart: boolean, bookingId: number }[] = [];

                // Add all booking start and end times to the timeline
                allBookings.forEach((booking: any) => {
                  const startTime = new Date(booking.start_time);
                  const endTime = new Date(booking.end_time);

                  timePoints.push({
                    time: startTime,
                    isStart: true,
                    bookingId: booking.id
                  });

                  timePoints.push({
                    time: endTime,
                    isStart: false,
                    bookingId: booking.id
                  });
                });

                // Sort the timeline by time
                timePoints.sort((a, b) => {
                  const timeDiff = a.time.getTime() - b.time.getTime();
                  // If times are equal, put end times before start times
                  if (timeDiff === 0) {
                    return a.isStart ? 1 : -1;
                  }
                  return timeDiff;
                });

                // Skip if there are no time points (should not happen)
                if (timePoints.length === 0) {
                  return;
                }

                // Create time segments based on the timeline
                const activeBookings = new Set<number>();
                let lastTime = timePoints[0].time;

                for (let i = 0; i < timePoints.length; i++) {
                  const point = timePoints[i];

                  // Skip if this point is at the same time as the last one we processed
                  if (i > 0 && point.time.getTime() === lastTime.getTime()) {
                    // Just update the active bookings set
                    if (point.isStart) {
                      activeBookings.add(point.bookingId);
                    } else {
                      activeBookings.delete(point.bookingId);
                    }
                    continue;
                  }

                  // If there are active bookings and time has advanced, create a segment
                  if (activeBookings.size > 0 && lastTime.getTime() < point.time.getTime()) {
                    // Find the bookings that are active in this segment
                    const segmentBookings = allBookings.filter(booking =>
                      activeBookings.has(booking.id)
                    );

                    // Count the bookings in this segment
                    const bookingsCount = segmentBookings.length;

                    // Check if the segment is fully booked
                    const isFullyBooked = bookingsCount >= selectedRoom.max_seats;

                    // Calculate remaining seats
                    const remainingSeats = selectedRoom.max_seats - bookingsCount;

                    // Get the appropriate title and description based on locale and booking count
                    let title = '';
                    let description = '';

                    if (isFullyBooked) {
                      // Fully booked slot
                      title = locale === 'de' ? 'Ausgebucht' : 'Fully Booked';
                      description = locale === 'de'
                        ? 'Dieser Zeitraum ist vollständig ausgebucht.'
                        : 'This time slot is fully booked.';
                    } else {
                      // Partially booked slot - show count and remaining seats
                      const personText = locale === 'de'
                        ? (bookingsCount === 1 ? 'Person' : 'Personen')
                        : (bookingsCount === 1 ? 'person' : 'people');

                      const freeText = locale === 'de' ? 'frei' : 'free';
                      title = `${bookingsCount} ${personText} | ${remainingSeats} ${freeText}`;
                      description = locale === 'de'
                        ? `${bookingsCount} ${personText} haben bereits gebucht. Noch ${remainingSeats} Plätze verfügbar.`
                        : `${bookingsCount} ${personText} already booked. ${remainingSeats} seats still available.`;
                    }

                    // Create an event for this segment
                    const indicatorEvent = {
                      id: `segment-${lastTime.toISOString()}-${point.time.toISOString()}`,
                      title: isFullyBooked ? 'X' : bookingsCount.toString(), // Show X for fully booked, otherwise show count
                      start: lastTime,
                      end: point.time,
                      backgroundColor: isFullyBooked ? '#dc2626' : (remainingSeats === 1 ? '#fb923c' : '#f59e0b'), // Red for fully booked, darker amber for almost full, amber for partially booked
                      borderColor: isFullyBooked ? '#b91c1c' : (remainingSeats === 1 ? '#ea580c' : '#d97706'),
                      textColor: '#ffffff',
                      editable: false, // Cannot be edited
                      description: description,
                      className: `compact-booking ${isFullyBooked ? 'fully-booked' : ''}`,
                      extendedProps: {
                        eventType: 'other-booking', // For two-column layout
                        bookingsCount: bookingsCount,
                        maxSeats: selectedRoom.max_seats,
                        isFullyBooked: isFullyBooked,
                        remainingSeats: remainingSeats,
                        isCompactBooking: true
                      }
                    };

                    // Add the indicator event to the calendar
                    calendarApi.addEvent(indicatorEvent);
                  }

                  // Update the active bookings set
                  if (point.isStart) {
                    activeBookings.add(point.bookingId);
                  } else {
                    activeBookings.delete(point.bookingId);
                  }

                  // Update the last time
                  lastTime = point.time;
                }
              }
            }

            // We've already added the bookings directly to the calendar,
            // but we should still refresh all bookings to ensure consistency with the server
            // and make sure all indicators are properly displayed.

            // Schedule a delayed refresh to ensure consistency with the server
            // This is important for recurring bookings to ensure all instances are shown correctly
            setTimeout(() => {
              if (calendarRef.current && selectedRoomId) {
                const api = calendarRef.current.getApi();
                const startDate = api.view.activeStart.toISOString();
                const endDate = api.view.activeEnd.toISOString();

                // Force a complete refresh of all bookings
                fetchBookings(selectedRoomId, startDate, endDate);

                // If we have recurring bookings, make sure to scroll to the first booking
                if (Array.isArray(createdBooking) && createdBooking.length > 0) {
                  const firstBookingDate = new Date(createdBooking[0].start_time);
                  api.scrollToTime(firstBookingDate.getHours() + ':' + firstBookingDate.getMinutes());
                }
              }
            }, 1000); // Shorter delay for better user experience
          }}
          childId={childId}
          childName={childName}
          parentId={parentId}
          studioId={selectedStudioId}
          roomId={selectedRoomId}
          startTime={bookingDetails.startTime}
          endTime={bookingDetails.endTime}
          studioName={studios.find(s => s.id === selectedStudioId)?.name || ''}
          roomName={rooms.find(r => r.id === selectedRoomId)?.name || ''}
          roomPrice={rooms.find(r => r.id === selectedRoomId)?.price || 0}
          availabilityInfo={bookingDetails.availabilityInfo}
          onUpdateAvailabilityInfo={(info) => {
            setBookingDetails(prev => prev ? { ...prev, availabilityInfo: info } : null);
          }}
        />
      )}

      {/* Delete Booking Modal */}
      {showDeleteBookingModal && deleteBookingDetails && selectedStudioId && (
        <DeleteBookingModal
          isOpen={showDeleteBookingModal}
          onClose={() => setShowDeleteBookingModal(false)}
          onSuccess={() => {
            // Close the delete booking modal
            setShowDeleteBookingModal(false);

            // Show success message for booking deletion
            setIsDeleteSuccess(true);
            setBookingSuccess(true);
            setTimeout(() => {
              setBookingSuccess(false);
            }, 5000); // Hide success message after 5 seconds

            // Refresh all bookings data to ensure everything is up to date
            if (calendarRef.current && selectedRoomId) {
              const api = calendarRef.current.getApi();
              const startDate = api.view.activeStart.toISOString();
              const endDate = api.view.activeEnd.toISOString();

              // Remove all events and fetch fresh data to ensure proper segment calculation
              api.removeAllEvents();
              fetchBookings(selectedRoomId, startDate, endDate);
            }
          }}
          bookingId={deleteBookingDetails.bookingId}
          childName={childName}
          startTime={deleteBookingDetails.startTime}
          endTime={deleteBookingDetails.endTime}
          studioName={studios.find(s => s.id === selectedStudioId)?.name || ''}
          roomName={rooms.find(r => r.id === selectedRoomId)?.name || ''}
          locale={locale}
        />
      )}

      {/* Success Message */}
      {bookingSuccess && (
        <div className="fixed bottom-4 right-4 bg-green-800/90 text-white px-6 py-4 rounded-xl shadow-lg border border-green-600/50 z-50 animate-fadeInSlide">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <div>
              <p className="font-medium">
                {isDeleteSuccess
                  ? bookingConfirmationT.raw('deleteSuccessMessage')
                  : bookingConfirmationT.raw('successMessage')}
              </p>
              <p className="text-sm text-green-200">
                {isDeleteSuccess
                  ? bookingConfirmationT.raw('deleteSuccessDescription')
                  : bookingConfirmationT.raw('successDescription')}
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FullCalendarBookingModal;
