'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname, useParams } from 'next/navigation';
import DashboardLayout from './DashboardLayout';

interface ShopLayoutProps {
  children: React.ReactNode;
  cartItemCount?: number;
  favoritesCount?: number;
  onCartClick?: () => void;
  showBackButton?: boolean;
}

// Translations for the shop layout
const textTranslations = {
  en: {
    shop: 'Shop',
    backToShop: 'Back to Shop',
    viewFavorites: 'View Favorites',
    viewCart: 'View Cart'
  },
  de: {
    shop: 'Shop',
    backToShop: 'Zurück zum Shop',
    viewFavorites: 'Favoriten anzeigen',
    viewCart: 'Warenkorb anzeigen'
  },
  es: {
    shop: 'Tienda',
    backToShop: 'Volver a la Tienda',
    viewFavorites: 'Ver Favoritos',
    viewCart: 'Ver Carrito'
  },
  fr: {
    shop: 'Boutique',
    backToShop: 'Retour à la Boutique',
    viewFavorites: 'Voir les Favoris',
    viewCart: 'Voir le Panier'
  },
  zh: {
    shop: '商店',
    backToShop: '返回商店',
    viewFavorites: '查看收藏',
    viewCart: '查看购物车'
  },
  ja: {
    shop: 'ショップ',
    backToShop: 'ショップに戻る',
    viewFavorites: 'お気に入りを見る',
    viewCart: 'カートを見る'
  },
  ar: {
    shop: 'المتجر',
    backToShop: 'العودة إلى المتجر',
    viewFavorites: 'عرض المفضلة',
    viewCart: 'عرض السلة'
  },
  he: {
    shop: 'חנות',
    backToShop: 'חזרה לחנות',
    viewFavorites: 'הצג מועדפים',
    viewCart: 'הצג עגלה'
  },
  pt: {
    shop: 'Loja',
    backToShop: 'Voltar à Loja',
    viewFavorites: 'Ver Favoritos',
    viewCart: 'Ver Carrinho'
  }
};

const ShopLayout: React.FC<ShopLayoutProps> = ({ 
  children, 
  cartItemCount = 0, 
  favoritesCount = 0,
  onCartClick,
  showBackButton = false
}) => {
  const pathname = usePathname();
  const params = useParams();
  const currentLocale = params.locale || 'en'; // Default to 'en'
  const t = textTranslations[currentLocale as keyof typeof textTranslations] || textTranslations.en;
  
  return (
    <DashboardLayout>
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div className="flex items-center gap-3">
            {showBackButton && (
              <Link 
                href={`/${currentLocale}/shop`}
                className="flex items-center justify-center p-2 rounded-xl bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-200 text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="sr-only">{t.backToShop}</span>
              </Link>
            )}
            <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {t.shop}
            </h2>
          </div>
          
          <div className="flex items-center gap-3">
            <Link 
              href={`/${currentLocale}/shop/favorites`}
              className="relative flex items-center justify-center p-2 rounded-xl bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-200 text-white"
              aria-label={t.viewFavorites}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              {favoritesCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                  {favoritesCount}
                </span>
              )}
            </Link>
            
            <button 
              onClick={onCartClick}
              className="relative flex items-center justify-center p-2 rounded-xl bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-200 text-white"
              aria-label={t.viewCart}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
      
      {children}
    </DashboardLayout>
  );
};

export default ShopLayout; 