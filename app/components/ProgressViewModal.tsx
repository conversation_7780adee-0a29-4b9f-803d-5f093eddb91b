"use client";

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { skillService } from '../../services/skillService';
import { format } from 'date-fns';
import { SkillLevel, ChildSkillProgress } from '../../types/skill';

interface ProgressViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  childId: number;
  skillId: number;
  skillName: string;
  skillArea: string;
  skillLevel?: string;
  skillLevelId?: number;
  progress: number;
  notes?: string;
}

const ProgressViewModal: React.FC<ProgressViewModalProps> = ({
  isOpen,
  onClose,
  childId,
  skillId,
  skillName,
  skillArea,
  skillLevel,
  skillLevelId,
  progress,
  notes
}) => {
  const t = useTranslations('LearningDocumentation.Progress');

  // Ensure progress is a valid number
  let safeProgress = 0;

  if (progress !== undefined && progress !== null) {
    // Convert to number if it's a string
    const numProgress = typeof progress === 'string' ? parseInt(progress, 10) : progress;
    // Ensure it's a valid number between 0 and 100
    safeProgress = typeof numProgress === 'number' && !isNaN(numProgress) ?
      Math.min(100, Math.max(0, numProgress)) : 0;
  }

  // Log the progress value for debugging
  console.log(`ProgressViewModal for ${skillName}: original progress=${progress} (${typeof progress}), safeProgress=${safeProgress}`);

  const [progressHistory, setProgressHistory] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle clicking outside the modal to close it
  const handleOutsideClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Load progress history when the modal opens
  useEffect(() => {
    if (isOpen && childId && skillId) {
      const fetchProgressHistory = async () => {
        setIsLoadingHistory(true);
        setError(null);
        try {
          // Try to get progress history if the endpoint exists
          try {
            const history = await skillService.getChildSkillProgressHistory(childId, skillId);
            // Sort history by date (newest first)
            const sortedHistory = [...history].sort((a, b) =>
              new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );
            setProgressHistory(sortedHistory);
          } catch (err) {
            console.log('Progress history endpoint may not exist yet:', err);
            setProgressHistory([]);
          }
        } catch (err) {
          console.error('Error fetching progress history:', err);
          setError(t('errorFetchingHistory'));
        } finally {
          setIsLoadingHistory(false);
        }
      };

      fetchProgressHistory();
    }
  }, [isOpen, childId, skillId, t]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={handleOutsideClick}
    >
      <div
        className="bg-purple-900/90 border border-white/10 rounded-xl p-6 w-11/12 max-w-md shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-yellow-400">{skillName}</h2>
          <button
            onClick={onClose}
            className="text-gray-300 hover:text-yellow-400 transition-colors"
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="text-sm text-gray-300 mb-4">({skillArea})</div>

        <div className="space-y-4 mb-6">
          {/* Current progress details */}
          <div className="bg-white/5 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Skill Level */}
              <div>
                <label className="block text-yellow-400 mb-1 text-sm font-medium">
                  {t('skillLevelLabel')}
                </label>
                <div className="text-white">
                  {skillLevel || t('noLevelAssigned')}
                </div>
              </div>

              {/* Progress */}
              <div>
                <label className="block text-yellow-400 mb-1 text-sm font-medium">
                  {t('progressLabel')}
                </label>
                <div className="text-white">
                  {safeProgress}%
                </div>
              </div>

              {/* Notes */}
              {notes && (
                <div className="col-span-2">
                  <label className="block text-yellow-400 mb-1 text-sm font-medium">
                    {t('notesLabel')}
                  </label>
                  <div className="text-white whitespace-pre-wrap">
                    {notes}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Progress History */}
          <div>
            <h3 className="text-lg font-medium text-yellow-400 mb-2">{t('historyTitle')}</h3>

            {isLoadingHistory ? (
              <div className="text-center py-4">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-yellow-400 border-r-2 border-b-2 border-transparent"></div>
                <p className="mt-2 text-sm text-gray-300">{t('loadingMessage')}</p>
              </div>
            ) : error ? (
              <div className="text-red-400 text-sm">{error}</div>
            ) : progressHistory.length === 0 ? (
              <div className="text-gray-300 text-sm">{t('historyEmpty')}</div>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {progressHistory.map((record, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-3 text-sm">
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-300">{t('historyDate')}:</span>
                      <span className="text-white">
                        {record.created_at ? format(new Date(record.created_at), 'PPP') : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-300">{t('progressLabel')}:</span>
                      <span className="text-white">
                        {typeof record.progress === 'number' ? record.progress :
                         typeof record.progress === 'string' ? parseInt(record.progress, 10) : 0}%
                      </span>
                    </div>
                    {record.skill_level && (
                      <div className="flex justify-between mb-1">
                        <span className="text-gray-300">{t('skillLevelLabel')}:</span>
                        <span className="text-white">{record.skill_level.name_key}</span>
                      </div>
                    )}
                    {record.notes && (
                      <div className="mt-1">
                        <span className="text-gray-300">{t('notesLabel')}:</span>
                        <p className="text-white mt-1">{record.notes}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-purple-900 rounded-lg font-medium transition-colors"
          >
            {t('closeButton')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProgressViewModal;
