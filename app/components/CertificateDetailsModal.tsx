"use client";

import React from 'react';
import { useTranslations } from 'next-intl';
import { CertificateWithSkills } from '../../types/certificate';
import { Child } from '../../types/child';
import Image from 'next/image';

interface CertificateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  certificate: CertificateWithSkills;
  children: Child[];
  locale: string;
  translateSkill?: (skillName: string) => string;
  translateTitle?: (title: string) => string;
}

const CertificateDetailsModal: React.FC<CertificateDetailsModalProps> = ({
  isOpen,
  onClose,
  certificate,
  children,
  locale,
  translateSkill = (s) => s,
  translateTitle = (t) => t
}) => {
  const t = useTranslations('LearningDocumentation.Certificates');

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Get child name
  const childName = children.find(c => c.id === certificate.child_id)?.name || `Child ${certificate.child_id}`;

  // Handle outside click
  const handleOutsideClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale"
      onClick={handleOutsideClick}
    >
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal content */}
      <div
        className="relative bg-purple-900/90 border border-white/10 rounded-xl p-6 w-11/12 max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl z-10"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header with close button */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-yellow-400">{t('certificateDetailsTitle')}</h2>
          <button
            onClick={onClose}
            className="text-gray-300 hover:text-yellow-400 transition-colors"
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Certificate content */}
        <div className="space-y-8">
          {/* Certificate image */}
          {certificate.image_url && (
            <div className="bg-gradient-to-br from-purple-800 to-purple-950 rounded-lg overflow-hidden border border-white/10">
              <div className="relative h-64 w-full">
                <Image
                  src={certificate.image_url}
                  alt={translateTitle(certificate.title_key)}
                  fill
                  className="object-contain"
                  onError={(e) => {
                    e.currentTarget.src = '/images/certificate-placeholder.png';
                  }}
                />
              </div>
            </div>
          )}

          {/* Certificate details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column */}
            <div className="space-y-4">
              {/* Certificate title */}
              <div>
                <h3 className="text-yellow-400 text-sm font-medium mb-1">{t('titleLabel')}</h3>
                <p className="text-white text-xl font-semibold">{translateTitle(certificate.title_key)}</p>
              </div>

              {/* Issue date */}
              <div>
                <h3 className="text-yellow-400 text-sm font-medium mb-1">{t('issueDateLabel')}</h3>
                <p className="text-white">{formatDate(certificate.issue_date)}</p>
              </div>

              {/* Awarded to */}
              <div>
                <h3 className="text-yellow-400 text-sm font-medium mb-1">{t('awardedToText')}</h3>
                <p className="text-white">{childName}</p>
              </div>
            </div>

            {/* Right column */}
            <div className="space-y-4">
              {/* Related skills */}
              {certificate.skills && certificate.skills.length > 0 && (
                <div>
                  <h3 className="text-yellow-400 text-sm font-medium mb-2">{t('relatedSkillsLabel')}</h3>
                  <div className="flex flex-wrap gap-2">
                    {certificate.skills.map((skill, index) => (
                      <span 
                        key={index} 
                        className="inline-block bg-white/10 text-white px-3 py-1.5 rounded-full text-sm"
                      >
                        {translateSkill(skill.name_key)}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Certificate ID */}
              <div>
                <h3 className="text-yellow-400 text-sm font-medium mb-1">{t('certificateIdLabel')}</h3>
                <p className="text-white font-mono">{certificate.id}</p>
              </div>

              {/* Issue date in ISO format for reference */}
              <div>
                <h3 className="text-yellow-400 text-sm font-medium mb-1">{t('issueDateISOLabel')}</h3>
                <p className="text-white font-mono text-sm">{certificate.issue_date}</p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 mt-6 pt-4 border-t border-white/10">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
            >
              {t('closeButton')}
            </button>
            <button
              onClick={() => {
                // This would be implemented if download functionality is needed
                console.log('Download certificate:', certificate.id);
              }}
              className="px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-purple-900 font-medium rounded-lg transition-colors"
            >
              {t('downloadButton')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateDetailsModal;
