'use client';

import React from 'react';
import { Product } from '../../types/products';
import Link from 'next/link';

interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product) => void;
  onToggleFavorite: (productId: number) => void;
  isFavorite: boolean;
  locale?: string;
}

// Default fallback image if no image URL is provided
const DEFAULT_IMAGE = "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=300&h=200&auto=format";

// Add translations for the product card
const textTranslations = {
  en: {
    outOfStock: 'Out of Stock',
    addToCart: 'Add to Cart',
    stock: 'Stock:',
    favorites: {
      add: "Add to favorites",
      remove: "Remove from favorites"
    },
    productType: {
      license: 'License',
      physical: 'Physical',
      internal: 'Internal'
    }
  },
  de: {
    outOfStock: 'Nicht auf Lager',
    addToCart: 'In den Warenkorb',
    stock: 'Bestand:',
    favorites: {
      add: "Zu Favoriten hinzufügen",
      remove: "Aus Favoriten entfernen"
    },
    productType: {
      license: 'Lizenz',
      physical: 'Physisch',
      internal: 'Intern'
    }
  },
  ar: {
    outOfStock: 'نفذ من المخزون',
    addToCart: 'أضف إلى السلة',
    stock: 'المخزون:',
    favorites: {
      add: "أضف إلى المفضلة",
      remove: "إزالة من المفضلة"
    },
    productType: {
      license: 'ترخيص',
      physical: 'مادي',
      internal: 'داخلي'
    }
  },
  he: {
    outOfStock: 'אזל מהמלאי',
    addToCart: 'הוסף לעגלה',
    stock: 'מלאי:',
    favorites: {
      add: "הוסף למועדפים",
      remove: "הסר מהמועדפים"
    },
    productType: {
      license: 'רישיון',
      physical: 'פיזי',
      internal: 'פנימי'
    }
  },
  pt: {
    outOfStock: 'Esgotado',
    addToCart: 'Adicionar ao Carrinho',
    stock: 'Estoque:',
    favorites: {
      add: "Adicionar aos Favoritos",
      remove: "Remover dos Favoritos"
    },
    productType: {
      license: 'Licença',
      physical: 'Físico',
      internal: 'Interno'
    }
  }
};

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart, onToggleFavorite, isFavorite, locale = 'en' }) => {
  const t = textTranslations[locale as keyof typeof textTranslations] || textTranslations.en;
  
  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden shadow-xl transition-all duration-300 hover:shadow-2xl hover:translate-y-[-5px] group">
      <div className="relative h-48 overflow-hidden">
        {/* Favorite button */}
        <button 
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onToggleFavorite(product.id);
          }}
          className="absolute top-3 right-3 z-10 p-2 rounded-full backdrop-blur-sm bg-black/20 text-white hover:bg-black/40 transition-colors"
          aria-label={isFavorite ? t.favorites.remove : t.favorites.add}
        >
          {isFavorite ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          )}
        </button>
        
        {/* Product image */}
        <Link href={`/${locale}/shop/${product.id}`}>
          <img 
            src={
              product.image_url && /^(https?:)?\/\//.test(product.image_url)
                ? product.image_url
                : DEFAULT_IMAGE
            }
            alt={product.name} 
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = DEFAULT_IMAGE;
            }}
          />
        </Link>
        
        {/* Add to cart quick button */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent translate-y-full group-hover:translate-y-0 transition-transform duration-300">
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddToCart(product);
            }}
            className="w-full bg-yellow-400 hover:bg-yellow-500 text-purple-900 font-bold py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
            disabled={product.type === 'physical' && product.quantity === 0}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            {product.type === 'physical' && product.quantity === 0 ? t.outOfStock : t.addToCart}
          </button>
        </div>
      </div>
      
      <Link href={`/${locale}/shop/${product.id}`} className="block p-5">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-bold text-white truncate pr-4">{product.name}</h3>
          <div className="bg-yellow-400 text-purple-900 px-3 py-1 rounded-full font-bold whitespace-nowrap">
            €{product.price.toFixed(2)}
          </div>
        </div>
        
        <div className="flex justify-between items-center mb-3">
          <span className={`text-xs px-2 py-1 rounded-full ${
            product.type === 'license' 
              ? 'bg-blue-500/30 text-blue-300 border border-blue-500/30' 
              : product.type === 'physical'
                ? 'bg-green-500/30 text-green-300 border border-green-500/30'
                : 'bg-gray-500/30 text-gray-300 border border-gray-500/30'
          }`}>
            {t.productType[product.type as keyof typeof t.productType] || product.type.charAt(0).toUpperCase() + product.type.slice(1)}
          </span>
          
          {product.type === 'physical' && product.quantity !== undefined && (
            <span className={`text-xs px-2 py-1 rounded-full ${
              product.quantity > 0
                ? 'bg-purple-500/30 text-purple-300 border border-purple-500/30'
                : 'bg-red-500/30 text-red-300 border border-red-500/30'
            }`}>
              {product.quantity > 0 ? `${t.stock} ${product.quantity}` : t.outOfStock}
            </span>
          )}
        </div>
        
        <p className="text-sm text-white/70 line-clamp-2 h-10">
          {product.product_description || `High-quality ${product.type === 'license' ? 'digital license' : 'educational product'}.`}
        </p>
      </Link>
    </div>
  );
};

export default ProductCard;
