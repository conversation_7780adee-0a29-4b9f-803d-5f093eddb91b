"use client";

import React, { useState, useEffect } from 'react';
import ProgressCard from './ProgressCard';
import ProgressViewModal from './ProgressViewModal';
import { useTranslations } from 'next-intl';
import { skillService } from '../../services/skillService';

interface ProgressCardViewerProps {
  childId: number;
  skillId: number;
  skillName: string;
  skillArea: string;
  skillLevel?: string;
  skillLevelId?: number;
  progress: number;
  notes?: string;
}

interface ProgressHistoryEntry {
  id: number;
  child_id: number;
  skill_id: number;
  skill_level_id: number | null;
  progress: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  skill_level?: {
    id: number;
    name_key: string;
    level_order: number;
  };
}

const ProgressCardViewer: React.FC<ProgressCardViewerProps> = ({
  childId,
  skillId,
  skillName,
  skillArea,
  skillLevel,
  skillLevelId,
  progress,
  notes
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [latestProgressEntry, setLatestProgressEntry] = useState<ProgressHistoryEntry | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const t = useTranslations('LearningDocumentation.Progress');

  // Fetch the latest progress history entry when component mounts
  useEffect(() => {
    const fetchLatestProgressHistory = async () => {
      if (!childId || !skillId) return;

      setIsLoadingHistory(true);
      try {
        const history = await skillService.getChildSkillProgressHistory(childId, skillId);
        if (history && history.length > 0) {
          // Sort by created_at date in descending order to get the latest entry
          const sortedHistory = [...history].sort((a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          setLatestProgressEntry(sortedHistory[0]);
        }
      } catch (err) {
        console.log('Could not fetch progress history:', err);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    fetchLatestProgressHistory();
  }, [childId, skillId]);

  const handleCardClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Ensure progress is a valid number
  let safeProgress = 0;

  if (progress !== undefined && progress !== null) {
    // Convert to number if it's a string
    const numProgress = typeof progress === 'string' ? parseInt(progress, 10) : progress;
    // Ensure it's a valid number between 0 and 100
    safeProgress = typeof numProgress === 'number' && !isNaN(numProgress) ?
      Math.min(100, Math.max(0, numProgress)) : 0;
  }

  // Get the latest progress value from history if available
  const latestProgress = latestProgressEntry ?
    (typeof latestProgressEntry.progress === 'number' ?
      latestProgressEntry.progress :
      typeof latestProgressEntry.progress === 'string' ?
        parseInt(latestProgressEntry.progress, 10) :
        safeProgress) :
    safeProgress;

  // Get the latest notes from history if available
  const latestNotes = latestProgressEntry?.notes || notes;

  // Get the latest skill level from history if available
  const latestSkillLevel = latestProgressEntry?.skill_level?.name_key || skillLevel;
  const latestSkillLevelId = latestProgressEntry?.skill_level_id || skillLevelId;

  // Format the latest update date if available
  const latestUpdateDate = latestProgressEntry?.created_at ?
    new Date(latestProgressEntry.created_at).toLocaleDateString() :
    null;

  return (
    <>
      <ProgressCard
        skillName={skillName}
        skillArea={skillArea}
        skillLevel={latestSkillLevel}
        progress={latestProgress}
        notes={latestNotes}
        isLoading={isLoadingHistory}
        lastUpdated={latestUpdateDate}
        onClick={handleCardClick}
      />

      <ProgressViewModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        childId={childId}
        skillId={skillId}
        skillName={skillName}
        skillArea={skillArea}
        skillLevel={latestSkillLevel}
        skillLevelId={latestSkillLevelId}
        progress={latestProgress}
        notes={latestNotes}
      />
    </>
  );
};

export default ProgressCardViewer;
