'use client';

import React from 'react';
import { useTranslations } from 'next-intl'; // Import useTranslations

interface ImprintModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Translation props removed
}

const ImprintModal: React.FC<ImprintModalProps> = ({
  isOpen,
  onClose,
  // Destructured translation props removed
}) => {
  const t = useTranslations('imprintModal'); // Initialize hook
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal Content */}
      <div className="relative z-10 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl max-w-2xl w-full m-4 max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex justify-between items-center">
            <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {t('title')}
            </h3>
            <button 
              onClick={onClose}
              className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Body */}
        <div className="flex-grow overflow-y-auto p-6">
          <div className="space-y-6">
            <div>
              <h4 className="text-xl font-semibold text-yellow-300 mb-2">{t('companyInfoTitle')}</h4>
              <p className="text-white/90">{t('companyName')}</p>
              <p className="text-white/90">{t('street')}</p>
              <p className="text-white/90">{t('city')}</p>
              <p className="text-white/90">{t('country')}</p>
            </div>
            
            <div>
              <h4 className="text-xl font-semibold text-yellow-300 mb-2">{t('contactTitle')}</h4>
              <p className="text-white/90">{t('phoneLabel')} +49 30 *********</p>
              <p className="text-white/90">{t('emailLabel')} <EMAIL></p>
              <p className="text-white/90">{t('webLabel')} www.booking-parents.com</p>
            </div>
            
            <div>
              <h4 className="text-xl font-semibold text-yellow-300 mb-2">{t('legalTitle')}</h4>
              <p className="text-white/90">{t('registerLabel')} {t('registerValue')}</p>
              <p className="text-white/90">{t('regNumLabel')} {t('regNumValue')}</p>
              <p className="text-white/90">{t('vatIdLabel')} {t('vatIdValue')}</p>
              <p className="text-white/90">{t('directorsLabel')} {t('directorsValue')}</p>
            </div>
            
            <div>
              <h4 className="text-xl font-semibold text-yellow-300 mb-2">{t('regulatoryTitle')}</h4>
              <p className="text-white/90">{t('responsibleLabel')}</p>
              <p className="text-white/90">{t('responsibleName')}</p>
              <p className="text-white/90">{t('street')}</p> {/* Re-use street */}
              <p className="text-white/90">{t('city')}</p> {/* Re-use city */}
              <p className="text-white/90">{t('country')}</p> {/* Re-use country */}
            </div>
            
            <div>
              <h4 className="text-xl font-semibold text-yellow-300 mb-2">{t('disclaimerTitle')}</h4>
              <p className="text-white/90">
                {t('disclaimerText')}
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 bg-black/20 border-t border-white/10">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-200"
            >
              {t('closeButton')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImprintModal; 