"use client";

import React, { useState, useEffect } from 'react';
import { getBrowserTimezone } from '../../utils/timezone';
import { API_URL } from '../../utils/apiConfig';
import { formatPrice } from '../../utils/calendarFormatUtils';
import { useTranslations } from 'next-intl';
import { RecurringOptions } from '../../types/booking';
import { Checkbox } from './ui/checkbox';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Input } from './ui/input';

interface BusinessHour {
  daysOfWeek: number[];
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

interface BookingConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (createdBooking?: any) => void;
  childId: number;
  childName: string;
  parentId: string;
  studioId: number | null;
  roomId: number | null;
  startTime: string;
  endTime: string;
  studioName: string;
  roomName: string;
  roomPrice: number;
  availabilityInfo?: string;
  onUpdateAvailabilityInfo?: (info: string) => void;
  businessHours?: BusinessHour[];
}

// Helper function to check if a time slot is within studio opening hours
const isTimeSlotWithinOpeningHours = (
  startTime: Date,
  endTime: Date,
  businessHours?: BusinessHour[]
): boolean => {
  // If no business hours are defined, return false
  if (!businessHours || businessHours.length === 0) {
    return false;
  }

  // Get the day of the week for the start and end times (0 = Sunday, 1 = Monday, etc.)
  const startDay = startTime.getDay();
  const endDay = endTime.getDay();

  // If the booking spans multiple days, we need to check each day
  if (startDay !== endDay) {
    // For simplicity, we'll just check if both the start and end times are within business hours
    // A more complex implementation would check each day in between as well
    return (
      isTimeWithinBusinessHours(startTime, businessHours) &&
      isTimeWithinBusinessHours(endTime, businessHours)
    );
  }

  // For same-day bookings, check if the entire time slot is within business hours
  return isTimeWithinBusinessHours(startTime, businessHours) &&
         isTimeWithinBusinessHours(endTime, businessHours);
};

// Helper function to check if a specific time is within business hours
const isTimeWithinBusinessHours = (time: Date, businessHours: BusinessHour[]): boolean => {
  const day = time.getDay();
  const hours = time.getHours();
  const minutes = time.getMinutes();

  // Format the time as HH:MM for comparison with business hours
  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

  // Check if the time falls within any of the business hour ranges for this day
  return businessHours.some(hours => {
    // Check if this business hour applies to the current day
    if (!hours.daysOfWeek.includes(day)) {
      return false;
    }

    // Check if the time is within the business hour range
    return timeString >= hours.startTime && timeString <= hours.endTime;
  });
};

const BookingConfirmationModal: React.FC<BookingConfirmationModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  childId,
  childName,
  parentId,
  studioId,
  roomId,
  startTime,
  endTime,
  studioName,
  roomName,
  roomPrice,
  availabilityInfo,
  onUpdateAvailabilityInfo,
  businessHours,
}) => {
  // Get translations
  const t = useTranslations('dashboardPage.bookingConfirmation');

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [childBudget, setChildBudget] = useState<number | null>(null);
  const [budgetLoading, setBudgetLoading] = useState(false);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const [isTimeSlotAvailable, setIsTimeSlotAvailable] = useState(true);

  // Recurring booking state
  const [enableRecurring, setEnableRecurring] = useState<boolean>(false);
  const [recurringOptions, setRecurringOptions] = useState<RecurringOptions>({
    type: 'weekly',
    count: 2, // Default to 2 occurrences (original + 1 repeat)
    endDate: null,
    weeklyDays: [], // Will be set based on the booking day
    total_occurrences: 2 // Ensure we create exactly 2 bookings
  });

  // State for tracking limiting factors for recurring bookings
  const [maxBookingsByBudget, setMaxBookingsByBudget] = useState<number | null>(null);
  const [maxBookingsByCapacity, setMaxBookingsByCapacity] = useState<number | null>(null);
  const [limitingFactor, setLimitingFactor] = useState<'budget' | 'capacity' | null>(null);

  // Calculate booking duration in hours
  const calculateDuration = () => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const durationHours = durationMs / (1000 * 60 * 60);
    return durationHours;
  };

  // Calculate total price
  const calculateTotalPrice = () => {
    const duration = calculateDuration();
    const singleBookingPrice = roomPrice * duration;

    // If recurring is enabled, multiply by the number of occurrences
    if (enableRecurring && recurringOptions.type !== 'none') {
      return singleBookingPrice * recurringOptions.count;
    }

    return singleBookingPrice;
  };

  // Calculate maximum number of bookings possible based on budget
  const calculateMaxBookingsByBudget = () => {
    if (childBudget === null || childBudget <= 0) return 0;

    const duration = calculateDuration();
    const singleBookingPrice = roomPrice * duration;

    if (singleBookingPrice <= 0) return 0;

    // Calculate how many bookings the budget can cover
    const maxBookings = Math.floor(childBudget / singleBookingPrice);
    return Math.max(1, maxBookings); // Ensure at least 1 booking is possible if budget > 0
  };

  // Calculate maximum number of bookings possible based on capacity
  const calculateMaxBookingsByCapacity = async (): Promise<number> => {
    if (!roomId || !startTime || !endTime) return 1;

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('Authentication token not found');
        return 1;
      }

      // Get the room details to check max capacity
      const roomResponse = await fetch(`${API_URL}/rooms/${roomId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!roomResponse.ok) {
        throw new Error(`Failed to fetch room details: ${roomResponse.status}`);
      }

      const roomData = await roomResponse.json();
      const maxSeats = roomData.max_seats || 1;

      // Calculate the dates for potential recurring bookings (up to a reasonable limit)
      const recurringDates: Date[] = [];
      const startDate = new Date(startTime);
      const recurringType = recurringOptions.type || 'weekly';
      const maxPossibleBookings = 10; // Set a reasonable upper limit

      // Add the first booking date
      recurringDates.push(startDate);

      // Calculate the dates for the recurring bookings
      if (recurringType === 'weekly') {
        for (let i = 1; i < maxPossibleBookings; i++) {
          const nextDate = new Date(startDate);
          nextDate.setDate(nextDate.getDate() + (7 * i)); // Add 7 days for each week
          recurringDates.push(nextDate);
        }
      } else if (recurringType === 'biweekly') {
        for (let i = 1; i < maxPossibleBookings; i++) {
          const nextDate = new Date(startDate);
          nextDate.setDate(nextDate.getDate() + (14 * i)); // Add 14 days for each biweek
          recurringDates.push(nextDate);
        }
      }

      // Check each date for availability
      let maxPossibleRecurringBookings = 1; // Start with 1 (the original booking)

      for (let i = 1; i < recurringDates.length; i++) {
        const date = recurringDates[i];
        const dateStartTime = new Date(date);
        const dateEndTime = new Date(date);
        dateEndTime.setHours(new Date(endTime).getHours());
        dateEndTime.setMinutes(new Date(endTime).getMinutes());

        const timezone = getBrowserTimezone();
        const params = new URLSearchParams({
          room_id: roomId.toString(),
          start_date: dateStartTime.toISOString(),
          end_date: dateEndTime.toISOString(),
          timezone: timezone,
          response_timezone: timezone
        });

        const bookingsResponse = await fetch(`${API_URL}/bookings/date-range?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!bookingsResponse.ok) {
          throw new Error(`Failed to fetch bookings: ${bookingsResponse.status}`);
        }

        const bookingsData = await bookingsResponse.json();
        const bookingsArray = Array.isArray(bookingsData) ? bookingsData : [];

        // Count bookings for this exact time slot
        const bookingsCount = bookingsArray.filter((booking: any) => {
          const bookingStart = new Date(booking.start_time).toISOString();
          const bookingEnd = new Date(booking.end_time).toISOString();
          const slotStart = dateStartTime.toISOString();
          const slotEnd = dateEndTime.toISOString();

          // Check for overlap
          return (
            (slotStart >= bookingStart && slotStart < bookingEnd) ||
            (slotEnd > bookingStart && slotEnd <= bookingEnd) ||
            (slotStart <= bookingStart && slotEnd >= bookingEnd)
          );
        }).length;

        // Add 1 to the bookings count to account for the current child's booking
        const totalBookingsCount = bookingsCount + 1;

        // If this date would be fully booked, we've reached the limit
        if (totalBookingsCount > maxSeats) {
          break;
        }

        // This date is available, increment the counter
        maxPossibleRecurringBookings++;
      }

      return maxPossibleRecurringBookings;
    } catch (err) {
      console.error('Error calculating max bookings by capacity:', err);
      return 1; // Default to 1 if there's an error
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // Get locale from URL path
    const locale = window.location.pathname.split('/')[1] || 'en';

    try {
      // Use Intl.DateTimeFormat for localized date formatting
      return new Intl.DateTimeFormat(locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    } catch (error) {
      // Fallback to simple approach if Intl API fails
      const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

      const weekday = weekdays[date.getDay()];
      const month = months[date.getMonth()];
      const day = date.getDate();
      const year = date.getFullYear();

      return `${weekday}, ${month} ${day}, ${year}`;
    }
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    // Get locale from URL params
    const locale = window.location.pathname.split('/')[1] || 'en';
    const language = locale.split('-')[0].toLowerCase();

    // German and most European languages use 24-hour format
    const use24HourFormat = !['en', 'ar', 'hi', 'he', 'fa', 'ur', 'bn'].includes(language);

    try {
      // Use Intl.DateTimeFormat for localized time formatting
      return new Intl.DateTimeFormat(locale, {
        hour: 'numeric',
        minute: '2-digit',
        hour12: !use24HourFormat // Use 24-hour format for German and other European languages
      }).format(date);
    } catch (error) {
      // Fallback to simple approach if Intl API fails
      const hours = date.getHours();
      const minutes = date.getMinutes();

      if (use24HourFormat) {
        // 24-hour format
        const formattedHours = hours < 10 ? `0${hours}` : hours;
        const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
        return `${formattedHours}:${formattedMinutes}`;
      } else {
        // 12-hour format
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
        const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
        return `${formattedHours}:${formattedMinutes} ${ampm}`;
      }
    }
  };

  // Check if time slot is available
  const checkTimeSlotAvailability = async () => {
    if (!roomId || !startTime || !endTime) return;

    setCheckingAvailability(true);
    setError(null);

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('Authentication token not found');
        return;
      }

      // First, get the room details to check max capacity
      const roomResponse = await fetch(`${API_URL}/rooms/${roomId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!roomResponse.ok) {
        throw new Error(`Failed to fetch room details: ${roomResponse.status}`);
      }

      const roomData = await roomResponse.json();
      const maxSeats = roomData.max_seats || 1; // Default to 1 if not specified

      // Now get all bookings for this time slot to check if it's fully booked
      const timezone = getBrowserTimezone();
      const params = new URLSearchParams({
        room_id: roomId.toString(),
        start_date: startTime,
        end_date: endTime,
        timezone: timezone,
        response_timezone: timezone
      });

      const bookingsResponse = await fetch(`${API_URL}/bookings/date-range?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!bookingsResponse.ok) {
        throw new Error(`Failed to fetch bookings: ${bookingsResponse.status}`);
      }

      const bookingsData = await bookingsResponse.json();

      // Ensure bookingsData is always an array, even if the API returns null or undefined
      const bookingsArray = Array.isArray(bookingsData) ? bookingsData : [];

      // Count bookings for this exact time slot
      const bookingsCount = bookingsArray.filter((booking: any) => {
        const bookingStart = new Date(booking.start_time).toISOString();
        const bookingEnd = new Date(booking.end_time).toISOString();
        const slotStart = new Date(startTime).toISOString();
        const slotEnd = new Date(endTime).toISOString();

        // Check for overlap
        return (
          (slotStart >= bookingStart && slotStart < bookingEnd) ||
          (slotEnd > bookingStart && slotEnd <= bookingEnd) ||
          (slotStart <= bookingStart && slotEnd >= bookingEnd)
        );
      }).length;

      // Add 1 to the bookings count to account for the current child's booking
      // This ensures we're checking the true capacity including the current child
      const totalBookingsCount = bookingsCount + 1;

      // A time slot is only unavailable if it's fully booked (total bookings count > max seats)
      const isFullyBooked = totalBookingsCount > maxSeats;
      setIsTimeSlotAvailable(!isFullyBooked);

      // Always show the availability info, even if there are no other bookings
      // This ensures the orange indicator always includes the current child
      {
        // Set availability info based on locale
        const locale = window.location.pathname.split('/')[1] || 'en';

        // Calculate the total bookings count (including the current child)
        const displayBookingsCount = totalBookingsCount;

        // Calculate the remaining seats after this booking
        const remainingSeats = maxSeats - displayBookingsCount;

        if (locale === 'de') {
          const personText = displayBookingsCount === 1 ? 'Person' : 'Personen';
          // Set the availability info directly in the component state
          if (onUpdateAvailabilityInfo) {
            onUpdateAvailabilityInfo(`${displayBookingsCount} ${personText} bereits gebucht. Noch ${remainingSeats} Plätze verfügbar.`);
          }
        } else {
          const personText = displayBookingsCount === 1 ? 'person' : 'people';
          // Set the availability info directly in the component state
          if (onUpdateAvailabilityInfo) {
            onUpdateAvailabilityInfo(`${displayBookingsCount} ${personText} already booked. ${remainingSeats} seats available.`);
          }
        }
      }

      if (isFullyBooked) {
        setError(t('timeSlotBooked'));
      }
    } catch (err: any) {
      console.error('Error checking time slot availability:', err);
      // Set a default - assume available if we can't check
      setIsTimeSlotAvailable(true);
    } finally {
      setCheckingAvailability(false);
    }
  };

  // Fetch child's budget and check time slot availability
  useEffect(() => {
    if (!isOpen || !childId) return;

    const fetchChildBudget = async () => {
      setBudgetLoading(true);
      setError(null);

      try {
        const token = localStorage.getItem('userToken');
        if (!token) {
          setError('Authentication token not found');
          return;
        }

        const response = await fetch(`${API_URL}/children/${childId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch child data: ${response.status}`);
        }

        const childData = await response.json();
        setChildBudget(childData.budget);

        // Calculate max bookings by budget once we have the budget
        const maxByBudget = Math.floor(childData.budget / (roomPrice * calculateDuration()));
        setMaxBookingsByBudget(Math.max(1, maxByBudget)); // Ensure at least 1 booking is possible
      } catch (err: any) {
        // Error fetching child budget
        setError(err.message || 'Failed to fetch child budget');
      } finally {
        setBudgetLoading(false);
      }
    };

    // Calculate the maximum number of bookings based on capacity
    const calculateMaxBookings = async () => {
      try {
        // Calculate max bookings by capacity
        const maxByCapacity = await calculateMaxBookingsByCapacity();
        setMaxBookingsByCapacity(maxByCapacity);

        // Wait for budget calculation to complete
        const maxByBudget = maxBookingsByBudget || calculateMaxBookingsByBudget();

        // Determine the limiting factor and set the default count
        if (maxByBudget <= maxByCapacity) {
          setLimitingFactor('budget');
          setRecurringOptions(prev => ({
            ...prev,
            count: maxByBudget,
            total_occurrences: maxByBudget
          }));
        } else {
          setLimitingFactor('capacity');
          setRecurringOptions(prev => ({
            ...prev,
            count: maxByCapacity,
            total_occurrences: maxByCapacity
          }));
        }
      } catch (err) {
        console.error('Error calculating max bookings:', err);
      }
    };

    // Check if the booking is within studio opening hours
    if (startTime && endTime && businessHours && businessHours.length > 0) {
      const startDate = new Date(startTime);
      const endDate = new Date(endTime);
      const isWithinOpeningHours = isTimeSlotWithinOpeningHours(startDate, endDate, businessHours);

      if (!isWithinOpeningHours) {
        // Get locale from URL path
        const locale = window.location.pathname.split('/')[1] || 'en';

        if (locale === 'de') {
          setError('Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.');
        } else {
          setError('Bookings are only possible within the studio opening hours.');
        }
        setIsTimeSlotAvailable(false);
        return;
      }
    }

    fetchChildBudget();
    checkTimeSlotAvailability();
    calculateMaxBookings();
  }, [isOpen, childId, roomId, startTime, endTime, businessHours]);

  // Check if recurring bookings would exceed room capacity
  const checkRecurringBookingsCapacity = async (recurringOptions: RecurringOptions): Promise<boolean> => {
    if (!roomId || !startTime || !endTime) return false;

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('Authentication token not found');
        return false;
      }

      // First, get the room details to check max capacity
      const roomResponse = await fetch(`${API_URL}/rooms/${roomId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!roomResponse.ok) {
        throw new Error(`Failed to fetch room details: ${roomResponse.status}`);
      }

      const roomData = await roomResponse.json();
      const maxSeats = roomData.max_seats || 1; // Default to 1 if not specified

      // Calculate the dates for all recurring bookings
      const recurringDates: Date[] = [];
      const startDate = new Date(startTime);

      // Add the first booking date
      recurringDates.push(startDate);

      // Calculate the dates for the recurring bookings
      if (recurringOptions.type === 'weekly') {
        for (let i = 1; i < recurringOptions.count; i++) {
          const nextDate = new Date(startDate);
          nextDate.setDate(nextDate.getDate() + (7 * i)); // Add 7 days for each week
          recurringDates.push(nextDate);
        }
      } else if (recurringOptions.type === 'biweekly') {
        for (let i = 1; i < recurringOptions.count; i++) {
          const nextDate = new Date(startDate);
          nextDate.setDate(nextDate.getDate() + (14 * i)); // Add 14 days for each biweek
          recurringDates.push(nextDate);
        }
      }

      // Check each date for availability
      for (const date of recurringDates) {
        const dateStartTime = new Date(date);
        const dateEndTime = new Date(date);
        dateEndTime.setHours(new Date(endTime).getHours());
        dateEndTime.setMinutes(new Date(endTime).getMinutes());

        const timezone = getBrowserTimezone();
        const params = new URLSearchParams({
          room_id: roomId.toString(),
          start_date: dateStartTime.toISOString(),
          end_date: dateEndTime.toISOString(),
          timezone: timezone,
          response_timezone: timezone
        });

        const bookingsResponse = await fetch(`${API_URL}/bookings/date-range?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!bookingsResponse.ok) {
          throw new Error(`Failed to fetch bookings: ${bookingsResponse.status}`);
        }

        const bookingsData = await bookingsResponse.json();
        const bookingsArray = Array.isArray(bookingsData) ? bookingsData : [];

        // Count bookings for this exact time slot
        const bookingsCount = bookingsArray.filter((booking: any) => {
          const bookingStart = new Date(booking.start_time).toISOString();
          const bookingEnd = new Date(booking.end_time).toISOString();
          const slotStart = dateStartTime.toISOString();
          const slotEnd = dateEndTime.toISOString();

          // Check for overlap
          return (
            (slotStart >= bookingStart && slotStart < bookingEnd) ||
            (slotEnd > bookingStart && slotEnd <= bookingEnd) ||
            (slotStart <= bookingStart && slotEnd >= bookingEnd)
          );
        }).length;

        // Add 1 to the bookings count to account for the current child's booking
        const totalBookingsCount = bookingsCount + 1;

        // If this date would be fully booked, return false
        if (totalBookingsCount > maxSeats) {
          return false;
        }
      }

      // All dates are available
      return true;
    } catch (err) {
      console.error('Error checking recurring bookings capacity:', err);
      return false;
    }
  };

  // Handle booking confirmation
  const handleConfirmBooking = async () => {
    if (!studioId || !roomId) {
      setError('Studio or room not selected');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('userToken');
      if (!token) {
        setError('Authentication token not found');
        return;
      }

      // Check if the booking is within studio opening hours
      if (startTime && endTime && businessHours && businessHours.length > 0) {
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);
        const isWithinOpeningHours = isTimeSlotWithinOpeningHours(startDate, endDate, businessHours);

        if (!isWithinOpeningHours) {
          // Get locale from URL path
          const locale = window.location.pathname.split('/')[1] || 'en';

          if (locale === 'de') {
            throw new Error('Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.');
          } else {
            throw new Error('Bookings are only possible within the studio opening hours.');
          }
        }
      }

      // If this is a recurring booking, check if all recurring bookings would be within capacity
      if (enableRecurring && recurringOptions.type !== 'none' && recurringOptions.count > 1) {
        const allRecurringBookingsAvailable = await checkRecurringBookingsCapacity(recurringOptions);

        if (!allRecurringBookingsAvailable) {
          // Get locale from URL path
          const locale = window.location.pathname.split('/')[1] || 'en';

          throw new Error(t('recurringBookingCapacityExceeded'));
        }
      }

      const totalPrice = calculateTotalPrice();

      // Check if child has enough budget
      if (childBudget !== null && totalPrice > childBudget) {
        setError('Insufficient budget for this booking');
        setLoading(false);
        return;
      }

      const timezone = getBrowserTimezone();
      // Prepare booking data
      let bookingData;
      let apiEndpoint;

      if (enableRecurring && recurringOptions.type !== 'none') {
        // For recurring bookings, use the batch endpoint

        // Make a copy of the recurring options
        const recurringData = { ...recurringOptions };

        // For weekly or biweekly recurrence, always use the day of the week from the original booking
        if (recurringData.type === 'weekly' || recurringData.type === 'biweekly') {
          // Get the day of the week of the booking (0 = Sunday, 1 = Monday, etc.)
          // Use local timezone to ensure consistency with the displayed calendar
          const bookingDate = new Date(startTime);
          const bookingDay = bookingDate.getDay(); // Use local day instead of UTC day (0 = Sunday in JavaScript)

          console.log(`Original booking day: ${bookingDay} (JS format, 0=Sunday), date: ${bookingDate.toISOString()}`);

          // Set the weeklyDays to only include the original booking day
          recurringData.weeklyDays = [bookingDay];

          // Make sure the API knows to create the second booking exactly one week later
          // by setting the interval property
          recurringData.interval = recurringData.type === 'weekly' ? 1 : 2;

          // Explicitly set the start date to ensure the first recurrence is correct
          recurringData.start_date = startTime;

          // Set the total number of occurrences (including the original booking)
          // This ensures we get exactly the number of bookings specified
          recurringData.total_occurrences = recurringData.count;

          // Add explicit timezone information to ensure consistency
          recurringData.timezone = timezone;
          recurringData.response_timezone = timezone;
        }

        bookingData = {
          bookings: [{
            parent_id: parseInt(parentId),
            child_id: childId,
            start_time: startTime,
            end_time: endTime,
            room_id: roomId,
            price: totalPrice / recurringData.count, // Price per booking
            learning_studios_id: studioId
          }],
          recurring: recurringData,
          timezone: timezone,
          response_timezone: timezone
        };
        apiEndpoint = `${API_URL}/bookings/batch`;
      } else {
        // For single bookings, use the regular endpoint
        bookingData = {
          parent_id: parseInt(parentId),
          child_id: childId,
          start_time: startTime,
          end_time: endTime,
          room_id: roomId,
          price: totalPrice,
          learning_studios_id: studioId,
          timezone: timezone,
          response_timezone: timezone
        };
        apiEndpoint = `${API_URL}/bookings`;
      }

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
        credentials: 'include'
      });



      if (!response.ok) {
        const errorData = await response.json();

        // Handle specific error codes
        if (response.status === 409) {
          // Check if we have detailed conflict information
          if (errorData.description && typeof errorData.description === 'object' && errorData.description.error_type === 'booking_conflict') {
            const details = errorData.description.details;
            // Create a more informative error message with the conflicting time
            const formattedMessage = t('bookingConflictDetailed', {
              childName: details.child_name,
              roomName: details.room_name,
              requestedTime: details.requested_time,
              conflictingTime: details.conflicting_time,
              defaultMessage: `Booking conflict detected for ${details.child_name} in ${details.room_name}.
              Requested time: ${details.requested_time} conflicts with existing booking at ${details.conflicting_time}.`
            });
            throw new Error(formattedMessage);
          } else {
            // Fallback to generic message
            throw new Error(t('timeSlotBooked'));
          }
        } else if (errorData.message) {
          throw new Error(errorData.message);
        } else if (errorData.description && typeof errorData.description === 'string') {
          throw new Error(errorData.description);
        } else {
          throw new Error(t('bookingFailed'));
        }
      }

      // Get the booking data from the response
      const responseData = await response.json();

      // For batch bookings (recurring), the API returns an array of bookings
      // For single bookings, it returns a single booking object
      // We need to handle both cases
      const createdBooking = responseData;

      // Booking successful - pass the created booking to the success handler
      onSuccess(createdBooking);
    } catch (err: any) {

      setError(err.message || 'Failed to create booking');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const totalPrice = calculateTotalPrice();
  const remainingBudget = childBudget !== null ? childBudget - totalPrice : null;
  const hasEnoughBudget = remainingBudget !== null && remainingBudget >= 0;
  // Only allow booking if the time slot is available, the user has enough budget, and we're not in a loading state
  // Also check that if recurring is enabled, a valid type is selected
  const canBook = hasEnoughBudget && isTimeSlotAvailable && !loading && !budgetLoading && !checkingAvailability && !error
    && (!enableRecurring || (enableRecurring && recurringOptions.type !== 'none'));

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal Content */}
      <div className="relative z-10 bg-gradient-to-br from-amber-900/90 to-yellow-800/90 backdrop-blur-xl border border-white/10 rounded-2xl p-6 md:p-8 shadow-2xl max-w-lg w-full m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-amber-500">
            {t('title')}
          </h3>
          <button
            onClick={onClose}
            className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white"
            disabled={loading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          {/* Booking Details */}
          <div className="bg-black/20 rounded-xl p-4 border border-white/10">
            <h4 className="text-lg font-semibold text-amber-300 mb-3">{t('bookingDetails')}</h4>

            <div className="space-y-2 text-white">
              <div className="flex justify-between">
                <span className="text-white/70">{t('child')}:</span>
                <span className="font-medium">{childName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t('studio')}:</span>
                <span className="font-medium">{studioName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t('room')}:</span>
                <span className="font-medium">{roomName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t('date')}:</span>
                <span className="font-medium">{formatDate(startTime)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t('time')}:</span>
                <span className="font-medium">{formatTime(startTime)} - {formatTime(endTime)}</span>
              </div>

              {/* Availability Status */}
              {checkingAvailability ? (
                <div className="flex justify-between items-center mt-1 pt-1 border-t border-white/10">
                  <span className="text-white/70">{t('availability')}:</span>
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-amber-300 mr-2"></div>
                    {t('checking')}
                  </span>
                </div>
              ) : (
                <div className="flex flex-col mt-1 pt-1 border-t border-white/10">
                  <div className="flex justify-between items-center">
                    <span className="text-white/70">{t('availability')}:</span>
                    {isTimeSlotAvailable ? (
                      <span className="flex items-center text-green-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        {t('available')}
                      </span>
                    ) : (
                      <span className="flex items-center text-red-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                        {t('notAvailable')}
                      </span>
                    )}
                  </div>

                  {/* Show availability info if available */}
                  {availabilityInfo && isTimeSlotAvailable && (
                    <div className="mt-1 text-sm text-amber-300/90 bg-amber-900/20 p-2 rounded-md">
                      <div className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>{availabilityInfo}</span>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-white/70">{t('duration')}:</span>
                <span className="font-medium">
                  {window.location.pathname.split('/')[1] === 'de'
                    ? calculateDuration().toFixed(1).replace('.', ',')
                    : calculateDuration().toFixed(1)} {t('hours')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t('pricePerHour')}:</span>
                <span className="font-medium">{formatPrice(roomPrice, window.location.pathname.split('/')[1] || 'en')}</span>
              </div>
              <div className="flex justify-between pt-2 border-t border-white/10 text-lg">
                <span className="font-semibold text-amber-300">{t('totalPrice')}:</span>
                <span className="font-bold text-amber-300">{formatPrice(totalPrice, window.location.pathname.split('/')[1] || 'en')}</span>
              </div>
            </div>
          </div>

          {/* Recurring Booking Options */}
          <div className="bg-black/20 rounded-xl p-4 border border-white/10">
            <h4 className="text-lg font-semibold text-amber-300 mb-3">{t('recurringBooking')}</h4>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enable-recurring"
                  checked={enableRecurring}
                  onCheckedChange={(checked) => {
                    setEnableRecurring(Boolean(checked));
                    // Set type to weekly if enabled, reset if disabled
                    if (checked) {
                      // Calculate the default count based on limiting factors
                      let defaultCount = 2;

                      if (maxBookingsByBudget !== null && maxBookingsByCapacity !== null) {
                        // Use the lower of the two limits
                        defaultCount = Math.min(maxBookingsByBudget, maxBookingsByCapacity);

                        // Set the limiting factor
                        if (maxBookingsByBudget <= maxBookingsByCapacity) {
                          setLimitingFactor('budget');
                        } else {
                          setLimitingFactor('capacity');
                        }
                      } else if (maxBookingsByBudget !== null) {
                        defaultCount = maxBookingsByBudget;
                        setLimitingFactor('budget');
                      } else if (maxBookingsByCapacity !== null) {
                        defaultCount = maxBookingsByCapacity;
                        setLimitingFactor('capacity');
                      }

                      // Ensure at least 1 booking
                      defaultCount = Math.max(1, defaultCount);

                      setRecurringOptions({
                        ...recurringOptions,
                        type: 'weekly',
                        count: defaultCount,
                        total_occurrences: defaultCount
                      });
                    } else {
                      setRecurringOptions({
                        type: 'none',
                        count: 2,
                        endDate: null,
                        total_occurrences: 2
                      });
                      setLimitingFactor(null);
                    }
                  }}
                  className="border-gray-400 data-[state=checked]:bg-amber-400 data-[state=checked]:text-purple-800"
                />
                <Label htmlFor="enable-recurring" className="text-white">{t('enableRecurringBooking')}</Label>
              </div>

              {enableRecurring && (
                <div className="space-y-4 pl-6 border-l border-white/10">
                  <div>
                    <Label htmlFor="recurrence-type" className="text-gray-300 block mb-1">{t('recurrenceType')}</Label>
                    <Select
                      value={recurringOptions.type}
                      onValueChange={(value: RecurringOptions['type']) => setRecurringOptions({
                        ...recurringOptions,
                        type: value,
                        total_occurrences: recurringOptions.count // Ensure total_occurrences matches count
                      })}
                    >
                      <SelectTrigger id="recurrence-type" className="w-full bg-black/30 border-white/20 text-white">
                        <SelectValue placeholder={t('selectType')} />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700 text-white">
                        <SelectItem value="weekly">{t('weekly')}</SelectItem>
                        <SelectItem value="biweekly">{t('biweekly')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="recurrence-count" className="text-gray-300 block mb-1">{t('numberOfOccurrences')}</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="recurrence-count"
                          type="number"
                          min="1"
                          max={limitingFactor === 'budget' ? maxBookingsByBudget || 10 :
                               limitingFactor === 'capacity' ? maxBookingsByCapacity || 10 : 10}
                          value={recurringOptions.count}
                          onChange={(e) => {
                            const newCount = Math.max(1, parseInt(e.target.value) || 1);

                            // Enforce the limits based on the limiting factor
                            let limitedCount = newCount;
                            if (limitingFactor === 'budget' && maxBookingsByBudget !== null) {
                              limitedCount = Math.min(newCount, maxBookingsByBudget);
                            } else if (limitingFactor === 'capacity' && maxBookingsByCapacity !== null) {
                              limitedCount = Math.min(newCount, maxBookingsByCapacity);
                            }

                            setRecurringOptions({
                              ...recurringOptions,
                              count: limitedCount,
                              total_occurrences: limitedCount // Update total_occurrences to match count
                            });
                          }}
                          className="w-full bg-black/30 border-white/20 text-white"
                        />
                      </div>

                      {/* Display the limiting factor */}
                      {limitingFactor && (
                        <div className="mt-2 text-sm text-amber-300/80">
                          {limitingFactor === 'budget' ? (
                            <span>({t('limitedByBudget', { defaultMessage: 'Limited by budget' })})</span>
                          ) : (
                            <span>({t('limitedByCapacity', { defaultMessage: 'Limited by available space' })})</span>
                          )}
                        </div>
                      )}
                    </div>

                    {recurringOptions.type === 'weekly' && (
                      <div className="text-sm text-amber-300/80 bg-amber-900/20 p-2 rounded-md">
                        <div className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          <span>{t('weeklyRecurrenceInfo', { defaultMessage: 'The booking will repeat weekly on the same day for the specified number of weeks.' })}</span>
                        </div>
                      </div>
                    )}

                    {recurringOptions.type === 'biweekly' && (
                      <div className="text-sm text-amber-300/80 bg-amber-900/20 p-2 rounded-md">
                        <div className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          <span>{t('biweeklyRecurrenceInfo', { defaultMessage: 'The booking will repeat every two weeks on the same day for the specified number of occurrences.' })}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Budget Information */}
          <div className="bg-black/20 rounded-xl p-4 border border-white/10">
            <h4 className="text-lg font-semibold text-amber-300 mb-3">{t('budgetInformation')}</h4>

            {budgetLoading ? (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-300"></div>
              </div>
            ) : childBudget !== null ? (
              <div className="space-y-2 text-white">
                <div className="flex justify-between">
                  <span className="text-white/70">{t('currentBudget')}:</span>
                  <span className="font-medium">{formatPrice(childBudget, window.location.pathname.split('/')[1] || 'en')}</span>
                </div>

                {enableRecurring && recurringOptions.type !== 'none' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-white/70">{t('singleBookingCost', { defaultMessage: 'Single Booking Cost' })}:</span>
                      <span className="font-medium">{formatPrice(calculateTotalPrice() / recurringOptions.count, window.location.pathname.split('/')[1] || 'en')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">{t('numberOfBookings', { defaultMessage: 'Number of Bookings' })}:</span>
                      <span className="font-medium">{recurringOptions.count}</span>
                    </div>
                  </>
                )}

                <div className="flex justify-between">
                  <span className="text-white/70">{t('bookingCost')}:</span>
                  <span className="font-medium">{formatPrice(totalPrice, window.location.pathname.split('/')[1] || 'en')}</span>
                </div>
                <div className="flex justify-between pt-2 border-t border-white/10 text-lg">
                  <span className="font-semibold text-amber-300">{t('remainingBudget')}:</span>
                  <span className={`font-bold ${hasEnoughBudget ? 'text-green-400' : 'text-red-400'}`}>
                    {formatPrice(remainingBudget || 0, window.location.pathname.split('/')[1] || 'en')}
                  </span>
                </div>

                {!hasEnoughBudget && (
                  <div className="mt-2 p-3 bg-red-900/50 border border-red-500/30 rounded-lg text-red-300 text-sm">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <p className="font-medium">{t('insufficientBudget')}</p>
                        <p>{t('insufficientBudgetMessage')}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-300 py-2">
                {t('errorLoadingBudget')}
              </div>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-4 bg-red-900/70 border border-red-500/50 rounded-lg text-white text-sm animate-pulse">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 flex-shrink-0 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-medium text-lg text-red-200 mb-1">{t('bookingFailed')}</p>
                  <p className="text-white/90">{error}</p>
                  <p className="mt-2 text-red-200/80 text-xs">{t('tryAgainMessage')}</p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-xl transition-all duration-200"
              disabled={loading}
            >
              {t('cancelButton')}
            </button>
            <button
              type="button"
              onClick={handleConfirmBooking}
              className={`px-6 py-3 ${
                canBook
                  ? 'bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-amber-900'
                  : 'bg-gray-600 text-white cursor-not-allowed'
              } font-bold rounded-xl transition-all duration-200 transform hover:scale-[1.02] flex items-center justify-center`}
              disabled={!canBook}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-amber-900 mr-2"></div>
                  {t('processing')}
                </>
              ) : (
                t('confirmButton')
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmationModal;
