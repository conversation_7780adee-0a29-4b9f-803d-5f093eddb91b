import React from 'react';
import { useTranslations } from 'next-intl'; // Import useTranslations

interface PrivacyModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Translation props removed
}

const PrivacyModal: React.FC<PrivacyModalProps> = ({
  isOpen,
  onClose,
  // Destructured translation props removed
}) => {
  const t = useTranslations('privacyModal'); // Initialize hook
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center animate-fadeInScale">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal Content */}
      <div className="relative z-10 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl border border-white/10 rounded-2xl p-6 md:p-8 shadow-2xl max-w-3xl w-full m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6 sticky top-0 bg-gradient-to-br from-purple-900/90 to-indigo-900/90 backdrop-blur-xl py-2 -mt-2 -mx-2 px-2">
          <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
            {t('title')}
          </h3>
          <button 
            onClick={onClose}
            className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white/70 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="space-y-6 text-white/90">
          {/* Introduction */}
          <div>
            <p className="mb-4">
              {t('lastUpdated')} {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })} {/* Keep date format for now */}
            </p>
            <p className="mb-4">
              {t('intro')}
            </p>
          </div>
          
          {/* Collection of Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              {t('infoCollectedTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('infoCollectedIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li><span className="text-yellow-200 font-medium">{t('personalDataTitle')}</span> {t('personalDataDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('childInfoTitle')}</span> {t('childInfoDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('paymentDataTitle')}</span> {t('paymentDataDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('usageDataTitle')}</span> {t('usageDataDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('cookiesTitle')}</span> {t('cookiesDesc')}</li>
              </ul>
            </div>
          </div>
          
          {/* Use of Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
              </svg>
              {t('howWeUseTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('howWeUseIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li>{t('howWeUseLi1')}</li>
                <li>{t('howWeUseLi2')}</li>
                <li>{t('howWeUseLi3')}</li>
                <li>{t('howWeUseLi4')}</li>
                <li>{t('howWeUseLi5')}</li>
                <li>{t('howWeUseLi6')}</li>
                <li>{t('howWeUseLi7')}</li>
                <li>{t('howWeUseLi8')}</li>
                <li>{t('howWeUseLi9')}</li>
              </ul>
            </div>
          </div>
          
          {/* Information Sharing */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
              </svg>
              {t('sharingTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('sharingIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li><span className="text-yellow-200 font-medium">{t('sharingServiceProvidersTitle')}</span> {t('sharingServiceProvidersDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('sharingStudiosTitle')}</span> {t('sharingStudiosDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('sharingBusinessTransfersTitle')}</span> {t('sharingBusinessTransfersDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('sharingConsentTitle')}</span> {t('sharingConsentDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('sharingLegalTitle')}</span> {t('sharingLegalDesc')}</li>
              </ul>
            </div>
          </div>
          
          {/* Security */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {t('securityTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('securityIntro1')}</p>
              <p>{t('securityIntro2')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li>{t('securityLi1')}</li>
                <li>{t('securityLi2')}</li>
                <li>{t('securityLi3')}</li>
                <li>{t('securityLi4')}</li>
                <li>{t('securityLi5')}</li>
              </ul>
            </div>
          </div>
          
          {/* Children's Privacy */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
              </svg>
              {t('childrenTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('childrenIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li>{t('childrenLi1')}</li>
                <li>{t('childrenLi2')}</li>
                <li>{t('childrenLi3')}</li>
                <li>{t('childrenLi4')}</li>
                <li>{t('childrenLi5')}</li>
              </ul>
            </div>
          </div>
          
          {/* Your Rights */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              {t('rightsTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('rightsIntro')}</p>
              <ul className="list-disc list-inside space-y-2 text-white/80 pl-2">
                <li><span className="text-yellow-200 font-medium">{t('rightsAccessTitle')}</span> {t('rightsAccessDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('rightsRectificationTitle')}</span> {t('rightsRectificationDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('rightsErasureTitle')}</span> {t('rightsErasureDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('rightsRestrictTitle')}</span> {t('rightsRestrictDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('rightsPortabilityTitle')}</span> {t('rightsPortabilityDesc')}</li>
                <li><span className="text-yellow-200 font-medium">{t('rightsObjectTitle')}</span> {t('rightsObjectDesc')}</li>
              </ul>
              <p className="mt-4">{t('rightsContact')}</p>
            </div>
          </div>
          
          {/* Contact Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              {t('contactTitle')}
            </h4>
            <div className="bg-white/5 rounded-xl p-4">
              <p className="mb-3">{t('contactIntro')}</p>
              <ul className="list-none space-y-2 text-white pl-2">
                <li><span className="text-yellow-200 font-medium">{t('contactEmail')}</span> <EMAIL></li>
                <li><span className="text-yellow-200 font-medium">{t('contactAddress')}</span> Booking Parents, Inc., 123 Learning Street, Innovation City, 94105</li>
                <li><span className="text-yellow-200 font-medium">{t('contactPhone')}</span> +****************</li>
              </ul>
            </div>
          </div>
          
          {/* Accept Button */}
          <div className="flex justify-center pt-4">
            <button 
              onClick={onClose}
              className="px-6 py-3 rounded-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold transition-all duration-200 transform hover:scale-[1.02]"
            >
              {t('acceptButton')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyModal; 