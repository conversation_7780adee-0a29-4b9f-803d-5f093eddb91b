"use client";

import React from 'react';

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  showViewBookingsButton?: boolean;
  onViewBookings?: () => void;
  locale?: string; // Add locale prop
}

const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  showViewBookingsButton = false,
  onViewBookings,
  locale = 'en' // Default locale
}) => {
  if (!isOpen) return null;

  // Translations object
  const translations = {
    en: {
      viewBookings: "View Bookings",
      close: "Close"
    },
    de: {
      viewBookings: "Buchungen anzeigen",
      close: "Schließen"
    },
    es: {
      viewBookings: "Ver Reservas",
      close: "Cerrar"
    },
    fr: {
      viewBookings: "Voir les Réservations",
      close: "Fermer"
    },
    zh: {
      viewBookings: "查看预订",
      close: "关闭"
    },
    ja: {
      viewBookings: "予約を表示",
      close: "閉じる"
    }
  };

  const t = translations[locale as keyof typeof translations] || translations.en;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-red-500">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-red-500">{title}</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
            aria-label={t.close}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="mb-6">
          {/* Display message with proper line breaks */}
          {message.split('\n').map((line, index) => (
            <React.Fragment key={index}>
              <p className="text-white">{line}</p>
              {line === '' && <br />}
            </React.Fragment>
          ))}
        </div>
        <div className="flex justify-end space-x-3">
          {showViewBookingsButton && onViewBookings && (
            <button
              onClick={() => {
                onViewBookings();
                onClose();
              }}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              {t.viewBookings}
            </button>
          )}
          <button
            onClick={onClose}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            {t.close}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorModal;