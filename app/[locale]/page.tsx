import { redirect } from 'next/navigation';
import { isAuthenticated } from '@/utils/auth';

// Default landing page for each locale
export default function LocaleHome({ params }: { params: { locale: string } }) {
  // Redirect authenticated users to dashboard, otherwise to login
  if (isAuthenticated()) {
    redirect(`/${params.locale}/dashboard`);
  } else {
    redirect(`/${params.locale}/login`);
  }
}
