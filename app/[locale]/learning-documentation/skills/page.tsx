"use client";

import React, { useState, useEffect } from 'react';
import DashboardLayout from '../../../components/DashboardLayout';
import LearningDocumentationLayout from '../../../components/LearningDocumentationLayout';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import ProgressCardViewer from '../../../components/ProgressCardViewer';
import { useTranslations } from 'next-intl';

// Import services
import { skillService } from '../../../../services/skillService';
import { childService } from '../../../../services/childService';

// Import types
import { Child } from '../../../../types/child';
import {
  SkillArea as SkillAreaType,
  SkillProgressWithDetails,
  SkillAreaWithSkills,
  SkillWithProgress,
  SkillLevel
} from '../../../../types/skill';

// Icons for skill areas
const getSkillAreaIcon = (iconName: string) => {
  switch (iconName) {
    case 'code':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    case 'game':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
        </svg>
      );
    case 'art':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
        </svg>
      );
    case 'robot':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
        </svg>
      );
    default:
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
        </svg>
      );
  }
};

// Get color theme for skill area
const getSkillAreaColor = (colorTheme: string) => {
  switch (colorTheme) {
    case 'blue':
      return 'from-blue-400 to-blue-600';
    case 'purple':
      return 'from-purple-400 to-purple-600';
    case 'pink':
      return 'from-pink-400 to-pink-600';
    case 'green':
      return 'from-green-400 to-green-600';
    case 'red':
      return 'from-red-400 to-red-600';
    case 'orange':
      return 'from-orange-400 to-orange-600';
    case 'yellow':
      return 'from-yellow-400 to-yellow-600';
    default:
      return 'from-gray-400 to-gray-600';
  }
};

interface SkillArea {
  id: number;
  name: string;
  icon: React.ReactNode;
  color: string;
  skills: {
    id: number;
    name: string;
    progress: number;
    level: string;
  }[]
}

interface SkillLevels {
  Beginner: string;
  Intermediate: string;
  Advanced: string;
  [key: string]: string;
}

// Mock data for skills in English
const mockSkillCategoriesEN: SkillArea[] = [
  {
    id: 1,
    name: 'Web Development',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-blue-400 to-blue-600',
    skills: [
      { id: 101, name: 'HTML', progress: 85, level: 'Advanced' },
      { id: 102, name: 'CSS', progress: 75, level: 'Intermediate' },
      { id: 103, name: 'JavaScript', progress: 60, level: 'Intermediate' },
      { id: 104, name: 'Responsive Design', progress: 70, level: 'Intermediate' },
      { id: 105, name: 'React Basics', progress: 40, level: 'Beginner' },
    ]
  },
  {
    id: 2,
    name: 'Game Design',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
      </svg>
    ),
    color: 'from-purple-400 to-purple-600',
    skills: [
      { id: 201, name: 'Game Mechanics', progress: 65, level: 'Intermediate' },
      { id: 202, name: 'Character Design', progress: 50, level: 'Beginner' },
      { id: 203, name: 'Level Design', progress: 40, level: 'Beginner' },
      { id: 204, name: 'Scratch Programming', progress: 80, level: 'Advanced' },
    ]
  },
  {
    id: 3,
    name: 'Digital Art',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-pink-400 to-pink-600',
    skills: [
      { id: 301, name: 'Basic Drawing', progress: 70, level: 'Intermediate' },
      { id: 302, name: 'Digital Coloring', progress: 60, level: 'Intermediate' },
      { id: 303, name: 'Animation Basics', progress: 45, level: 'Beginner' },
      { id: 304, name: 'Character Design', progress: 55, level: 'Intermediate' },
    ]
  },
  {
    id: 4,
    name: 'Robotics',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-green-400 to-green-600',
    skills: [
      { id: 401, name: 'Basic Circuits', progress: 50, level: 'Beginner' },
      { id: 402, name: 'Motor Control', progress: 30, level: 'Beginner' },
      { id: 403, name: 'Sensors', progress: 20, level: 'Beginner' },
      { id: 404, name: 'Simple Robotics Kits', progress: 40, level: 'Beginner' },
    ]
  },
];

// Mock data for skills in German
const mockSkillCategoriesDE: SkillArea[] = [
  {
    id: 1,
    name: 'Webentwicklung',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-blue-400 to-blue-600',
    skills: [
      { id: 101, name: 'HTML', progress: 85, level: 'Advanced' },
      { id: 102, name: 'CSS', progress: 75, level: 'Intermediate' },
      { id: 103, name: 'JavaScript', progress: 60, level: 'Intermediate' },
      { id: 104, name: 'Responsive Design', progress: 70, level: 'Intermediate' },
      { id: 105, name: 'React Grundlagen', progress: 40, level: 'Beginner' },
    ]
  },
  {
    id: 2,
    name: 'Spieledesign',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
      </svg>
    ),
    color: 'from-purple-400 to-purple-600',
    skills: [
      { id: 201, name: 'Spielmechanik', progress: 65, level: 'Intermediate' },
      { id: 202, name: 'Charakterdesign', progress: 50, level: 'Beginner' },
      { id: 203, name: 'Level-Design', progress: 40, level: 'Beginner' },
      { id: 204, name: 'Scratch-Programmierung', progress: 80, level: 'Advanced' },
    ]
  },
  {
    id: 3,
    name: 'Digitale Kunst',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-pink-400 to-pink-600',
    skills: [
      { id: 301, name: 'Grundlagen des Zeichnens', progress: 70, level: 'Intermediate' },
      { id: 302, name: 'Digitales Kolorieren', progress: 60, level: 'Intermediate' },
      { id: 303, name: 'Animations-Grundlagen', progress: 45, level: 'Beginner' },
      { id: 304, name: 'Charakterdesign', progress: 55, level: 'Intermediate' },
    ]
  },
  {
    id: 4,
    name: 'Robotik',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-green-400 to-green-600',
    skills: [
      { id: 401, name: 'Einfache Schaltkreise', progress: 50, level: 'Beginner' },
      { id: 402, name: 'Motorsteuerung', progress: 30, level: 'Beginner' },
      { id: 403, name: 'Sensoren', progress: 20, level: 'Beginner' },
      { id: 404, name: 'Einfache Robotik-Bausätze', progress: 40, level: 'Beginner' },
    ]
  },
];

// Mock data for skills in Arabic
const mockSkillCategoriesAR: SkillArea[] = [
  {
    id: 1,
    name: 'تطوير الويب',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-blue-400 to-blue-600',
    skills: [
      { id: 101, name: 'HTML', progress: 85, level: 'Advanced' },
      { id: 102, name: 'CSS', progress: 75, level: 'Intermediate' },
      { id: 103, name: 'JavaScript', progress: 60, level: 'Intermediate' },
      { id: 104, name: 'تصميم متجاوب', progress: 70, level: 'Intermediate' },
      { id: 105, name: 'أساسيات React', progress: 40, level: 'Beginner' },
    ]
  },
  {
    id: 2,
    name: 'تصميم الألعاب',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
      </svg>
    ),
    color: 'from-purple-400 to-purple-600',
    skills: [
      { id: 201, name: 'ميكانيكا اللعبة', progress: 65, level: 'Intermediate' },
      { id: 202, name: 'تصميم الشخصيات', progress: 50, level: 'Beginner' },
      { id: 203, name: 'تصميم المراحل', progress: 40, level: 'Beginner' },
      { id: 204, name: 'برمجة Scratch', progress: 80, level: 'Advanced' },
    ]
  },
  {
    id: 3,
    name: 'الفن الرقمي',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-pink-400 to-pink-600',
    skills: [
      { id: 301, name: 'الرسم الأساسي', progress: 70, level: 'Intermediate' },
      { id: 302, name: 'التلوين الرقمي', progress: 60, level: 'Intermediate' },
      { id: 303, name: 'أساسيات الرسوم المتحركة', progress: 45, level: 'Beginner' },
      { id: 304, name: 'تصميم الشخصيات', progress: 55, level: 'Intermediate' },
    ]
  },
  {
    id: 4,
    name: 'الروبوتات',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-green-400 to-green-600',
    skills: [
      { id: 401, name: 'الدوائر الأساسية', progress: 50, level: 'Beginner' },
      { id: 402, name: 'التحكم في المحركات', progress: 30, level: 'Beginner' },
      { id: 403, name: 'المستشعرات', progress: 20, level: 'Beginner' },
      { id: 404, name: 'مجموعات الروبوتات البسيطة', progress: 40, level: 'Beginner' },
    ]
  },
];

// Mock data for skills in Hebrew
const mockSkillCategoriesHE: SkillArea[] = [
  {
    id: 1,
    name: 'פיתוח אתרים',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-blue-400 to-blue-600',
    skills: [
      { id: 101, name: 'HTML', progress: 85, level: 'Advanced' },
      { id: 102, name: 'CSS', progress: 75, level: 'Intermediate' },
      { id: 103, name: 'JavaScript', progress: 60, level: 'Intermediate' },
      { id: 104, name: 'עיצוב רספונסיבי', progress: 70, level: 'Intermediate' },
      { id: 105, name: 'יסודות React', progress: 40, level: 'Beginner' },
    ]
  },
  {
    id: 2,
    name: 'עיצוב משחקים',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
      </svg>
    ),
    color: 'from-purple-400 to-purple-600',
    skills: [
      { id: 201, name: 'מכניקת משחק', progress: 65, level: 'Intermediate' },
      { id: 202, name: 'עיצוב דמויות', progress: 50, level: 'Beginner' },
      { id: 203, name: 'עיצוב שלבים', progress: 40, level: 'Beginner' },
      { id: 204, name: 'תכנות Scratch', progress: 80, level: 'Advanced' },
    ]
  },
  {
    id: 3,
    name: 'אמנות דיגיטלית',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-pink-400 to-pink-600',
    skills: [
      { id: 301, name: 'רישום בסיסי', progress: 70, level: 'Intermediate' },
      { id: 302, name: 'צביעה דיגיטלית', progress: 60, level: 'Intermediate' },
      { id: 303, name: 'יסודות אנימציה', progress: 45, level: 'Beginner' },
      { id: 304, name: 'עיצוב דמויות', progress: 55, level: 'Intermediate' },
    ]
  },
  {
    id: 4,
    name: 'רובוטיקה',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-green-400 to-green-600',
    skills: [
      { id: 401, name: 'מעגלים בסיסיים', progress: 50, level: 'Beginner' },
      { id: 402, name: 'בקרת מנועים', progress: 30, level: 'Beginner' },
      { id: 403, name: 'חיישנים', progress: 20, level: 'Beginner' },
      { id: 404, name: 'ערכות רובוטיקה פשוטות', progress: 40, level: 'Beginner' },
    ]
  },
];

// Mock data for skills in Portuguese
const mockSkillCategoriesPT: SkillArea[] = [
  {
    id: 1,
    name: 'Desenvolvimento Web',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-blue-400 to-blue-600',
    skills: [
      { id: 101, name: 'HTML', progress: 85, level: 'Advanced' },
      { id: 102, name: 'CSS', progress: 75, level: 'Intermediate' },
      { id: 103, name: 'JavaScript', progress: 60, level: 'Intermediate' },
      { id: 104, name: 'Design Responsivo', progress: 70, level: 'Intermediate' },
      { id: 105, name: 'Fundamentos de React', progress: 40, level: 'Beginner' },
    ]
  },
  {
    id: 2,
    name: 'Design de Jogos',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
      </svg>
    ),
    color: 'from-purple-400 to-purple-600',
    skills: [
      { id: 201, name: 'Mecânicas de Jogo', progress: 65, level: 'Intermediate' },
      { id: 202, name: 'Design de Personagem', progress: 50, level: 'Beginner' },
      { id: 203, name: 'Design de Nível', progress: 40, level: 'Beginner' },
      { id: 204, name: 'Programação Scratch', progress: 80, level: 'Advanced' },
    ]
  },
  {
    id: 3,
    name: 'Arte Digital',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-pink-400 to-pink-600',
    skills: [
      { id: 301, name: 'Desenho Básico', progress: 70, level: 'Intermediate' },
      { id: 302, name: 'Coloração Digital', progress: 60, level: 'Intermediate' },
      { id: 303, name: 'Fundamentos de Animação', progress: 45, level: 'Beginner' },
      { id: 304, name: 'Design de Personagem', progress: 55, level: 'Intermediate' },
    ]
  },
  {
    id: 4,
    name: 'Robótica',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    ),
    color: 'from-green-400 to-green-600',
    skills: [
      { id: 401, name: 'Circuitos Básicos', progress: 50, level: 'Beginner' },
      { id: 402, name: 'Controle de Motor', progress: 30, level: 'Beginner' },
      { id: 403, name: 'Sensores', progress: 20, level: 'Beginner' },
      { id: 404, name: 'Kits de Robótica Simples', progress: 40, level: 'Beginner' },
    ]
  },
];

// Level translations
const skillLevelTranslations: Record<string, SkillLevels> = {
  en: {
    Beginner: 'Beginner',
    Intermediate: 'Intermediate',
    Advanced: 'Advanced'
  },
  de: {
    Beginner: 'Anfänger',
    Intermediate: 'Fortgeschritten',
    Advanced: 'Experte'
  },
  pt: {
    Beginner: 'Iniciante',
    Intermediate: 'Intermediário',
    Advanced: 'Avançado'
  },
  he: {
    Beginner: 'מתחיל',
    Intermediate: 'בינוני',
    Advanced: 'מתקדם'
  },
  ar: {
    Beginner: 'مبتدئ',
    Intermediate: 'متوسط',
    Advanced: 'متقدم'
  },
  // Bengali translations
  bn: {
    Beginner: 'শিক্ষানবিস',
    Intermediate: 'মধ্যবর্তী',
    Advanced: 'উন্নত'
  },
  // Korean translations
  ko: {
    Beginner: '초보자',
    Intermediate: '중급자',
    Advanced: '고급자'
  },
  // Dutch translations
  nl: {
    Beginner: 'Beginner',
    Intermediate: 'Gemiddeld',
    Advanced: 'Gevorderd'
  },
  // Danish translations
  da: {
    Beginner: 'Begynder',
    Intermediate: 'Øvet',
    Advanced: 'Avanceret'
  },
  // Polish translations
  pl: {
    Beginner: 'Początkujący',
    Intermediate: 'Średniozaawansowany',
    Advanced: 'Zaawansowany'
  },
  // Czech translations
  cs: {
    Beginner: 'Začátečník',
    Intermediate: 'Středně pokročilý',
    Advanced: 'Pokročilý'
  },
  // Italian translations
  it: {
    Beginner: 'Principiante',
    Intermediate: 'Intermedio',
    Advanced: 'Avanzato'
  },
  // Greek translations
  el: {
    Beginner: 'Αρχάριος',
    Intermediate: 'Μεσαίο επίπεδο',
    Advanced: 'Προχωρημένος'
  },
  // Japanese translations
  ja: {
    Beginner: '初級',
    Intermediate: '中級',
    Advanced: '上級'
  }
};

// Page text translations
const textTranslations: Record<string, {
  pageTitle: string;
  printReportButton: string;
  selectChildTitle: string;
  allChildrenButton: string;
  skillsLabel: string;
  beginner: string;
  intermediate: string;
  advanced: string;
  noSkillsFound: string;
  loading: string;
  error: string;
}> = {
  en: {
    pageTitle: "Skills Progress",
    printReportButton: "Print Skill Report",
    selectChildTitle: "Select Child",
    allChildrenButton: "All Children",
    skillsLabel: "skills",
    beginner: "Beginner",
    intermediate: "Intermediate",
    advanced: "Advanced",
    noSkillsFound: "No skills found for this child.",
    loading: "Loading skills...",
    error: "Error loading skills. Please try again."
  },
  // Bengali translations
  bn: {
    pageTitle: "দক্ষতা অগ্রগতি",
    printReportButton: "দক্ষতা রিপোর্ট প্রিন্ট করুন",
    selectChildTitle: "সন্তান নির্বাচন করুন",
    allChildrenButton: "সব সন্তান",
    skillsLabel: "দক্ষতা",
    beginner: "শিক্ষানবিস",
    intermediate: "মধ্যবর্তী",
    advanced: "উন্নত",
    noSkillsFound: "এই সন্তানের জন্য কোন দক্ষতা পাওয়া যায়নি।",
    loading: "দক্ষতা লোড হচ্ছে...",
    error: "দক্ষতা লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।"
  },
  // Korean translations
  ko: {
    pageTitle: "기술 진행 상황",
    printReportButton: "기술 보고서 인쇄",
    selectChildTitle: "자녀 선택",
    allChildrenButton: "모든 자녀",
    skillsLabel: "기술",
    beginner: "초보자",
    intermediate: "중급자",
    advanced: "고급자",
    noSkillsFound: "이 자녀에 대한 기술을 찾을 수 없습니다.",
    loading: "기술 로딩 중...",
    error: "기술 로딩 중 오류가 발생했습니다. 다시 시도해 주세요."
  },
  // Dutch translations
  nl: {
    pageTitle: "Vaardigheidsvoortgang",
    printReportButton: "Vaardigheidsrapport afdrukken",
    selectChildTitle: "Kind selecteren",
    allChildrenButton: "Alle kinderen",
    skillsLabel: "vaardigheden",
    beginner: "Beginner",
    intermediate: "Gemiddeld",
    advanced: "Gevorderd",
    noSkillsFound: "Geen vaardigheden gevonden voor dit kind.",
    loading: "Vaardigheden laden...",
    error: "Fout bij het laden van vaardigheden. Probeer het opnieuw."
  },
  de: {
    pageTitle: "Fortschritt der Fähigkeiten",
    printReportButton: "Fähigkeitsbericht drucken",
    selectChildTitle: "Kind auswählen",
    allChildrenButton: "Alle Kinder",
    skillsLabel: "Fähigkeiten",
    beginner: "Anfänger",
    intermediate: "Fortgeschritten",
    advanced: "Experte",
    noSkillsFound: "Keine Fähigkeiten für dieses Kind gefunden.",
    loading: "Fähigkeiten werden geladen...",
    error: "Fehler beim Laden der Fähigkeiten. Bitte versuchen Sie es erneut."
  },
  pt: {
    pageTitle: "Progresso das Habilidades",
    printReportButton: "Imprimir Relatório de Habilidades",
    selectChildTitle: "Selecionar Criança",
    allChildrenButton: "Todas as Crianças",
    skillsLabel: "habilidades",
    beginner: "Iniciante",
    intermediate: "Intermediário",
    advanced: "Avançado",
    noSkillsFound: "Nenhuma habilidade encontrada para esta criança.",
    loading: "Carregando habilidades...",
    error: "Erro ao carregar habilidades. Por favor, tente novamente."
  },
  // Danish translations
  da: {
    pageTitle: "Færdighedsfremgang",
    printReportButton: "Udskriv færdighedsrapport",
    selectChildTitle: "Vælg barn",
    allChildrenButton: "Alle børn",
    skillsLabel: "færdigheder",
    beginner: "Begynder",
    intermediate: "Øvet",
    advanced: "Avanceret",
    noSkillsFound: "Ingen færdigheder fundet for dette barn.",
    loading: "Indlæser færdigheder...",
    error: "Fejl ved indlæsning af færdigheder. Prøv venligst igen."
  },
  // Polish translations
  pl: {
    pageTitle: "Postęp umiejętności",
    printReportButton: "Drukuj raport umiejętności",
    selectChildTitle: "Wybierz dziecko",
    allChildrenButton: "Wszystkie dzieci",
    skillsLabel: "umiejętności",
    beginner: "Początkujący",
    intermediate: "Średniozaawansowany",
    advanced: "Zaawansowany",
    noSkillsFound: "Nie znaleziono umiejętności dla tego dziecka.",
    loading: "Ładowanie umiejętności...",
    error: "Błąd podczas ładowania umiejętności. Spróbuj ponownie."
  },
  // Czech translations
  cs: {
    pageTitle: "Pokrok v dovednostech",
    printReportButton: "Vytisknout zprávu o dovednostech",
    selectChildTitle: "Vybrat dítě",
    allChildrenButton: "Všechny děti",
    skillsLabel: "dovednosti",
    beginner: "Začátečník",
    intermediate: "Středně pokročilý",
    advanced: "Pokročilý",
    noSkillsFound: "Pro toto dítě nebyly nalezeny žádné dovednosti.",
    loading: "Načítání dovedností...",
    error: "Chyba při načítání dovedností. Zkuste to prosím znovu."
  },
  // Italian translations
  it: {
    pageTitle: "Progresso delle competenze",
    printReportButton: "Stampa rapporto competenze",
    selectChildTitle: "Seleziona bambino",
    allChildrenButton: "Tutti i bambini",
    skillsLabel: "competenze",
    beginner: "Principiante",
    intermediate: "Intermedio",
    advanced: "Avanzato",
    noSkillsFound: "Nessuna competenza trovata per questo bambino.",
    loading: "Caricamento competenze...",
    error: "Errore durante il caricamento delle competenze. Riprova."
  },
  // Greek translations
  el: {
    pageTitle: "Πρόοδος δεξιοτήτων",
    printReportButton: "Εκτύπωση αναφοράς δεξιοτήτων",
    selectChildTitle: "Επιλογή παιδιού",
    allChildrenButton: "Όλα τα παιδιά",
    skillsLabel: "δεξιότητες",
    beginner: "Αρχάριος",
    intermediate: "Μεσαίο επίπεδο",
    advanced: "Προχωρημένος",
    noSkillsFound: "Δεν βρέθηκαν δεξιότητες για αυτό το παιδί.",
    loading: "Φόρτωση δεξιοτήτων...",
    error: "Σφάλμα κατά τη φόρτωση δεξιοτήτων. Παρακαλώ προσπαθήστε ξανά."
  },
  // Japanese translations
  ja: {
    pageTitle: "スキル進捗",
    printReportButton: "スキルレポートを印刷",
    selectChildTitle: "子供を選択",
    allChildrenButton: "すべての子供",
    skillsLabel: "スキル",
    beginner: "初級",
    intermediate: "中級",
    advanced: "上級",
    noSkillsFound: "この子供のスキルが見つかりません。",
    loading: "スキルを読み込み中...",
    error: "スキルの読み込み中にエラーが発生しました。もう一度お試しください。"
  },
  he: {
    pageTitle: "התקדמות מיומנויות",
    printReportButton: "הדפס דוח מיומנויות",
    selectChildTitle: "בחר ילד",
    allChildrenButton: "כל הילדים",
    skillsLabel: "מיומנויות",
    beginner: "מתחיל",
    intermediate: "בינוני",
    advanced: "מתקדם",
    noSkillsFound: "לא נמצאו מיומנויות לילד זה.",
    loading: "טוען מיומנויות...",
    error: "שגיאה בטעינת מיומנויות. אנא נסה שוב."
  },
  ar: {
    pageTitle: "تقدم المهارات",
    printReportButton: "طباعة تقرير المهارات",
    selectChildTitle: "اختر الطفل",
    allChildrenButton: "جميع الأطفال",
    skillsLabel: "مهارات",
    beginner: "مبتدئ",
    intermediate: "متوسط",
    advanced: "متقدم",
    noSkillsFound: "لم يتم العثور على مهارات لهذا الطفل.",
    loading: "جاري تحميل المهارات...",
    error: "خطأ في تحميل المهارات. يرجى المحاولة مرة أخرى."
  }
};

const SkillsProgressPage: React.FC = () => {
  const params = useParams();
  const currentLocale = (params.locale as string) || 'en';

  // State for data
  const [selectedChild, setSelectedChild] = useState<number | null>(null);
  const [expandedCategory, setExpandedCategory] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');

  // State for API data
  const [children, setChildren] = useState<Child[]>([]);
  const [skillAreasData, setSkillAreasData] = useState<SkillAreaType[]>([]);
  const [skillProgress, setSkillProgress] = useState<SkillProgressWithDetails[]>([]);
  const [skillAreaWithSkills, setSkillAreaWithSkills] = useState<SkillAreaWithSkills[]>([]);
  const [skillLevelsData, setSkillLevelsData] = useState<SkillLevel[]>([]);

  // Get translations for the current locale
  const t = textTranslations[currentLocale] || textTranslations.en;
  const skillLevels = skillLevelTranslations[currentLocale] || skillLevelTranslations.en;

  // Get translations from next-intl
  const progressT = useTranslations('LearningDocumentation.Progress');

  // Fetch user data and children
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // Get current user ID
        const userId = await childService.getCurrentUserId();

        // Get children for the parent
        const childrenData = await childService.getParentChildren(userId);
        setChildren(childrenData);

        // Get skill areas
        const skillAreasData = await skillService.getSkillAreas();
        setSkillAreasData(skillAreasData);

        // Get skill levels
        const skillLevelsData = await skillService.getSkillLevels();
        setSkillLevelsData(skillLevelsData);

        // If we have children, select the first one and fetch their data
        if (childrenData.length > 0) {
          setSelectedChild(childrenData[0].id);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load user data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Fetch child-specific data when a child is selected
  useEffect(() => {
    const fetchChildData = async () => {
      if (selectedChild === null) return;

      try {
        setIsLoading(true);

        // Get skill progress for the selected child
        const progressData = await skillService.getChildSkillProgress(selectedChild);
        setSkillProgress(progressData);

        // Process the data to group by skill area
        const skillAreasWithSkills: SkillAreaWithSkills[] = [];

        // Group skills by skill area
        skillAreasData.forEach(skillArea => {
          const skillsInArea = progressData.filter(
            item => item.skill.skill_area_id === skillArea.id
          );

          if (skillsInArea.length > 0) {
            const skillsWithProgress: SkillWithProgress[] = skillsInArea.map(item => ({
              ...item.skill,
              progress: item.progress.progress,
              level: item.skill_level
            }));

            skillAreasWithSkills.push({
              ...skillArea,
              skills: skillsWithProgress
            });
          }
        });

        setSkillAreaWithSkills(skillAreasWithSkills);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching child data:', error);
        setError('Failed to load child data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchChildData();
  }, [selectedChild, skillAreasData]);

  // Toggle category expansion
  const toggleCategory = (categoryId: number) => {
    if (expandedCategory === categoryId) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(categoryId);
    }
  };

  // Determine skill level color
  const getLevelColor = (levelKey: string) => {
    switch (levelKey) {
      case 'Beginner':
        return 'bg-blue-500/30 text-blue-200';
      case 'Intermediate':
        return 'bg-purple-500/30 text-purple-200';
      case 'Advanced':
        return 'bg-green-500/30 text-green-200';
      default:
        return 'bg-gray-500/30 text-gray-200';
    }
  };

  return (
    <DashboardLayout>
      <LearningDocumentationLayout>
        <div className="space-y-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {t.pageTitle}
            </h1>

            <div className="flex items-center space-x-4">
              {/* View mode toggle */}
              <div className="bg-white/5 backdrop-blur-xl rounded-lg border border-white/10 p-1 flex">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-1 rounded-md transition-all ${
                    viewMode === 'grid'
                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-purple-900 font-medium'
                      : 'text-white hover:bg-white/10'
                  }`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-1 rounded-md transition-all ${
                    viewMode === 'list'
                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-purple-900 font-medium'
                      : 'text-white hover:bg-white/10'
                  }`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              <button className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                </svg>
                {t.printReportButton}
              </button>
            </div>
          </div>

          {/* Child selection */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-4 text-white">{t.selectChildTitle}</h2>

            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <div className="flex flex-wrap gap-4">
                {children.length === 0 ? (
                  <p className="text-gray-300">No children found. Please add a child to your account.</p>
                ) : (
                  <>
                    {children.map(child => (
                      <button
                        key={child.id}
                        onClick={() => setSelectedChild(child.id)}
                        className={`flex items-center px-4 py-2 rounded-lg transition-all ${selectedChild === child.id
                          ? 'bg-yellow-400 text-purple-900 font-bold'
                          : 'bg-white/10 text-white hover:bg-white/20'}`}
                      >
                        <div className="w-8 h-8 rounded-full bg-gray-300 mr-2 overflow-hidden">
                          {child.avatar_url ? (
                            <Image
                              src={child.avatar_url}
                              alt={child.name || 'Child avatar'}
                              width={32}
                              height={32}
                              className="object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
                          )}
                        </div>
                        {child.name || `Child ${child.id}`}
                      </button>
                    ))}
                  </>
                )}
              </div>
            )}
          </div>

          {/* Progress Content */}
          <div className="space-y-6">
            {isLoading && (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                <span className="ml-3 text-white">{t.loading}</span>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <>
                {viewMode === 'grid' ? (
                  // Grid view with progress cards
                  <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
                    <h2 className="text-xl font-bold mb-6 text-white">
                      {progressT('existingSkillsTitle')}
                    </h2>

                    {skillProgress.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-300">{t.noSkillsFound}</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {skillProgress.map((progressItem) => {
                          // Get the skill level name
                          const skillLevel = progressItem.skill_level ?
                            progressItem.skill_level.name_key :
                            progressT('noLevelAssigned');

                          // Get the skill level ID
                          const skillLevelId = progressItem.skill_level ?
                            progressItem.skill_level.id :
                            undefined;

                          // Log the progress data for debugging
                          console.log(`Progress data for ${progressItem.skill.name_key}:`, {
                            progressObject: progressItem.progress,
                            progressValue: progressItem.progress.progress,
                            progressType: typeof progressItem.progress.progress
                          });

                          // Ensure progress is a valid number
                          let safeProgress = 0;
                          if (progressItem.progress.progress !== undefined && progressItem.progress.progress !== null) {
                            // Convert to number if it's a string
                            const numProgress = typeof progressItem.progress.progress === 'string'
                              ? parseInt(progressItem.progress.progress, 10)
                              : progressItem.progress.progress;
                            // Ensure it's a valid number between 0 and 100
                            safeProgress = typeof numProgress === 'number' && !isNaN(numProgress)
                              ? Math.min(100, Math.max(0, numProgress))
                              : 0;
                          }

                          return (
                            <ProgressCardViewer
                              key={`progress-${progressItem.progress.id}`}
                              childId={selectedChild || 0}
                              skillId={progressItem.skill.id}
                              skillName={progressItem.skill.name_key}
                              skillArea={progressItem.skill_area.name_key}
                              skillLevel={skillLevel}
                              skillLevelId={skillLevelId}
                              progress={safeProgress}
                              notes={progressItem.progress.notes}
                            />
                          );
                        })}
                      </div>
                    )}
                  </div>
                ) : (
                  // List view with expandable categories
                  <>
                    {skillAreaWithSkills.length > 0 ? (
                      skillAreaWithSkills.map(category => {
                        const colorTheme = getSkillAreaColor(category.color_theme || 'gray');

                        return (
                          <div key={category.id} className="bg-white/5 backdrop-blur-xl rounded-xl border border-white/10 overflow-hidden">
                            <button
                              onClick={() => toggleCategory(category.id)}
                              className="w-full p-6 flex justify-between items-center hover:bg-white/5 transition-colors"
                            >
                              <div className="flex items-center">
                                <div className={`p-2 rounded-lg mr-4 bg-gradient-to-br ${colorTheme}`}>
                                  {getSkillAreaIcon(category.icon_name || 'default')}
                                </div>
                                <div>
                                  <h2 className="text-xl font-bold text-white">{category.name_key}</h2>
                                  <p className="text-sm text-gray-400">{category.skills.length} {t.skillsLabel}</p>
                                </div>
                              </div>

                              <div className="flex items-center">
                                <div className="relative mr-4">
                                  <div className="overflow-hidden h-2 w-32 mb-4 text-xs flex rounded-full bg-white/10">
                                    <div
                                      style={{
                                        width: `${category.skills.reduce((acc, skill) => {
                                          // Ensure progress is a valid number
                                          let progress = 0;
                                          if (skill.progress !== undefined && skill.progress !== null) {
                                            // Convert to number if it's a string
                                            const numProgress = typeof skill.progress === 'string'
                                              ? parseInt(skill.progress, 10)
                                              : skill.progress;
                                            // Ensure it's a valid number
                                            progress = typeof numProgress === 'number' && !isNaN(numProgress)
                                              ? numProgress
                                              : 0;
                                          }
                                          return acc + progress;
                                        }, 0) / (category.skills.length || 1)}%`
                                      }}
                                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r ${colorTheme}`}
                                    ></div>
                                  </div>
                                </div>

                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className={`h-5 w-5 text-gray-400 transition-transform duration-300 ${expandedCategory === category.id ? 'transform rotate-180' : ''}`}
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </div>
                            </button>

                            {expandedCategory === category.id && (
                              <div className="p-6 pt-0 border-t border-white/10">
                                <div className="space-y-4">
                                  {category.skills.map(skill => {
                                    const levelKey = skill.level?.name_key || 'Beginner';
                                    const levelName = skillLevels[levelKey] || levelKey;

                                    return (
                                      <div key={skill.id} className="bg-white/10 p-4 rounded-lg">
                                        <div className="flex justify-between items-center mb-2">
                                          <div className="flex items-center">
                                            <h3 className="font-medium text-white">{skill.name_key}</h3>
                                            <span className={`ml-3 text-xs px-2 py-0.5 rounded-full ${getLevelColor(levelKey)}`}>
                                              {levelName}
                                            </span>
                                          </div>
                                          <span className="text-sm text-yellow-400">
                                            {typeof skill.progress === 'number' ? skill.progress :
                                             typeof skill.progress === 'string' ? parseInt(skill.progress, 10) : 0}%
                                          </span>
                                        </div>

                                        <div className="relative">
                                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded-full bg-white/10">
                                            <div
                                              style={{
                                                width: `${typeof skill.progress === 'number' ? skill.progress :
                                                        typeof skill.progress === 'string' ? parseInt(skill.progress, 10) : 0}%`
                                              }}
                                              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r ${colorTheme}`}
                                            ></div>
                                          </div>
                                        </div>

                                        <div className="flex justify-between text-xs text-gray-400">
                                          <span>{t.beginner}</span>
                                          <span>{t.intermediate}</span>
                                          <span>{t.advanced}</span>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10 text-center">
                        <p className="text-gray-300">{t.noSkillsFound}</p>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </LearningDocumentationLayout>
    </DashboardLayout>
  );
};

export default SkillsProgressPage;