"use client";

import React, { useState, useEffect } from 'react';
import DashboardLayout from '../../components/DashboardLayout';
import LearningDocumentationLayout from '../../components/LearningDocumentationLayout';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

// Import services
import { skillService } from '../../../services/skillService';
import { certificateService } from '../../../services/certificateService';
import { learningEventService } from '../../../services/learningEventService';
import { childService } from '../../../services/childService';
import { documentationService, Documentation } from '../../../services/documentationService';

// Import types
import { Child } from '../../../types/child';
import { SkillArea as SkillAreaType, SkillProgressWithDetails } from '../../../types/skill';
import { LearningEventWithDetails } from '../../../types/learningEvent';
import { CertificateWithSkills } from '../../../types/certificate';

interface SkillArea {
  id: number;
  name: string;
  progress: number;
  color: string;
}

interface SkillLevels {
  Beginner: string;
  Intermediate: string;
  Advanced: string;
  [key: string]: string; // Add index signature for dynamic access
}

// Skill areas with translations
const skillAreasTranslations: Record<string, SkillArea[]> = {
  en: [
    { id: 1, name: 'Web Development', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Game Design', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Digital Art', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotics', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Bengali translations
  bn: [
    { id: 1, name: 'ওয়েব ডেভেলপমেন্ট', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'গেম ডিজাইন', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'ডিজিটাল আর্ট', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'রোবোটিক্স', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Korean translations
  ko: [
    { id: 1, name: '웹 개발', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: '게임 디자인', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: '디지털 아트', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: '로보틱스', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Dutch translations
  nl: [
    { id: 1, name: 'Webontwikkeling', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Gameontwerp', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Digitale Kunst', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotica', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  de: [
    { id: 1, name: 'Webentwicklung', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Spieledesign', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Digitale Kunst', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotik', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  es: [
    { id: 1, name: 'Desarrollo Web', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Diseño de Juegos', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Arte Digital', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robótica', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  fr: [
    { id: 1, name: 'Développement Web', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Conception de Jeux', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Art Numérique', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotique', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  zh: [
    { id: 1, name: '网页开发', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: '游戏设计', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: '数字艺术', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: '机器人学', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  ja: [
    { id: 1, name: 'ウェブ開発', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'ゲームデザイン', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'デジタルアート', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'ロボット工学', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  hi: [
    { id: 1, name: 'वेब डेवलपमेंट', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'गेम डिज़ाइन', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'डिजिटल आर्ट', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'रोबोटिक्स', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  ar: [
    { id: 1, name: 'تطوير الويب', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'تصميم الألعاب', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'الفن الرقمي', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'الروبوتات', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  he: [
    { id: 1, name: 'פיתוח אתרים', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'עיצוב משחקים', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'אמנות דיגיטלית', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'רובוטיקה', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  pt: [
    { id: 1, name: 'Desenvolvimento Web', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Design de Jogos', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Arte Digital', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robótica', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Danish translations
  da: [
    { id: 1, name: 'Webudvikling', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Spildesign', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Digital Kunst', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotteknologi', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Polish translations
  pl: [
    { id: 1, name: 'Tworzenie stron internetowych', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Projektowanie gier', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Sztuka cyfrowa', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotyka', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Czech translations
  cs: [
    { id: 1, name: 'Vývoj webových stránek', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Návrh her', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Digitální umění', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotika', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Italian translations
  it: [
    { id: 1, name: 'Sviluppo Web', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Design di Giochi', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Arte Digitale', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Robotica', progress: 30, color: 'from-green-400 to-green-600' },
  ],
  // Greek translations
  el: [
    { id: 1, name: 'Ανάπτυξη Ιστού', progress: 75, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Σχεδιασμός Παιχνιδιών', progress: 45, color: 'from-purple-400 to-purple-600' },
    { id: 3, name: 'Ψηφιακή Τέχνη', progress: 60, color: 'from-pink-400 to-pink-600' },
    { id: 4, name: 'Ρομποτική', progress: 30, color: 'from-green-400 to-green-600' },
  ],
};

// Level translations
const skillLevelTranslations: Record<string, SkillLevels> = {
  en: {
    Beginner: 'Beginner',
    Intermediate: 'Intermediate',
    Advanced: 'Advanced'
  },
  de: {
    Beginner: 'Anfänger',
    Intermediate: 'Fortgeschritten',
    Advanced: 'Experte'
  },
  es: {
    Beginner: 'Principiante',
    Intermediate: 'Intermedio',
    Advanced: 'Avanzado'
  },
  fr: {
    Beginner: 'Débutant',
    Intermediate: 'Intermédiaire',
    Advanced: 'Avancé'
  },
  zh: {
    Beginner: '初学者',
    Intermediate: '中级',
    Advanced: '高级'
  },
  ja: {
    Beginner: '初級',
    Intermediate: '中級',
    Advanced: '上級'
  },
  hi: {
    Beginner: 'शुरुआती',
    Intermediate: 'मध्यवर्ती',
    Advanced: 'उन्नत'
  },
  ar: {
    Beginner: 'مبتدئ',
    Intermediate: 'متوسط',
    Advanced: 'متقدم'
  },
  he: {
    Beginner: 'מתחיל',
    Intermediate: 'בינוני',
    Advanced: 'מתקדם'
  },
  pt: {
    Beginner: 'Iniciante',
    Intermediate: 'Intermediário',
    Advanced: 'Avançado'
  },
  // Bengali translations
  bn: {
    Beginner: 'শিক্ষানবিস',
    Intermediate: 'মধ্যবর্তী',
    Advanced: 'উন্নত'
  },
  // Korean translations
  ko: {
    Beginner: '초보자',
    Intermediate: '중급자',
    Advanced: '고급자'
  },
  // Dutch translations
  nl: {
    Beginner: 'Beginner',
    Intermediate: 'Gemiddeld',
    Advanced: 'Gevorderd'
  },
  // Danish translations
  da: {
    Beginner: 'Begynder',
    Intermediate: 'Øvet',
    Advanced: 'Avanceret'
  },
  // Polish translations
  pl: {
    Beginner: 'Początkujący',
    Intermediate: 'Średniozaawansowany',
    Advanced: 'Zaawansowany'
  },
  // Czech translations
  cs: {
    Beginner: 'Začátečník',
    Intermediate: 'Středně pokročilý',
    Advanced: 'Pokročilý'
  },
  // Italian translations
  it: {
    Beginner: 'Principiante',
    Intermediate: 'Intermedio',
    Advanced: 'Avanzato'
  },
  // Greek translations
  el: {
    Beginner: 'Αρχάριος',
    Intermediate: 'Μεσαίο επίπεδο',
    Advanced: 'Προχωρημένος'
  },
};

// We'll fetch children from the API

const LearningDocumentationPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const currentLocale = (params.locale as string) || 'en';

  // State for data
  const [selectedChild, setSelectedChild] = useState<number | null>(null);
  const [timeframe, setTimeframe] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for API data
  const [children, setChildren] = useState<Child[]>([]);
  const [skillAreasData, setSkillAreasData] = useState<SkillAreaType[]>([]);
  const [skillProgress, setSkillProgress] = useState<SkillProgressWithDetails[]>([]);
  const [learningEvents, setLearningEvents] = useState<LearningEventWithDetails[]>([]);
  const [certificates, setCertificates] = useState<CertificateWithSkills[]>([]);
  const [documentation, setDocumentation] = useState<Documentation | null>(null);

  // Get translations for the current locale
  const t = useTranslations('learningDocumentation');
  const skillAreasTranslated = skillAreasTranslations[currentLocale as keyof typeof skillAreasTranslations] || skillAreasTranslations.en;
  const skillLevels = skillLevelTranslations[currentLocale as keyof typeof skillLevelTranslations] || skillLevelTranslations.en;

  // Fetch user data and children
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // Get current user ID
        const userId = await childService.getCurrentUserId();

        // Get children for the parent
        const childrenData = await childService.getParentChildren(userId);
        setChildren(childrenData);

        // Get skill areas
        const skillAreasData = await skillService.getSkillAreas();
        setSkillAreasData(skillAreasData);

        // If we have children, select the first one and fetch their data
        if (childrenData.length > 0) {
          setSelectedChild(childrenData[0].id);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load user data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Fetch child-specific data when a child is selected
  useEffect(() => {
    const fetchChildData = async () => {
      if (selectedChild === null) return;

      try {
        setIsLoading(true);

        // Get skill progress for the selected child
        const progressData = await skillService.getChildSkillProgress(selectedChild);
        setSkillProgress(progressData);

        // Get learning events for the selected child
        const eventsData = await learningEventService.getChildLearningEvents(
          selectedChild,
          10, // Limit to 10 events
          0,  // No offset
          getTimeframeStartDate(timeframe),
          new Date().toISOString()
        );
        setLearningEvents(eventsData);

        // Get certificates for the selected child
        const certificatesData = await certificateService.getChildCertificates(selectedChild);
        setCertificates(certificatesData);

        // Get documentation for the selected child
        try {
          const documentationData = await documentationService.getDocumentation(selectedChild);
          setDocumentation(documentationData);
        } catch (docError) {
          console.error('Error fetching documentation:', docError);
          // Don't set the main error state, just log the error
          // This allows the rest of the page to still function
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching child data:', error);
        setError('Failed to load child data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchChildData();
  }, [selectedChild, timeframe]);

  // Helper function to get the start date for a timeframe
  const getTimeframeStartDate = (timeframe: string): string | undefined => {
    const now = new Date();

    switch (timeframe) {
      case 'month':
        const oneMonthAgo = new Date(now);
        oneMonthAgo.setMonth(now.getMonth() - 1);
        return oneMonthAgo.toISOString();
      case 'quarter':
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        return threeMonthsAgo.toISOString();
      case 'year':
        const oneYearAgo = new Date(now);
        oneYearAgo.setFullYear(now.getFullYear() - 1);
        return oneYearAgo.toISOString();
      case 'all':
      default:
        return undefined; // No start date filter
    }
  };

  return (
    <DashboardLayout>
      <LearningDocumentationLayout>
        <div className="space-y-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {t('pageTitle', {defaultValue: 'Learning Documentation'})}
            </h1>

            <div className="flex space-x-3">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                <option value="all">{t('timeframeAllTime', {defaultValue: 'All Time'})}</option>
                <option value="month">{t('timeframePastMonth', {defaultValue: 'Past Month'})}</option>
                <option value="quarter">{t('timeframePastQuarter', {defaultValue: 'Past 3 Months'})}</option>
                <option value="year">{t('timeframePastYear', {defaultValue: 'Past Year'})}</option>
              </select>

              <button className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                </svg>
                {t('exportReportButton', {defaultValue: 'Export Report'})}
              </button>
            </div>
          </div>

          {/* Child selection */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-4 text-white">{t('selectChildTitle', {defaultValue: 'Select Child'})}</h2>

            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <div className="flex flex-wrap gap-4">
                {children.length === 0 ? (
                  <p className="text-gray-300">No children found. Please add a child to your account.</p>
                ) : (
                  <>
                    {children.map(child => (
                      <button
                        key={child.id}
                        onClick={() => setSelectedChild(child.id)}
                        className={`flex items-center px-4 py-2 rounded-lg transition-all ${selectedChild === child.id
                          ? 'bg-yellow-400 text-purple-900 font-bold'
                          : 'bg-white/10 text-white hover:bg-white/20'}`}
                      >
                        <div className="w-8 h-8 rounded-full bg-gray-300 mr-2 overflow-hidden">
                          {child.avatar_url ? (
                            <Image
                              src={child.avatar_url}
                              alt={child.name || 'Child avatar'}
                              width={32}
                              height={32}
                              className="object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
                          )}
                        </div>
                        {child.name || `Child ${child.id}`}
                      </button>
                    ))}
                  </>
                )}
              </div>
            )}
          </div>

          {/* Documentation Overview */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-4 text-white">{t('documentationTitle', {defaultValue: 'Documentation Overview'})}</h2>

            {isLoading && (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <>
                {documentation ? (
                  <div className="space-y-6">
                    {/* General Notes */}
                    {documentation.general_notes && (
                      <div>
                        <h3 className="text-lg font-semibold text-yellow-400 mb-2">{t('generalNotesLabel', {defaultValue: 'General Notes'})}</h3>
                        <div className="bg-white/10 p-4 rounded-lg">
                          <p className="text-white">{documentation.general_notes}</p>
                        </div>
                      </div>
                    )}

                    {/* Learning Goals */}
                    {documentation.learning_goals && (
                      <div>
                        <h3 className="text-lg font-semibold text-yellow-400 mb-2">{t('learningGoalsLabel', {defaultValue: 'Learning Goals'})}</h3>
                        <div className="bg-white/10 p-4 rounded-lg">
                          <p className="text-white">{documentation.learning_goals}</p>
                        </div>
                      </div>
                    )}

                    {/* Strengths */}
                    {documentation.strengths && (
                      <div>
                        <h3 className="text-lg font-semibold text-yellow-400 mb-2">{t('strengthsLabel', {defaultValue: 'Strengths'})}</h3>
                        <div className="bg-white/10 p-4 rounded-lg">
                          <p className="text-white">{documentation.strengths}</p>
                        </div>
                      </div>
                    )}

                    {/* Areas for Improvement */}
                    {documentation.areas_for_improvement && (
                      <div>
                        <h3 className="text-lg font-semibold text-yellow-400 mb-2">{t('areasForImprovementLabel', {defaultValue: 'Areas for Improvement'})}</h3>
                        <div className="bg-white/10 p-4 rounded-lg">
                          <p className="text-white">{documentation.areas_for_improvement}</p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-300 text-sm">{t('noDocumentationMessage', {defaultValue: 'No documentation has been added for this child yet.'})}</p>
                )}
              </>
            )}
          </div>

          {/* Progress summary */}
          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Overall progress */}
            <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-6 text-white">{t('overallProgressTitle', {defaultValue: 'Overall Progress'})}</h2>

              {isLoading && (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                </div>
              )}

              {error && (
                <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                  {error}
                </div>
              )}

              {!isLoading && !error && (
                <>
                  {/* Calculate overall progress across all skills */}
                  {(() => {
                    // Calculate average progress
                    const overallProgress = skillProgress.length > 0
                      ? Math.round(skillProgress.reduce((sum, item) => sum + item.progress.progress, 0) / skillProgress.length)
                      : 0;

                    return (
                      <div className="relative pt-1">
                        <div className="flex mb-2 items-center justify-between">
                          <div>
                            <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full bg-white/10 text-white">
                              {t('progressTowardMastery', {defaultValue: 'Progress Toward Mastery'})}
                            </span>
                          </div>
                          <div className="text-right">
                            <span className="text-xs font-semibold inline-block text-white">
                              {overallProgress}%
                            </span>
                          </div>
                        </div>
                        <div className="overflow-hidden h-4 mb-4 text-xs flex rounded-full bg-white/10">
                          <div
                            style={{ width: `${overallProgress}%` }}
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-yellow-300 to-yellow-500"
                          ></div>
                        </div>
                      </div>
                    );
                  })()}

                  {/* Group progress by skill area */}
                  <div className="mt-8 space-y-4">
                    {skillAreasData.map((skillArea) => {
                      // Find all progress entries for this skill area
                      const areaProgress = skillProgress.filter(
                        item => item.skill.skill_area_id === skillArea.id
                      );

                      // Calculate average progress for this area
                      const averageProgress = areaProgress.length > 0
                        ? Math.round(areaProgress.reduce((sum, item) => sum + item.progress.progress, 0) / areaProgress.length)
                        : 0;

                      // Get color from the translated skill areas (for now)
                      const areaColor = skillAreasTranslated.find(a => a.id === skillArea.id)?.color || 'from-gray-400 to-gray-600';

                      return (
                        <div key={skillArea.id} className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block text-white">
                                {skillArea.name_key}
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-white">
                                {averageProgress}%
                              </span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded-full bg-white/10">
                            <div
                              style={{ width: `${averageProgress}%` }}
                              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r ${areaColor}`}
                            ></div>
                          </div>
                        </div>
                      );
                    })}

                    {skillAreasData.length === 0 && (
                      <p className="text-gray-300 text-sm">No skill areas found.</p>
                    )}
                  </div>
                </>
              )}
            </div>

            {/* Recent achievements */}
            <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-4 text-white">{t('recentAchievementsTitle', {defaultValue: 'Recent Achievements'})}</h2>

              {isLoading && (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                </div>
              )}

              {error && (
                <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                  {error}
                </div>
              )}

              {!isLoading && !error && (
                <div className="space-y-4">
                  {learningEvents.length > 0 ? (
                    <>
                      {learningEvents.slice(0, 3).map(event => {
                        // Format the date
                        const eventDate = new Date(event.event_date);
                        const formattedDate = eventDate.toLocaleDateString(currentLocale, {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        });

                        // Get the child name
                        const childName = children.find(c => c.id === event.child_id)?.name || `Child ${event.child_id}`;

                        // Get the skill level if available
                        const skillLevelName = event.skill_level_achieved
                          ? skillLevels[event.skill_level_achieved.name_key] || event.skill_level_achieved.name_key
                          : '';

                        return (
                          <div key={event.id} className="bg-white/10 rounded-lg p-4 flex items-start">
                            <div className="bg-yellow-400 p-2 rounded-full mr-4">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-900" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <div className="flex justify-between items-start">
                                <h3 className="font-semibold text-white">{event.achievement_details_key}</h3>
                                <span className="text-xs text-gray-400">{formattedDate}</span>
                              </div>
                              <p className="text-sm text-gray-300 mt-1">{event.description_key}</p>
                              <div className="flex items-center mt-2">
                                <span className="text-xs bg-white/20 rounded-full px-2 py-0.5 text-white mr-2">
                                  {childName}
                                </span>
                                {skillLevelName && (
                                  <span className="text-xs bg-purple-500/30 rounded-full px-2 py-0.5 text-purple-200">
                                    {skillLevelName}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}

                      <Link
                        href={`/${currentLocale}/learning-documentation/timeline`}
                        className="block w-full py-2 mt-2 text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors text-center"
                      >
                        {t('viewFullTimelineButton', {defaultValue: 'View Full Timeline'})}
                      </Link>
                    </>
                  ) : (
                    <p className="text-gray-300 text-sm">No learning events found for this timeframe.</p>
                  )}
                </div>
              )}
            </div>

            {/* Skills Overview Card */}
            <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-4 text-white">{t('skillsOverviewTitle', {defaultValue: 'Skills Overview'})}</h2>

              {isLoading && (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                </div>
              )}

              {error && (
                <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                  {error}
                </div>
              )}

              {!isLoading && !error && (
                <>
                  {skillProgress.length > 0 ? (
                    <div className="space-y-4">
                      {/* Count skills by level */}
                      {(() => {
                        const skillsByLevel: Record<string, number> = {};

                        // Count skills by level
                        skillProgress.forEach(item => {
                          if (item.skill_level) {
                            const levelKey = item.skill_level.name_key;
                            skillsByLevel[levelKey] = (skillsByLevel[levelKey] || 0) + 1;
                          }
                        });

                        // Display summary
                        return (
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                            {Object.entries(skillsByLevel).map(([levelKey, count]) => {
                              const levelName = skillLevels[levelKey] || levelKey;
                              return (
                                <div key={levelKey} className="bg-white/10 p-3 rounded-lg text-center">
                                  <div className="text-2xl font-bold text-yellow-400">{count}</div>
                                  <div className="text-xs text-gray-300">{levelName}</div>
                                </div>
                              );
                            })}
                          </div>
                        );
                      })()}

                      <Link
                        href={`/${currentLocale}/learning-documentation/skills`}
                        className="block w-full py-2 mt-2 text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors text-center"
                      >
                        {t('viewAllSkillsButton', {defaultValue: 'View All Skills'})}
                      </Link>
                    </div>
                  ) : (
                    <>
                      <p className="text-gray-300 text-sm mb-4">{t('skillsSummaryText', {defaultValue: 'Summary of mastered and in-progress skills will go here.'})}</p>
                      <Link
                        href={`/${currentLocale}/learning-documentation/skills`}
                        className="block w-full py-2 mt-2 text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors text-center"
                      >
                        {t('viewAllSkillsButton', {defaultValue: 'View All Skills'})}
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Certificates Card */}
            <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-4 text-white">{t('certificatesTitle', {defaultValue: 'Certificates'})}</h2>

              {isLoading && (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                </div>
              )}

              {error && (
                <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                  {error}
                </div>
              )}

              {!isLoading && !error && (
                <>
                  {certificates.length > 0 ? (
                    <div className="space-y-4">
                      {/* Display most recent certificates */}
                      <div className="space-y-3">
                        {certificates.slice(0, 2).map(certificate => {
                          // Format the date
                          const issueDate = new Date(certificate.issue_date);
                          const formattedDate = issueDate.toLocaleDateString(currentLocale, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          });

                          return (
                            <div key={certificate.id} className="bg-white/10 p-3 rounded-lg flex items-center">
                              <div className="bg-yellow-400 p-2 rounded-full mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-900" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="flex-1">
                                <div className="flex justify-between">
                                  <h3 className="font-medium text-white">{certificate.title_key}</h3>
                                  <span className="text-xs text-gray-400">{formattedDate}</span>
                                </div>
                                <div className="text-xs text-gray-300 mt-1">
                                  {certificate.skills.length} skills
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      <Link
                        href={`/${currentLocale}/learning-documentation/certificates`}
                        className="block w-full py-2 mt-2 text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors text-center"
                      >
                        {t('viewAllCertificatesButton', {defaultValue: 'View All Certificates'})}
                      </Link>
                    </div>
                  ) : (
                    <>
                      <p className="text-gray-300 text-sm mb-4">{t('certificatesSummaryText', {defaultValue: 'Summary of earned certificates will go here.'})}</p>
                      <Link
                        href={`/${currentLocale}/learning-documentation/certificates`}
                        className="block w-full py-2 mt-2 text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors text-center"
                      >
                        {t('viewAllCertificatesButton', {defaultValue: 'View All Certificates'})}
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </LearningDocumentationLayout>
    </DashboardLayout>
  ); // Ensure semicolon is present and correctly placed
};

export default LearningDocumentationPage;