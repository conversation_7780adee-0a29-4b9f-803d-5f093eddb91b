"use client";

import React, { useState, useEffect } from 'react';
import DashboardLayout from '../../../components/DashboardLayout';
import LearningDocumentationLayout from '../../../components/LearningDocumentationLayout';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

// Import services
import { certificateService } from '../../../../services/certificateService';
import { childService } from '../../../../services/childService';

// Import components
import CertificateDetailsModal from '../../../components/CertificateDetailsModal';

// Import types
import { Child } from '../../../../types/child';
import { CertificateWithSkills } from '../../../../types/certificate';
import { getApiUrl } from '../../../../utils/apiConfig';

// Page text translations
const textTranslations: Record<string, {
  pageTitle: string;
  downloadAllButton: string;
  selectChildTitle: string;
  allChildrenButton: string;
  certificateLabel: string;
  awardedToText: string;
  viewDetailsButton: string;
  downloadButton: string;
  loading: string;
  error: string;
  noCertificatesFound: string;
  certificatesTitle: string;
}> = {
  en: {
    pageTitle: "Certificates & Achievements",
    downloadAllButton: "Download All",
    selectChildTitle: "Select Child",
    allChildrenButton: "All Children",
    certificateLabel: "Certificate",
    awardedToText: "Awarded to",
    viewDetailsButton: "View Details",
    downloadButton: "Download",
    loading: "Loading certificates...",
    error: "Error loading certificates. Please try again.",
    noCertificatesFound: "No certificates found for this child.",
    certificatesTitle: "Certificates"
  },
  de: {
    pageTitle: "Zertifikate & Erfolge",
    downloadAllButton: "Alle herunterladen",
    selectChildTitle: "Kind auswählen",
    allChildrenButton: "Alle Kinder",
    certificateLabel: "Zertifikat",
    awardedToText: "Verliehen an",
    viewDetailsButton: "Details anzeigen",
    downloadButton: "Herunterladen",
    loading: "Zertifikate werden geladen...",
    error: "Fehler beim Laden der Zertifikate. Bitte versuchen Sie es erneut.",
    noCertificatesFound: "Keine Zertifikate für dieses Kind gefunden.",
    certificatesTitle: "Zertifikate"
  },
  he: {
    pageTitle: "תעודות והישגים",
    downloadAllButton: "הורד הכל",
    selectChildTitle: "בחר ילד",
    allChildrenButton: "כל הילדים",
    certificateLabel: "תעודה",
    awardedToText: "הוענק ל",
    viewDetailsButton: "הצג פרטים",
    downloadButton: "הורד",
    loading: "טוען תעודות...",
    error: "שגיאה בטעינת תעודות. אנא נסה שוב.",
    noCertificatesFound: "לא נמצאו תעודות לילד זה.",
    certificatesTitle: "תעודות"
  },
  pt: {
    pageTitle: "Certificados e Conquistas",
    downloadAllButton: "Baixar Todos",
    selectChildTitle: "Selecionar Criança",
    allChildrenButton: "Todas as Crianças",
    certificateLabel: "Certificado",
    awardedToText: "Concedido a",
    viewDetailsButton: "Ver Detalhes",
    downloadButton: "Baixar",
    loading: "Carregando certificados...",
    error: "Erro ao carregar certificados. Por favor, tente novamente.",
    noCertificatesFound: "Nenhum certificado encontrado para esta criança.",
    certificatesTitle: "Certificados"
  },
  ar: {
    pageTitle: "الشهادات والإنجازات",
    downloadAllButton: "تحميل الكل",
    selectChildTitle: "اختر الطفل",
    allChildrenButton: "جميع الأطفال",
    certificateLabel: "شهادة",
    awardedToText: "مُنحت لـ",
    viewDetailsButton: "عرض التفاصيل",
    downloadButton: "تحميل",
    loading: "جاري تحميل الشهادات...",
    error: "خطأ في تحميل الشهادات. يرجى المحاولة مرة أخرى.",
    noCertificatesFound: "لم يتم العثور على شهادات لهذا الطفل.",
    certificatesTitle: "الشهادات"
  },
  // Bengali translations
  bn: {
    pageTitle: "সার্টিফিকেট এবং অর্জন",
    downloadAllButton: "সব ডাউনলোড করুন",
    selectChildTitle: "সন্তান নির্বাচন করুন",
    allChildrenButton: "সব সন্তান",
    certificateLabel: "সার্টিফিকেট",
    awardedToText: "প্রদান করা হয়েছে",
    viewDetailsButton: "বিস্তারিত দেখুন",
    downloadButton: "ডাউনলোড করুন",
    loading: "সার্টিফিকেট লোড হচ্ছে...",
    error: "সার্টিফিকেট লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।",
    noCertificatesFound: "এই সন্তানের জন্য কোন সার্টিফিকেট পাওয়া যায়নি।",
    certificatesTitle: "সার্টিফিকেট"
  },
  // Korean translations
  ko: {
    pageTitle: "인증서 및 성취",
    downloadAllButton: "모두 다운로드",
    selectChildTitle: "자녀 선택",
    allChildrenButton: "모든 자녀",
    certificateLabel: "인증서",
    awardedToText: "수여 대상",
    viewDetailsButton: "세부 정보 보기",
    downloadButton: "다운로드",
    loading: "인증서 로딩 중...",
    error: "인증서 로딩 중 오류가 발생했습니다. 다시 시도해 주세요.",
    noCertificatesFound: "이 자녀에 대한 인증서를 찾을 수 없습니다.",
    certificatesTitle: "인증서"
  },
  // Dutch translations
  nl: {
    pageTitle: "Certificaten & Prestaties",
    downloadAllButton: "Alles downloaden",
    selectChildTitle: "Kind selecteren",
    allChildrenButton: "Alle kinderen",
    certificateLabel: "Certificaat",
    awardedToText: "Uitgereikt aan",
    viewDetailsButton: "Details bekijken",
    downloadButton: "Downloaden",
    loading: "Certificaten laden...",
    error: "Fout bij het laden van certificaten. Probeer het opnieuw.",
    noCertificatesFound: "Geen certificaten gevonden voor dit kind.",
    certificatesTitle: "Certificaten"
  },
  // Danish translations
  da: {
    pageTitle: "Certifikater & Præstationer",
    downloadAllButton: "Download alle",
    selectChildTitle: "Vælg barn",
    allChildrenButton: "Alle børn",
    certificateLabel: "Certifikat",
    awardedToText: "Tildelt til",
    viewDetailsButton: "Se detaljer",
    downloadButton: "Download",
    loading: "Indlæser certifikater...",
    error: "Fejl ved indlæsning af certifikater. Prøv venligst igen.",
    noCertificatesFound: "Ingen certifikater fundet for dette barn.",
    certificatesTitle: "Certifikater"
  },
  // Polish translations
  pl: {
    pageTitle: "Certyfikaty i osiągnięcia",
    downloadAllButton: "Pobierz wszystkie",
    selectChildTitle: "Wybierz dziecko",
    allChildrenButton: "Wszystkie dzieci",
    certificateLabel: "Certyfikat",
    awardedToText: "Przyznany dla",
    viewDetailsButton: "Zobacz szczegóły",
    downloadButton: "Pobierz",
    loading: "Ładowanie certyfikatów...",
    error: "Błąd podczas ładowania certyfikatów. Spróbuj ponownie.",
    noCertificatesFound: "Nie znaleziono certyfikatów dla tego dziecka.",
    certificatesTitle: "Certyfikaty"
  },
  // Czech translations
  cs: {
    pageTitle: "Certifikáty a úspěchy",
    downloadAllButton: "Stáhnout vše",
    selectChildTitle: "Vybrat dítě",
    allChildrenButton: "Všechny děti",
    certificateLabel: "Certifikát",
    awardedToText: "Uděleno",
    viewDetailsButton: "Zobrazit podrobnosti",
    downloadButton: "Stáhnout",
    loading: "Načítání certifikátů...",
    error: "Chyba při načítání certifikátů. Zkuste to prosím znovu.",
    noCertificatesFound: "Pro toto dítě nebyly nalezeny žádné certifikáty.",
    certificatesTitle: "Certifikáty"
  },
  // Italian translations
  it: {
    pageTitle: "Certificati e risultati",
    downloadAllButton: "Scarica tutti",
    selectChildTitle: "Seleziona bambino",
    allChildrenButton: "Tutti i bambini",
    certificateLabel: "Certificato",
    awardedToText: "Assegnato a",
    viewDetailsButton: "Visualizza dettagli",
    downloadButton: "Scarica",
    loading: "Caricamento certificati...",
    error: "Errore durante il caricamento dei certificati. Riprova.",
    noCertificatesFound: "Nessun certificato trovato per questo bambino.",
    certificatesTitle: "Certificati"
  },
  // Greek translations
  el: {
    pageTitle: "Πιστοποιητικά και επιτεύγματα",
    downloadAllButton: "Λήψη όλων",
    selectChildTitle: "Επιλογή παιδιού",
    allChildrenButton: "Όλα τα παιδιά",
    certificateLabel: "Πιστοποιητικό",
    awardedToText: "Απονεμήθηκε στον/στην",
    viewDetailsButton: "Προβολή λεπτομερειών",
    downloadButton: "Λήψη",
    loading: "Φόρτωση πιστοποιητικών...",
    error: "Σφάλμα κατά τη φόρτωση πιστοποιητικών. Παρακαλώ προσπαθήστε ξανά.",
    noCertificatesFound: "Δεν βρέθηκαν πιστοποιητικά για αυτό το παιδί.",
    certificatesTitle: "Πιστοποιητικά"
  },
  // Japanese translations
  ja: {
    pageTitle: "証明書と実績",
    downloadAllButton: "すべてダウンロード",
    selectChildTitle: "子供を選択",
    allChildrenButton: "すべての子供",
    certificateLabel: "証明書",
    awardedToText: "授与先",
    viewDetailsButton: "詳細を表示",
    downloadButton: "ダウンロード",
    loading: "証明書を読み込み中...",
    error: "証明書の読み込み中にエラーが発生しました。もう一度お試しください。",
    noCertificatesFound: "この子供の証明書が見つかりません。",
    certificatesTitle: "証明書"
  }
};

// German translations for certificate titles
const certificateTitlesDE = {
  'Web Development Fundamentals': 'Grundlagen der Webentwicklung',
  'Creative Coding for Kids': 'Kreatives Programmieren für Kinder',
  'Robotics Level 1': 'Robotik Level 1'
};

// German translations for skills
const skillsDE = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'JavaScript-Grundlagen',
  'Scratch': 'Scratch',
  'Game Design': 'Spieldesign',
  'Animation': 'Animation',
  'Basic Circuits': 'Grundlagen von Schaltkreisen',
  'Motor Control': 'Motorsteuerung',
  'Sensors': 'Sensoren'
};

// Hebrew translations for certificate titles
const certificateTitlesHE = {
  'Web Development Fundamentals': 'יסודות פיתוח אתרים',
  'Creative Coding for Kids': 'תכנות יצירתי לילדים',
  'Robotics Level 1': 'רובוטיקה רמה 1'
};

// Hebrew translations for skills
const skillsHE = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'יסודות JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'עיצוב משחקים',
  'Animation': 'אנימציה',
  'Basic Circuits': 'מעגלים בסיסיים',
  'Motor Control': 'בקרת מנועים',
  'Sensors': 'חיישנים'
};

// Portuguese translations for certificate titles
const certificateTitlesPT = {
  'Web Development Fundamentals': 'Fundamentos de Desenvolvimento Web',
  'Creative Coding for Kids': 'Programação Criativa para Crianças',
  'Robotics Level 1': 'Robótica Nível 1'
};

// Portuguese translations for skills
const skillsPT = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'Fundamentos de JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'Design de Jogos',
  'Animation': 'Animação',
  'Basic Circuits': 'Circuitos Básicos',
  'Motor Control': 'Controle de Motor',
  'Sensors': 'Sensores'
};

// Arabic translations for certificate titles
const certificateTitlesAR = {
  'Web Development Fundamentals': 'أساسيات تطوير الويب',
  'Creative Coding for Kids': 'البرمجة الإبداعية للأطفال',
  'Robotics Level 1': 'الروبوتات المستوى 1'
};

// Arabic translations for skills
const skillsAR = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'أساسيات JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'تصميم الألعاب',
  'Animation': 'الرسوم المتحركة',
  'Basic Circuits': 'الدوائر الأساسية',
  'Motor Control': 'التحكم في المحركات',
  'Sensors': 'المستشعرات'
};

// Bengali translations for certificate titles
const certificateTitlesBN = {
  'Web Development Fundamentals': 'ওয়েব ডেভেলপমেন্ট ফান্ডামেন্টালস',
  'Creative Coding for Kids': 'শিশুদের জন্য সৃজনশীল কোডিং',
  'Robotics Level 1': 'রোবোটিক্স লেভেল ১'
};

// Bengali translations for skills
const skillsBN = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'জাভাস্ক্রিপ্ট বেসিকস',
  'Scratch': 'স্ক্র্যাচ',
  'Game Design': 'গেম ডিজাইন',
  'Animation': 'অ্যানিমেশন',
  'Basic Circuits': 'বেসিক সার্কিট',
  'Motor Control': 'মোটর কন্ট্রোল',
  'Sensors': 'সেন্সর'
};

// Korean translations for certificate titles
const certificateTitlesKO = {
  'Web Development Fundamentals': '웹 개발 기초',
  'Creative Coding for Kids': '어린이를 위한 창의적 코딩',
  'Robotics Level 1': '로보틱스 레벨 1'
};

// Korean translations for skills
const skillsKO = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': '자바스크립트 기초',
  'Scratch': '스크래치',
  'Game Design': '게임 디자인',
  'Animation': '애니메이션',
  'Basic Circuits': '기본 회로',
  'Motor Control': '모터 제어',
  'Sensors': '센서'
};

// Dutch translations for certificate titles
const certificateTitlesNL = {
  'Web Development Fundamentals': 'Basisprincipes van webontwikkeling',
  'Creative Coding for Kids': 'Creatief programmeren voor kinderen',
  'Robotics Level 1': 'Robotica niveau 1'
};

// Dutch translations for skills
const skillsNL = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'JavaScript-basisprincipes',
  'Scratch': 'Scratch',
  'Game Design': 'Gameontwerp',
  'Animation': 'Animatie',
  'Basic Circuits': 'Basiscircuits',
  'Motor Control': 'Motorbesturing',
  'Sensors': 'Sensoren'
};

// Danish translations for certificate titles
const certificateTitlesDA = {
  'Web Development Fundamentals': 'Grundlæggende webudvikling',
  'Creative Coding for Kids': 'Kreativ kodning for børn',
  'Robotics Level 1': 'Robotteknologi niveau 1'
};

// Danish translations for skills
const skillsDA = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'JavaScript grundlæggende',
  'Scratch': 'Scratch',
  'Game Design': 'Spildesign',
  'Animation': 'Animation',
  'Basic Circuits': 'Grundlæggende kredsløb',
  'Motor Control': 'Motorstyring',
  'Sensors': 'Sensorer'
};

// Polish translations for certificate titles
const certificateTitlesPL = {
  'Web Development Fundamentals': 'Podstawy tworzenia stron internetowych',
  'Creative Coding for Kids': 'Kreatywne kodowanie dla dzieci',
  'Robotics Level 1': 'Robotyka poziom 1'
};

// Polish translations for skills
const skillsPL = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'Podstawy JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'Projektowanie gier',
  'Animation': 'Animacja',
  'Basic Circuits': 'Podstawowe obwody',
  'Motor Control': 'Sterowanie silnikiem',
  'Sensors': 'Czujniki'
};

// Czech translations for certificate titles
const certificateTitlesCS = {
  'Web Development Fundamentals': 'Základy vývoje webových stránek',
  'Creative Coding for Kids': 'Kreativní programování pro děti',
  'Robotics Level 1': 'Robotika úroveň 1'
};

// Czech translations for skills
const skillsCS = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'Základy JavaScriptu',
  'Scratch': 'Scratch',
  'Game Design': 'Návrh her',
  'Animation': 'Animace',
  'Basic Circuits': 'Základní obvody',
  'Motor Control': 'Ovládání motoru',
  'Sensors': 'Senzory'
};

// Italian translations for certificate titles
const certificateTitlesIT = {
  'Web Development Fundamentals': 'Fondamenti di sviluppo web',
  'Creative Coding for Kids': 'Programmazione creativa per bambini',
  'Robotics Level 1': 'Robotica livello 1'
};

// Italian translations for skills
const skillsIT = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'Fondamenti di JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'Design di giochi',
  'Animation': 'Animazione',
  'Basic Circuits': 'Circuiti di base',
  'Motor Control': 'Controllo motore',
  'Sensors': 'Sensori'
};

// Greek translations for certificate titles
const certificateTitlesEL = {
  'Web Development Fundamentals': 'Βασικές αρχές ανάπτυξης ιστού',
  'Creative Coding for Kids': 'Δημιουργικός προγραμματισμός για παιδιά',
  'Robotics Level 1': 'Ρομποτική επίπεδο 1'
};

// Greek translations for skills
const skillsEL = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'Βασικά JavaScript',
  'Scratch': 'Scratch',
  'Game Design': 'Σχεδιασμός παιχνιδιών',
  'Animation': 'Κινούμενα σχέδια',
  'Basic Circuits': 'Βασικά κυκλώματα',
  'Motor Control': 'Έλεγχος κινητήρα',
  'Sensors': 'Αισθητήρες'
};

// Japanese translations for certificate titles
const certificateTitlesJA = {
  'Web Development Fundamentals': 'ウェブ開発の基礎',
  'Creative Coding for Kids': '子供のためのクリエイティブコーディング',
  'Robotics Level 1': 'ロボティクスレベル1'
};

// Japanese translations for skills
const skillsJA = {
  'HTML': 'HTML',
  'CSS': 'CSS',
  'JavaScript Basics': 'JavaScript基礎',
  'Scratch': 'Scratch',
  'Game Design': 'ゲームデザイン',
  'Animation': 'アニメーション',
  'Basic Circuits': '基本回路',
  'Motor Control': 'モーター制御',
  'Sensors': 'センサー'
};

// We'll fetch certificates and children from the API

const CertificatesPage: React.FC = () => {
  const params = useParams();
  const currentLocale = (params.locale as string) || 'en';

  // State for data
  const [selectedChild, setSelectedChild] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for API data
  const [children, setChildren] = useState<Child[]>([]);
  const [certificates, setCertificates] = useState<CertificateWithSkills[]>([]);
  const [filteredCertificates, setFilteredCertificates] = useState<CertificateWithSkills[]>([]);

  // State for certificate details modal
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState<boolean>(false);
  const [selectedCertificate, setSelectedCertificate] = useState<CertificateWithSkills | null>(null);

  // Get translations from JSON files
  const t = useTranslations('learningDocumentation');
  const tCert = useTranslations('LearningDocumentation.Certificates');

  // Fallback to hardcoded translations for specific UI elements not in the JSON files
  const tUI = textTranslations[currentLocale] || textTranslations.en;

  // Function to translate certificate title
  const translateTitle = (title: string) => {
    if (currentLocale === 'de' && title in certificateTitlesDE) {
      return certificateTitlesDE[title as keyof typeof certificateTitlesDE];
    } else if (currentLocale === 'pt' && title in certificateTitlesPT) {
      return certificateTitlesPT[title as keyof typeof certificateTitlesPT];
    } else if (currentLocale === 'he' && title in certificateTitlesHE) {
      return certificateTitlesHE[title as keyof typeof certificateTitlesHE];
    } else if (currentLocale === 'ar' && title in certificateTitlesAR) {
      return certificateTitlesAR[title as keyof typeof certificateTitlesAR];
    } else if (currentLocale === 'bn' && title in certificateTitlesBN) {
      return certificateTitlesBN[title as keyof typeof certificateTitlesBN];
    } else if (currentLocale === 'ko' && title in certificateTitlesKO) {
      return certificateTitlesKO[title as keyof typeof certificateTitlesKO];
    } else if (currentLocale === 'nl' && title in certificateTitlesNL) {
      return certificateTitlesNL[title as keyof typeof certificateTitlesNL];
    } else if (currentLocale === 'da' && title in certificateTitlesDA) {
      return certificateTitlesDA[title as keyof typeof certificateTitlesDA];
    } else if (currentLocale === 'pl' && title in certificateTitlesPL) {
      return certificateTitlesPL[title as keyof typeof certificateTitlesPL];
    } else if (currentLocale === 'cs' && title in certificateTitlesCS) {
      return certificateTitlesCS[title as keyof typeof certificateTitlesCS];
    } else if (currentLocale === 'it' && title in certificateTitlesIT) {
      return certificateTitlesIT[title as keyof typeof certificateTitlesIT];
    } else if (currentLocale === 'el' && title in certificateTitlesEL) {
      return certificateTitlesEL[title as keyof typeof certificateTitlesEL];
    } else if (currentLocale === 'ja' && title in certificateTitlesJA) {
      return certificateTitlesJA[title as keyof typeof certificateTitlesJA];
    }
    return title;
  };

  // Function to translate skills
  const translateSkill = (skill: string) => {
    if (currentLocale === 'de' && skill in skillsDE) {
      return skillsDE[skill as keyof typeof skillsDE];
    } else if (currentLocale === 'pt' && skill in skillsPT) {
      return skillsPT[skill as keyof typeof skillsPT];
    } else if (currentLocale === 'he' && skill in skillsHE) {
      return skillsHE[skill as keyof typeof skillsHE];
    } else if (currentLocale === 'ar' && skill in skillsAR) {
      return skillsAR[skill as keyof typeof skillsAR];
    } else if (currentLocale === 'bn' && skill in skillsBN) {
      return skillsBN[skill as keyof typeof skillsBN];
    } else if (currentLocale === 'ko' && skill in skillsKO) {
      return skillsKO[skill as keyof typeof skillsKO];
    } else if (currentLocale === 'nl' && skill in skillsNL) {
      return skillsNL[skill as keyof typeof skillsNL];
    } else if (currentLocale === 'da' && skill in skillsDA) {
      return skillsDA[skill as keyof typeof skillsDA];
    } else if (currentLocale === 'pl' && skill in skillsPL) {
      return skillsPL[skill as keyof typeof skillsPL];
    } else if (currentLocale === 'cs' && skill in skillsCS) {
      return skillsCS[skill as keyof typeof skillsCS];
    } else if (currentLocale === 'it' && skill in skillsIT) {
      return skillsIT[skill as keyof typeof skillsIT];
    } else if (currentLocale === 'el' && skill in skillsEL) {
      return skillsEL[skill as keyof typeof skillsEL];
    } else if (currentLocale === 'ja' && skill in skillsJA) {
      return skillsJA[skill as keyof typeof skillsJA];
    }
    return skill;
  };

  // Handle opening the certificate details modal
  const handleViewCertificateDetails = (certificate: CertificateWithSkills) => {
    setSelectedCertificate(certificate);
    setIsDetailsModalOpen(true);
  };

  // Handle closing the certificate details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedCertificate(null);
  };

  // Fetch user data and children
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching user data...');

        // Get current user ID
        const userId = await childService.getCurrentUserId();
        console.log('Current user ID:', userId);

        // Get children for the parent
        const childrenData = await childService.getParentChildren(userId);
        console.log('Children data:', childrenData);
        setChildren(childrenData);

        // If we have children, select the first one and fetch their data
        if (childrenData.length > 0) {
          console.log('Selecting first child:', childrenData[0].id);
          setSelectedChild(childrenData[0].id);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(tCert('error'));
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [tCert]);

  // Fetch certificates when a child is selected
  useEffect(() => {
    const fetchCertificates = async () => {
      if (selectedChild === null) return;

      try {
        setIsLoading(true);
        console.log(`Fetching certificates for child ${selectedChild}`);

        // Get certificates for the selected child
        const certificatesData = await certificateService.getChildCertificates(selectedChild);
        console.log('Received certificates data:', certificatesData);

        setCertificates(certificatesData);
        setFilteredCertificates(certificatesData);
        setError(null);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching certificates:', error);
        // Use the translation from JSON files
        setError(tCert('error'));
        setCertificates([]);
        setFilteredCertificates([]);
        setIsLoading(false);
      }
    };

    fetchCertificates();
  }, [selectedChild, tCert]);

  return (
    <DashboardLayout>
      <LearningDocumentationLayout>
        <div className="space-y-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {tCert('certificatesTitle')}
            </h1>

            <button className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
              </svg>
              {tCert('downloadAllButton')}
            </button>
          </div>

          {/* Child selection */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-4 text-white">{tCert('selectChildTitle')}</h2>

            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <div className="flex flex-wrap gap-4">
                {children.length === 0 ? (
                  <p className="text-gray-300">No children found. Please add a child to your account.</p>
                ) : (
                  <>
                    {children.map(child => (
                      <button
                        key={child.id}
                        onClick={() => setSelectedChild(child.id)}
                        className={`flex items-center px-4 py-2 rounded-lg transition-all ${selectedChild === child.id
                          ? 'bg-yellow-400 text-purple-900 font-bold'
                          : 'bg-white/10 text-white hover:bg-white/20'}`}
                      >
                        <div className="w-8 h-8 rounded-full bg-gray-300 mr-2 overflow-hidden">
                          {child.avatar_url ? (
                            <Image
                              src={child.avatar_url}
                              alt={child.name || 'Child avatar'}
                              width={32}
                              height={32}
                              className="object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
                          )}
                        </div>
                        {child.name || `Child ${child.id}`}
                      </button>
                    ))}
                  </>
                )}
              </div>
            )}
          </div>

          {/* Certificates Grid */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-4 text-white">{tCert('certificatesTitle')}</h2>

            {isLoading && (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                <span className="ml-3 text-white">{tCert('loading')}</span>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            {!isLoading && !error && (
              <>
                {filteredCertificates && filteredCertificates.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredCertificates.map(certificate => {
                      // Format the date
                      const issueDate = new Date(certificate.issue_date);
                      const formattedDate = issueDate.toLocaleDateString(currentLocale, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      });

                      // Get the child name
                      const childName = children.find(c => c.id === certificate.child_id)?.name || `Child ${certificate.child_id}`;

                      return (
                        <div
                          key={certificate.id}
                          className="bg-white/5 backdrop-blur-xl rounded-xl border border-white/10 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] group cursor-pointer"
                          onClick={() => handleViewCertificateDetails(certificate)}
                        >
                          <div className="relative h-48 bg-gradient-to-br from-gray-800 to-gray-900 overflow-hidden">
                            {/* Certificate image placeholder */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              {certificate.image_url ? (
                                // Clean Google redirect URLs if present
                                (() => {
                                  let imageUrl = certificate.image_url;

                                  // Check if it's a Google redirect URL
                                  if (imageUrl.includes('google.com/url?') && imageUrl.includes('&url=')) {
                                    try {
                                      // Extract the actual URL from the Google redirect
                                      const match = imageUrl.match(/&url=([^&]+)/);
                                      if (match && match[1]) {
                                        // Decode the URL
                                        imageUrl = decodeURIComponent(match[1]);
                                        console.log('Cleaned Google redirect URL:', imageUrl);
                                      }
                                    } catch (e) {
                                      console.error('Error parsing Google redirect URL:', e);
                                    }
                                  }

                                  // Use regular img tag instead of Next.js Image component for better compatibility
                                  return (
                                    <img
                                      src={imageUrl}
                                      alt={certificate.title_key}
                                      className="max-w-full max-h-full object-contain"
                                      onError={(e) => {
                                        // Fallback to a default image if loading fails
                                        const target = e.target as HTMLImageElement;
                                        target.src = "https://images.unsplash.com/photo-1569705460033-cfaa4bf9f822?q=80&w=200&h=150&auto=format";
                                      }}
                                    />
                                  );
                                })()
                              ) : (
                                <div className="w-20 h-20 bg-yellow-400 rounded-full flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-purple-900" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </div>

                            {/* Decoration elements */}
                            <div className="absolute top-4 left-4 bg-yellow-400 text-purple-900 text-xs font-bold px-2 py-1 rounded">
                              {tCert('certificateLabel')}
                            </div>
                            <div className="absolute bottom-0 inset-x-0 h-1/3 bg-gradient-to-t from-black to-transparent"></div>
                          </div>

                          <div className="p-6">
                            <div className="flex justify-between items-start mb-4">
                              <h3 className="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">
                                {translateTitle(certificate.title_key)}
                              </h3>
                              <span className="text-xs text-gray-400">{formattedDate}</span>
                            </div>

                            <p className="text-sm text-gray-300 mb-4">{tCert('awardedToText')} <span className="text-yellow-400 font-medium">{childName}</span></p>

                            {certificate.skills && certificate.skills.length > 0 && (
                              <div className="flex flex-wrap gap-2 mb-4">
                                {certificate.skills.map((skill, index) => (
                                  <span key={index} className="text-xs bg-white/10 text-gray-300 px-2 py-1 rounded-full">
                                    {translateSkill(skill.name_key)}
                                  </span>
                                ))}
                              </div>
                            )}

                            <div className="flex justify-between mt-6">
                              <button
                                className="text-sm text-yellow-400 hover:text-yellow-300 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent card click
                                  handleViewCertificateDetails(certificate);
                                }}
                              >
                                {tCert('viewDetailsButton')}
                              </button>
                              {certificate.image_url && (
                                <button
                                  className="text-sm text-blue-400 hover:text-blue-300 transition-colors flex items-center"
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent card click
                                    // Download functionality would go here
                                    console.log('Download certificate:', certificate.id);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                  {tCert('downloadButton')}
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-400">{tCert('noCertificatesFound')}</p>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Certificate Details Modal */}
          {isDetailsModalOpen && selectedCertificate && (
            <CertificateDetailsModal
              isOpen={isDetailsModalOpen}
              onClose={handleCloseDetailsModal}
              certificate={selectedCertificate}
              children={children}
              locale={currentLocale}
              translateSkill={translateSkill}
              translateTitle={translateTitle}
            />
          )}
        </div>
      </LearningDocumentationLayout>
    </DashboardLayout>
  );
};

export default CertificatesPage;
