"use client";

import React, { useState, useEffect } from 'react';
import DashboardLayout from '../../../components/DashboardLayout';
import LearningDocumentationLayout from '../../../components/LearningDocumentationLayout';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

// Import services
import { learningEventService } from '../../../../services/learningEventService';
import { childService } from '../../../../services/childService';
import { skillService } from '../../../../services/skillService';

// Import types
import { Child } from '../../../../types/child';
import { LearningEventWithDetails } from '../../../../types/learningEvent';
import { Skill } from '../../../../types/skill';

const eventTypeColors = {
  'achievement': 'bg-blue-500/20 border-blue-500/30',
  'milestone': 'bg-yellow-500/20 border-yellow-500/30',
  'assessment': 'bg-purple-500/20 border-purple-500/30',
  'test': 'bg-green-500/20 border-green-500/30',
  'exam': 'bg-red-500/20 border-red-500/30',
  'report': 'bg-indigo-500/20 border-indigo-500/30',
  'presentation': 'bg-orange-500/20 border-orange-500/30',
  'project': 'bg-green-500/20 border-green-500/30',
  'workshop': 'bg-pink-500/20 border-pink-500/30',
  'other': 'bg-gray-500/20 border-gray-500/30'
};

// Event type translations
const eventTypeTranslations = {
  en: {
    'achievement': 'Achievement',
    'milestone': 'Milestone',
    'assessment': 'Assessment',
    'test': 'Test',
    'exam': 'Exam',
    'report': 'Report',
    'presentation': 'Presentation',
    'project': 'Project',
    'workshop': 'Workshop',
    'other': 'Other'
  },
  de: {
    'achievement': 'Erfolg',
    'milestone': 'Meilenstein',
    'assessment': 'Bewertung',
    'test': 'Test',
    'exam': 'Prüfung',
    'report': 'Bericht',
    'presentation': 'Präsentation',
    'project': 'Projekt',
    'workshop': 'Workshop',
    'other': 'Sonstiges'
  },
  he: {
    'achievement': 'הישג',
    'milestone': 'אבן דרך',
    'assessment': 'הערכה',
    'test': 'מבחן',
    'exam': 'בחינה',
    'report': 'דוח',
    'presentation': 'מצגת',
    'project': 'פרויקט',
    'workshop': 'סדנה',
    'other': 'אחר'
  },
  pt: {
    'achievement': 'Conquista',
    'milestone': 'Marco',
    'assessment': 'Avaliação',
    'test': 'Teste',
    'exam': 'Exame',
    'report': 'Relatório',
    'presentation': 'Apresentação',
    'project': 'Projeto',
    'workshop': 'Workshop',
    'other': 'Outro'
  },
  ar: {
    'achievement': 'إنجاز',
    'milestone': 'معلم رئيسي',
    'assessment': 'تقييم',
    'test': 'اختبار',
    'exam': 'امتحان',
    'report': 'تقرير',
    'presentation': 'عرض تقديمي',
    'project': 'مشروع',
    'workshop': 'ورشة عمل',
    'other': 'أخرى'
  },
  bn: {
    'achievement': 'অর্জন',
    'milestone': 'মাইলস্টোন',
    'assessment': 'মূল্যায়ন',
    'test': 'পরীক্ষা',
    'exam': 'পরীক্ষা',
    'report': 'প্রতিবেদন',
    'presentation': 'উপস্থাপনা',
    'project': 'প্রকল্প',
    'workshop': 'কর্মশালা',
    'other': 'অন্যান্য'
  },
  ko: {
    'achievement': '성취',
    'milestone': '마일스톤',
    'assessment': '평가',
    'test': '테스트',
    'exam': '시험',
    'report': '보고서',
    'presentation': '발표',
    'project': '프로젝트',
    'workshop': '워크숍',
    'other': '기타'
  },
  nl: {
    'achievement': 'Prestatie',
    'milestone': 'Mijlpaal',
    'assessment': 'Beoordeling',
    'test': 'Test',
    'exam': 'Examen',
    'report': 'Rapport',
    'presentation': 'Presentatie',
    'project': 'Project',
    'workshop': 'Workshop',
    'other': 'Overig'
  },
  da: {
    'achievement': 'Præstation',
    'milestone': 'Milepæl',
    'assessment': 'Vurdering',
    'test': 'Test',
    'exam': 'Eksamen',
    'report': 'Rapport',
    'presentation': 'Præsentation',
    'project': 'Projekt',
    'workshop': 'Workshop',
    'other': 'Andet'
  },
  pl: {
    'achievement': 'Osiągnięcie',
    'milestone': 'Kamień milowy',
    'assessment': 'Ocena',
    'test': 'Test',
    'exam': 'Egzamin',
    'report': 'Raport',
    'presentation': 'Prezentacja',
    'project': 'Projekt',
    'workshop': 'Warsztat',
    'other': 'Inne'
  },
  cs: {
    'achievement': 'Úspěch',
    'milestone': 'Milník',
    'assessment': 'Hodnocení',
    'test': 'Test',
    'exam': 'Zkouška',
    'report': 'Zpráva',
    'presentation': 'Prezentace',
    'project': 'Projekt',
    'workshop': 'Workshop',
    'other': 'Jiné'
  },
  it: {
    'achievement': 'Risultato',
    'milestone': 'Pietra miliare',
    'assessment': 'Valutazione',
    'test': 'Test',
    'exam': 'Esame',
    'report': 'Rapporto',
    'presentation': 'Presentazione',
    'project': 'Progetto',
    'workshop': 'Workshop',
    'other': 'Altro'
  },
  el: {
    'achievement': 'Επίτευγμα',
    'milestone': 'Ορόσημο',
    'assessment': 'Αξιολόγηση',
    'test': 'Τεστ',
    'exam': 'Εξέταση',
    'report': 'Αναφορά',
    'presentation': 'Παρουσίαση',
    'project': 'Έργο',
    'workshop': 'Εργαστήριο',
    'other': 'Άλλο'
  },
  ja: {
    'achievement': '達成',
    'milestone': 'マイルストーン',
    'assessment': '評価',
    'test': 'テスト',
    'exam': '試験',
    'report': 'レポート',
    'presentation': 'プレゼンテーション',
    'project': 'プロジェクト',
    'workshop': 'ワークショップ',
    'other': 'その他'
  }
};

// Page text translations
const textTranslations: Record<string, {
  pageTitle: string;
  timeframeAllTime: string;
  timeframePastMonth: string;
  timeframePastQuarter: string;
  timeframePastYear: string;
  exportTimelineButton: string;
  selectChildTitle: string;
  allChildrenButton: string;
  filterByEventTypeTitle: string;
  allEventsButton: string;
  learningEventsTitle: string;
  skillProgressLabel: string;
  relatedSkillsLabel: string;
  noEventsMatchMessage: string;
  noEventsYetMessage: string;
  eventsCreatedInAssistantsMessage: string;
  resetFiltersButton: string;
  loading: string;
  error: string;
}> = {
  en: {
    pageTitle: "Learning Timeline",
    timeframeAllTime: "All Time",
    timeframePastMonth: "Past Month",
    timeframePastQuarter: "Past 3 Months",
    timeframePastYear: "Past Year",
    exportTimelineButton: "Export Timeline",
    selectChildTitle: "Select Child",
    allChildrenButton: "All Children",
    filterByEventTypeTitle: "Filter by Event Type",
    allEventsButton: "All Events",
    learningEventsTitle: "Learning Events",
    skillProgressLabel: "Skill Progress",
    relatedSkillsLabel: "Related Skills",
    noEventsMatchMessage: "No events match your current filters.",
    noEventsYetMessage: "No learning events have been recorded for this child yet.",
    eventsCreatedInAssistantsMessage: "Events will appear here once they are created in the assistants app.",
    resetFiltersButton: "Reset filters",
    loading: "Loading events...",
    error: "Error loading events. Please try again."
  },
  de: {
    pageTitle: "Lern-Zeitlinie",
    timeframeAllTime: "Gesamter Zeitraum",
    timeframePastMonth: "Letzter Monat",
    timeframePastQuarter: "Letzte 3 Monate",
    timeframePastYear: "Letztes Jahr",
    exportTimelineButton: "Zeitlinie exportieren",
    selectChildTitle: "Kind auswählen",
    allChildrenButton: "Alle Kinder",
    filterByEventTypeTitle: "Nach Ereignistyp filtern",
    allEventsButton: "Alle Ereignisse",
    learningEventsTitle: "Lernereignisse",
    skillProgressLabel: "Fortschritt der Fähigkeiten",
    relatedSkillsLabel: "Zugehörige Fähigkeiten",
    noEventsMatchMessage: "Keine Ereignisse entsprechen Ihren aktuellen Filtern.",
    noEventsYetMessage: "Für dieses Kind wurden noch keine Lernereignisse erfasst.",
    eventsCreatedInAssistantsMessage: "Ereignisse werden hier angezeigt, sobald sie in der Assistenten-App erstellt wurden.",
    resetFiltersButton: "Filter zurücksetzen",
    loading: "Ereignisse werden geladen...",
    error: "Fehler beim Laden der Ereignisse. Bitte versuchen Sie es erneut."
  },
  he: {
    pageTitle: "ציר זמן למידה",
    timeframeAllTime: "כל הזמן",
    timeframePastMonth: "חודש אחרון",
    timeframePastQuarter: "3 חודשים אחרונים",
    timeframePastYear: "שנה אחרונה",
    exportTimelineButton: "ייצא ציר זמן",
    selectChildTitle: "בחר ילד",
    allChildrenButton: "כל הילדים",
    filterByEventTypeTitle: "סנן לפי סוג אירוע",
    allEventsButton: "כל האירועים",
    learningEventsTitle: "אירועי למידה",
    skillProgressLabel: "התקדמות במיומנות",
    relatedSkillsLabel: "מיומנויות קשורות",
    noEventsMatchMessage: "אין אירועים התואמים את הסינון הנוכחי.",
    noEventsYetMessage: "טרם נרשמו אירועי למידה עבור ילד זה.",
    eventsCreatedInAssistantsMessage: "אירועים יופיעו כאן לאחר שייווצרו באפליקציית המסייעים.",
    resetFiltersButton: "אפס סינונים",
    loading: "טוען אירועים...",
    error: "שגיאה בטעינת אירועים. אנא נסה שוב."
  },
  pt: {
    pageTitle: "Linha do Tempo de Aprendizagem",
    timeframeAllTime: "Todo o Período",
    timeframePastMonth: "Último Mês",
    timeframePastQuarter: "Últimos 3 Meses",
    timeframePastYear: "Último Ano",
    exportTimelineButton: "Exportar Linha do Tempo",
    selectChildTitle: "Selecionar Criança",
    allChildrenButton: "Todas as Crianças",
    filterByEventTypeTitle: "Filtrar por Tipo de Evento",
    allEventsButton: "Todos os Eventos",
    learningEventsTitle: "Eventos de Aprendizagem",
    skillProgressLabel: "Progresso da Habilidade",
    relatedSkillsLabel: "Habilidades Relacionadas",
    noEventsMatchMessage: "Nenhum evento corresponde aos seus filtros atuais.",
    noEventsYetMessage: "Nenhum evento de aprendizagem foi registrado para esta criança ainda.",
    eventsCreatedInAssistantsMessage: "Os eventos aparecerão aqui depois de serem criados no aplicativo de assistentes.",
    resetFiltersButton: "Redefinir filtros",
    loading: "Carregando eventos...",
    error: "Erro ao carregar eventos. Por favor, tente novamente."
  },
  ar: {
    pageTitle: "المسار الزمني للتعلم",
    timeframeAllTime: "كل الوقت",
    timeframePastMonth: "الشهر الماضي",
    timeframePastQuarter: "آخر 3 أشهر",
    timeframePastYear: "السنة الماضية",
    exportTimelineButton: "تصدير المسار الزمني",
    selectChildTitle: "اختر الطفل",
    allChildrenButton: "جميع الأطفال",
    filterByEventTypeTitle: "تصفية حسب نوع الحدث",
    allEventsButton: "جميع الأحداث",
    learningEventsTitle: "أحداث التعلم",
    skillProgressLabel: "تقدم المهارة",
    relatedSkillsLabel: "المهارات ذات الصلة",
    noEventsMatchMessage: "لا توجد أحداث تطابق المرشحات الحالية.",
    noEventsYetMessage: "لم يتم تسجيل أي أحداث تعليمية لهذا الطفل بعد.",
    eventsCreatedInAssistantsMessage: "ستظهر الأحداث هنا بمجرد إنشائها في تطبيق المساعدين.",
    resetFiltersButton: "إعادة تعيين المرشحات",
    loading: "جاري تحميل الأحداث...",
    error: "خطأ في تحميل الأحداث. يرجى المحاولة مرة أخرى."
  },
  // Bengali translations
  bn: {
    pageTitle: "শিক্ষার সময়রেখা",
    timeframeAllTime: "সব সময়",
    timeframePastMonth: "গত মাস",
    timeframePastQuarter: "গত ৩ মাস",
    timeframePastYear: "গত বছর",
    exportTimelineButton: "সময়রেখা এক্সপোর্ট করুন",
    selectChildTitle: "সন্তান নির্বাচন করুন",
    allChildrenButton: "সব সন্তান",
    filterByEventTypeTitle: "ইভেন্ট ধরন অনুযায়ী ফিল্টার করুন",
    allEventsButton: "সব ইভেন্ট",
    learningEventsTitle: "শিক্ষার ইভেন্ট",
    skillProgressLabel: "দক্ষতা অগ্রগতি",
    relatedSkillsLabel: "সম্পর্কিত দক্ষতা",
    noEventsMatchMessage: "আপনার বর্তমান ফিল্টারের সাথে কোন ইভেন্ট মেলে না।",
    noEventsYetMessage: "এই শিশুর জন্য এখনও কোন শিক্ষা ইভেন্ট রেকর্ড করা হয়নি।",
    eventsCreatedInAssistantsMessage: "সহায়ক অ্যাপে তৈরি হওয়ার পরে ইভেন্টগুলি এখানে প্রদর্শিত হবে।",
    resetFiltersButton: "ফিল্টার রিসেট করুন",
    loading: "ইভেন্ট লোড হচ্ছে...",
    error: "ইভেন্ট লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।"
  },
  // Korean translations
  ko: {
    pageTitle: "학습 타임라인",
    timeframeAllTime: "전체 기간",
    timeframePastMonth: "지난 달",
    timeframePastQuarter: "지난 3개월",
    timeframePastYear: "지난 해",
    exportTimelineButton: "타임라인 내보내기",
    selectChildTitle: "자녀 선택",
    allChildrenButton: "모든 자녀",
    filterByEventTypeTitle: "이벤트 유형별 필터링",
    allEventsButton: "모든 이벤트",
    learningEventsTitle: "학습 이벤트",
    skillProgressLabel: "기술 진행 상황",
    relatedSkillsLabel: "관련 기술",
    noEventsMatchMessage: "현재 필터와 일치하는 이벤트가 없습니다.",
    noEventsYetMessage: "이 아이를 위한 학습 이벤트가 아직 기록되지 않았습니다.",
    eventsCreatedInAssistantsMessage: "어시스턴트 앱에서 생성되면 여기에 이벤트가 표시됩니다.",
    resetFiltersButton: "필터 초기화",
    loading: "이벤트 로딩 중...",
    error: "이벤트 로딩 중 오류가 발생했습니다. 다시 시도해 주세요."
  },
  // Dutch translations
  nl: {
    pageTitle: "Leertijdlijn",
    timeframeAllTime: "Alle tijd",
    timeframePastMonth: "Afgelopen maand",
    timeframePastQuarter: "Afgelopen 3 maanden",
    timeframePastYear: "Afgelopen jaar",
    exportTimelineButton: "Tijdlijn exporteren",
    selectChildTitle: "Kind selecteren",
    allChildrenButton: "Alle kinderen",
    filterByEventTypeTitle: "Filteren op gebeurtenistype",
    allEventsButton: "Alle gebeurtenissen",
    learningEventsTitle: "Leergebeurtenissen",
    skillProgressLabel: "Vaardigheidsvoortgang",
    relatedSkillsLabel: "Gerelateerde vaardigheden",
    noEventsMatchMessage: "Geen gebeurtenissen komen overeen met je huidige filters.",
    noEventsYetMessage: "Er zijn nog geen leergebeurtenissen geregistreerd voor dit kind.",
    eventsCreatedInAssistantsMessage: "Gebeurtenissen verschijnen hier zodra ze zijn aangemaakt in de assistenten-app.",
    resetFiltersButton: "Filters resetten",
    loading: "Gebeurtenissen laden...",
    error: "Fout bij het laden van gebeurtenissen. Probeer het opnieuw."
  },
  // Danish translations
  da: {
    pageTitle: "Læringstidslinje",
    timeframeAllTime: "Hele perioden",
    timeframePastMonth: "Sidste måned",
    timeframePastQuarter: "Sidste 3 måneder",
    timeframePastYear: "Sidste år",
    exportTimelineButton: "Eksportér tidslinje",
    selectChildTitle: "Vælg barn",
    allChildrenButton: "Alle børn",
    filterByEventTypeTitle: "Filtrer efter begivenhedstype",
    allEventsButton: "Alle begivenheder",
    learningEventsTitle: "Læringsbegivenheder",
    skillProgressLabel: "Færdighedsfremgang",
    relatedSkillsLabel: "Relaterede færdigheder",
    noEventsMatchMessage: "Ingen begivenheder matcher dine nuværende filtre.",
    noEventsYetMessage: "Der er endnu ikke registreret læringsbegivenheder for dette barn.",
    eventsCreatedInAssistantsMessage: "Begivenheder vil blive vist her, når de er oprettet i assistent-appen.",
    resetFiltersButton: "Nulstil filtre",
    loading: "Indlæser begivenheder...",
    error: "Fejl ved indlæsning af begivenheder. Prøv venligst igen."
  },
  // Polish translations
  pl: {
    pageTitle: "Oś czasu nauki",
    timeframeAllTime: "Cały okres",
    timeframePastMonth: "Ostatni miesiąc",
    timeframePastQuarter: "Ostatnie 3 miesiące",
    timeframePastYear: "Ostatni rok",
    exportTimelineButton: "Eksportuj oś czasu",
    selectChildTitle: "Wybierz dziecko",
    allChildrenButton: "Wszystkie dzieci",
    filterByEventTypeTitle: "Filtruj według typu wydarzenia",
    allEventsButton: "Wszystkie wydarzenia",
    learningEventsTitle: "Wydarzenia edukacyjne",
    skillProgressLabel: "Postęp umiejętności",
    relatedSkillsLabel: "Powiązane umiejętności",
    noEventsMatchMessage: "Żadne wydarzenia nie pasują do bieżących filtrów.",
    noEventsYetMessage: "Dla tego dziecka nie zarejestrowano jeszcze żadnych wydarzeń edukacyjnych.",
    eventsCreatedInAssistantsMessage: "Wydarzenia pojawią się tutaj po utworzeniu ich w aplikacji asystentów.",
    resetFiltersButton: "Resetuj filtry",
    loading: "Ładowanie wydarzeń...",
    error: "Błąd podczas ładowania wydarzeń. Spróbuj ponownie."
  },
  // Czech translations
  cs: {
    pageTitle: "Časová osa učení",
    timeframeAllTime: "Celé období",
    timeframePastMonth: "Poslední měsíc",
    timeframePastQuarter: "Poslední 3 měsíce",
    timeframePastYear: "Poslední rok",
    exportTimelineButton: "Exportovat časovou osu",
    selectChildTitle: "Vybrat dítě",
    allChildrenButton: "Všechny děti",
    filterByEventTypeTitle: "Filtrovat podle typu události",
    allEventsButton: "Všechny události",
    learningEventsTitle: "Události učení",
    skillProgressLabel: "Pokrok v dovednostech",
    relatedSkillsLabel: "Související dovednosti",
    noEventsMatchMessage: "Žádné události neodpovídají vašim aktuálním filtrům.",
    noEventsYetMessage: "Pro toto dítě zatím nebyly zaznamenány žádné události učení.",
    eventsCreatedInAssistantsMessage: "Události se zde zobrazí, jakmile budou vytvořeny v aplikaci asistentů.",
    resetFiltersButton: "Resetovat filtry",
    loading: "Načítání událostí...",
    error: "Chyba při načítání událostí. Zkuste to prosím znovu."
  },
  // Italian translations
  it: {
    pageTitle: "Cronologia di apprendimento",
    timeframeAllTime: "Tutto il periodo",
    timeframePastMonth: "Ultimo mese",
    timeframePastQuarter: "Ultimi 3 mesi",
    timeframePastYear: "Ultimo anno",
    exportTimelineButton: "Esporta cronologia",
    selectChildTitle: "Seleziona bambino",
    allChildrenButton: "Tutti i bambini",
    filterByEventTypeTitle: "Filtra per tipo di evento",
    allEventsButton: "Tutti gli eventi",
    learningEventsTitle: "Eventi di apprendimento",
    skillProgressLabel: "Progresso delle competenze",
    relatedSkillsLabel: "Competenze correlate",
    noEventsMatchMessage: "Nessun evento corrisponde ai filtri attuali.",
    noEventsYetMessage: "Non sono ancora stati registrati eventi di apprendimento per questo bambino.",
    eventsCreatedInAssistantsMessage: "Gli eventi appariranno qui una volta creati nell'app degli assistenti.",
    resetFiltersButton: "Reimposta filtri",
    loading: "Caricamento eventi...",
    error: "Errore durante il caricamento degli eventi. Riprova."
  },
  // Greek translations
  el: {
    pageTitle: "Χρονοδιάγραμμα μάθησης",
    timeframeAllTime: "Όλη η περίοδος",
    timeframePastMonth: "Τελευταίος μήνας",
    timeframePastQuarter: "Τελευταίοι 3 μήνες",
    timeframePastYear: "Τελευταίο έτος",
    exportTimelineButton: "Εξαγωγή χρονοδιαγράμματος",
    selectChildTitle: "Επιλογή παιδιού",
    allChildrenButton: "Όλα τα παιδιά",
    filterByEventTypeTitle: "Φιλτράρισμα ανά τύπο συμβάντος",
    allEventsButton: "Όλα τα συμβάντα",
    learningEventsTitle: "Συμβάντα μάθησης",
    skillProgressLabel: "Πρόοδος δεξιοτήτων",
    relatedSkillsLabel: "Σχετικές δεξιότητες",
    noEventsMatchMessage: "Κανένα συμβάν δεν ταιριάζει με τα τρέχοντα φίλτρα σας.",
    noEventsYetMessage: "Δεν έχουν καταγραφεί ακόμη συμβάντα μάθησης για αυτό το παιδί.",
    eventsCreatedInAssistantsMessage: "Τα συμβάντα θα εμφανιστούν εδώ μόλις δημιουργηθούν στην εφαρμογή βοηθών.",
    resetFiltersButton: "Επαναφορά φίλτρων",
    loading: "Φόρτωση συμβάντων...",
    error: "Σφάλμα κατά τη φόρτωση συμβάντων. Παρακαλώ προσπαθήστε ξανά."
  },
  // Japanese translations
  ja: {
    pageTitle: "学習タイムライン",
    timeframeAllTime: "全期間",
    timeframePastMonth: "過去1ヶ月",
    timeframePastQuarter: "過去3ヶ月",
    timeframePastYear: "過去1年",
    exportTimelineButton: "タイムラインをエクスポート",
    selectChildTitle: "子供を選択",
    allChildrenButton: "すべての子供",
    filterByEventTypeTitle: "イベントタイプでフィルター",
    allEventsButton: "すべてのイベント",
    learningEventsTitle: "学習イベント",
    skillProgressLabel: "スキル進捗",
    relatedSkillsLabel: "関連スキル",
    noEventsMatchMessage: "現在のフィルターに一致するイベントはありません。",
    noEventsYetMessage: "この子供の学習イベントはまだ記録されていません。",
    eventsCreatedInAssistantsMessage: "アシスタントアプリで作成されると、イベントがここに表示されます。",
    resetFiltersButton: "フィルターをリセット",
    loading: "イベントを読み込み中...",
    error: "イベントの読み込み中にエラーが発生しました。もう一度お試しください。"
  }
};

const TimelinePage: React.FC = () => {
  const params = useParams();
  const currentLocale = (params.locale as string) || 'en';

  // State for data
  const [selectedChild, setSelectedChild] = useState<number | null>(null);
  const [timeframe, setTimeframe] = useState('all');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for API data
  const [children, setChildren] = useState<Child[]>([]);
  const [learningEvents, setLearningEvents] = useState<LearningEventWithDetails[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<LearningEventWithDetails[]>([]);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [isLoadingSkills, setIsLoadingSkills] = useState(false);

  // Get translations from the JSON files
  const t = useTranslations('LearningDocumentation.Timeline');
  const tEventTypes = useTranslations('LearningDocumentation.eventTypes');

  // Keep the event type translations for now as fallback
  const eventTypes = eventTypeTranslations[currentLocale as keyof typeof eventTypeTranslations] || eventTypeTranslations.en;

  // Check API configuration on component mount
  useEffect(() => {
    // Log API URL to verify it's properly configured
    console.log('Timeline page: API URL from environment:', process.env.NEXT_PUBLIC_API_URL);
  }, []);

  // Fetch user data and children
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        console.log('Timeline page: Fetching user data...');

        // Get current user ID
        const userId = await childService.getCurrentUserId();
        console.log(`Timeline page: Current user ID: ${userId}`);

        // Get children for the parent
        const childrenData = await childService.getParentChildren(userId);
        console.log(`Timeline page: Fetched ${childrenData.length} children`);
        setChildren(childrenData);

        // If we have children, select the first one and fetch their data
        if (childrenData.length > 0) {
          console.log(`Timeline page: Selecting first child with ID ${childrenData[0].id}`);
          setSelectedChild(childrenData[0].id);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Timeline page: Error fetching user data:', error);
        setError('Failed to load user data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Fetch learning events when a child is selected or timeframe changes
  useEffect(() => {
    const fetchLearningEvents = async () => {
      if (selectedChild === null) return;

      try {
        setIsLoading(true);
        console.log(`Timeline page: Fetching learning events for child ID ${selectedChild}`);
        console.log(`Timeline page: Using timeframe: ${timeframe}`);
        console.log(`Timeline page: Start date: ${getTimeframeStartDate(timeframe)}`);
        // Use a future date (end of 2030) to include all events including future ones
        const futureEndDate = new Date('2030-12-31T23:59:59.999Z').toISOString();
        console.log(`Timeline page: End date: ${futureEndDate}`);

        // Get learning events for the selected child
        const eventsData = await learningEventService.getChildLearningEvents(
          selectedChild,
          50, // Limit to 50 events
          0,  // No offset
          getTimeframeStartDate(timeframe),
          futureEndDate
        );

        console.log(`Timeline page: Successfully fetched ${eventsData.length} learning events`);
        if (eventsData.length > 0) {
          console.log('Timeline page: First event:', JSON.stringify(eventsData[0], null, 2));
        } else {
          console.log('Timeline page: No events found for this child. This is expected if no events have been created yet.');
        }

        // Set the events in state
        setLearningEvents(Array.isArray(eventsData) ? eventsData : []);
        setIsLoading(false);
      } catch (error) {
        console.error('Timeline page: Error fetching learning events:', error);
        setError('Failed to load learning events. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchLearningEvents();
  }, [selectedChild, timeframe]);

  // Fetch all skills from all skill areas
  useEffect(() => {
    const fetchAllSkills = async () => {
      try {
        setIsLoadingSkills(true);

        // First get all skill areas
        const skillAreas = await skillService.getSkillAreas();

        // Then get skills for each area
        let allSkills: Skill[] = [];
        for (const area of skillAreas) {
          try {
            const areaSkills = await skillService.getSkillsByArea(area.id);
            // Add area name to each skill for display
            const skillsWithArea = areaSkills.map(skill => ({
              ...skill,
              skill_area_name: area.name_key
            }));
            allSkills = [...allSkills, ...skillsWithArea];
          } catch (err) {
            console.error(`Error fetching skills for area ${area.id}:`, err);
          }
        }

        setSkills(allSkills);
      } catch (err) {
        console.error('Error fetching skill areas:', err);
      } finally {
        setIsLoadingSkills(false);
      }
    };

    fetchAllSkills();
  }, []);

  // Helper function to get the start date for a timeframe
  const getTimeframeStartDate = (timeframe: string): string | undefined => {
    const now = new Date();

    switch (timeframe) {
      case 'month':
        const oneMonthAgo = new Date(now);
        oneMonthAgo.setMonth(now.getMonth() - 1);
        return oneMonthAgo.toISOString();
      case 'quarter':
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        return threeMonthsAgo.toISOString();
      case 'year':
        const oneYearAgo = new Date(now);
        oneYearAgo.setFullYear(now.getFullYear() - 1);
        return oneYearAgo.toISOString();
      case 'all':
      default:
        return undefined; // No start date filter
    }
  };

  // Filter events based on event type
  useEffect(() => {
    // Ensure learningEvents is an array before proceeding
    if (!Array.isArray(learningEvents)) {
      setFilteredEvents([]);
      return;
    }

    let filtered = [...learningEvents];

    if (filterType) {
      filtered = filtered.filter(event => event.event_type === filterType);
    }

    // Sort by date, newest first
    filtered = filtered.sort((a, b) => new Date(b.event_date).getTime() - new Date(a.event_date).getTime());

    setFilteredEvents(filtered);
  }, [learningEvents, filterType]);

  return (
    <DashboardLayout>
      <LearningDocumentationLayout>
        <div className="space-y-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 to-yellow-500">
              {t('pageTitle')}
            </h1>

            <div className="flex space-x-3">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                <option value="all">{t('timeframeAllTime')}</option>
                <option value="month">{t('timeframePastMonth')}</option>
                <option value="quarter">{t('timeframePastQuarter')}</option>
                <option value="year">{t('timeframePastYear')}</option>
              </select>

              <button className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-purple-900 font-bold px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                </svg>
                {t('exportTimelineButton')}
              </button>
            </div>
          </div>

          {/* Filter section */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Child selection */}
            <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-4 text-white">{t('selectChildTitle')}</h2>

              {isLoading && (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                </div>
              )}

              {error && (
                <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                  {t('error')}
                </div>
              )}

              {!isLoading && !error && (
                <div className="flex flex-col space-y-2">
                  {children.length === 0 ? (
                    <p className="text-gray-300">No children found. Please add a child to your account.</p>
                  ) : (
                    <>
                      {children.map(child => (
                        <button
                          key={child.id}
                          onClick={() => setSelectedChild(child.id)}
                          className={`flex items-center px-4 py-2 rounded-lg transition-all ${selectedChild === child.id
                            ? 'bg-yellow-400 text-purple-900 font-bold'
                            : 'bg-white/10 text-white hover:bg-white/20'}`}
                        >
                          <div className="w-8 h-8 rounded-full bg-gray-300 mr-2 overflow-hidden">
                            {child.avatar_url ? (
                              <Image
                                src={child.avatar_url}
                                alt={child.name || 'Child avatar'}
                                width={32}
                                height={32}
                                className="object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
                            )}
                          </div>
                          {child.name || `Child ${child.id}`}
                        </button>
                      ))}
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Event Type Filter */}
            <div className="md:col-span-3 bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
              <h2 className="text-xl font-bold mb-4 text-white">{t('filterByEventTypeTitle')}</h2>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setFilterType(null)}
                  className={`px-4 py-2 rounded-lg transition-all ${filterType === null
                    ? 'bg-yellow-400 text-purple-900 font-bold'
                    : 'bg-white/10 text-white hover:bg-white/20'}`}
                >
                  {t('allEventsButton')}
                </button>

                {Object.keys(eventTypeColors).map((type) => (
                  <button
                    key={type}
                    onClick={() => setFilterType(type)}
                    className={`px-4 py-2 rounded-lg transition-all ${filterType === type
                      ? 'bg-yellow-400 text-purple-900 font-bold'
                      : 'bg-white/10 text-white hover:bg-white/20'}`}
                  >
                    {eventTypes[type as keyof typeof eventTypes]}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
            <h2 className="text-xl font-bold mb-6 text-white">{t('learningEventsTitle')}</h2>

            {isLoading && (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400"></div>
                <span className="ml-3 text-white">{t('loading')}</span>
              </div>
            )}

            {error && (
              <div className="bg-red-500/20 text-red-200 p-4 rounded-lg mb-4">
                {t('error')}
              </div>
            )}



            {!isLoading && !error && (
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-white/20"></div>

                {/* Timeline events */}
                <div className="space-y-8">
                  {filteredEvents.length > 0 ? (
                    filteredEvents.map((event) => {
                      // Format the date
                      const eventDate = new Date(event.event_date);
                      const formattedDate = eventDate.toLocaleDateString(currentLocale, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      });

                      // Get the child name
                      const childName = children.find(c => c.id === event.child_id)?.name || `Child ${event.child_id}`;

                      return (
                        <div key={event.id} className="relative flex items-start">
                          <div className="absolute left-8 top-0 -ml-3 h-6 w-6 rounded-full border-2 border-yellow-400 bg-gray-900 z-10"></div>

                          <div className="min-w-[120px] pr-4 text-right">
                            <span className="text-sm font-medium text-yellow-400">{formattedDate}</span>
                          </div>

                          <div className={`rounded-lg p-4 ml-10 flex-grow ${eventTypeColors[event.event_type as keyof typeof eventTypeColors] || 'bg-white/10'}`}>
                            <div className="flex justify-between items-start">
                              <div>
                                <span className="inline-block px-2 py-1 rounded text-xs font-medium bg-white/20 text-white mb-2">
                                  {/* Try to use the translation first, then fallback to the local object, then to the raw event type */}
                                  {tEventTypes(event.event_type) !== event.event_type
                                    ? tEventTypes(event.event_type)
                                    : eventTypes[event.event_type as keyof typeof eventTypes] || event.event_type}
                                </span>
                                {event.description_key && (
                                  <h3 className="font-semibold text-white">{event.description_key}</h3>
                                )}
                              </div>
                              <span className="text-xs bg-white/20 rounded-full px-2 py-0.5 text-white">
                                {childName}
                              </span>
                            </div>

                            {event.achievement_details_key && (
                              <p className="text-sm text-gray-300 mt-1">
                                <span className="text-yellow-400 font-medium">{t('achievementLabel')}: </span>
                                {event.achievement_details_key}
                              </p>
                            )}

                            {/* Display related skills */}
                            {event.related_skill_ids && event.related_skill_ids.length > 0 && (
                              <div className="mt-3">
                                <div className="text-xs text-gray-400 mb-1">
                                  <span>{t('relatedSkillsLabel')}</span>
                                </div>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {event.related_skill_ids.map(skillId => {
                                    const skill = skills.find(s => s.id === skillId);
                                    return (
                                      <span key={skillId} className="inline-block bg-white/10 text-yellow-300 px-2 py-1 rounded-full text-xs">
                                        {skill ? `${skill.name_key}${skill.skill_area_id ? ` (${skill.skill_area_id})` : ''}` : `Skill ID: ${skillId}`}
                                      </span>
                                    );
                                  })}
                                </div>
                              </div>
                            )}

                            {event.skill_progress_at_event !== null && (
                              <div className="mt-3">
                                <div className="flex justify-between text-xs text-gray-400 mb-1">
                                  <span>{t('skillProgressLabel')}</span>
                                  <span>{event.skill_progress_at_event}%</span>
                                </div>
                                <div className="overflow-hidden h-1.5 text-xs flex rounded-full bg-white/10">
                                  <div
                                    style={{ width: `${event.skill_progress_at_event}%` }}
                                    className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-yellow-300 to-yellow-500"
                                  ></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      {filterType ? (
                        // No events match the current filter
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-gray-400">{t('noEventsMatchMessage')}</p>
                          <button
                            onClick={() => {
                              setFilterType(null);
                            }}
                            className="mt-4 text-yellow-400 hover:text-yellow-300 transition-colors"
                          >
                            {t('resetFiltersButton')}
                          </button>
                        </>
                      ) : (
                        // No events at all for this child
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <p className="text-gray-400">{t('noEventsYetMessage')}</p>
                          <p className="text-gray-500 text-sm mt-2">{t('eventsCreatedInAssistantsMessage')}</p>


                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </LearningDocumentationLayout>
    </DashboardLayout>
  );
};

export default TimelinePage;