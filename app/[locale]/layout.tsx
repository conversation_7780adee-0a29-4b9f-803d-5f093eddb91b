import type { Metada<PERSON> } from "next";
import { Inter, <PERSON>o_Mono, Noto_Sans_Arabic, Noto_Sans_Hebrew } from "next/font/google";
import "../globals.css";
import { isRtlLang, Locale } from "@/lib/i18n"; // Import Locale
import { NextIntlClientProvider } from 'next-intl'; // Import Provider
import { loadTranslationMessages } from '@/utils/i18n'; // Import our optimized translation loader
import BetaTag from '@/app/components/BetaTag';
import ErrorBoundary from '@/app/components/ErrorBoundary';

// Latin font
const inter = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
});

// Monospace font
const robotoMono = Roboto_Mono({
  variable: "--font-mono",
  subsets: ["latin"],
});

// Arabic font
const notoSansArabic = Noto_Sans_Arabic({
  variable: "--font-arabic",
  subsets: ["arabic"],
  weight: ["400", "700"],
  display: "swap",
});

// Hebrew font
const notoSansHebrew = Noto_Sans_Hebrew({
  variable: "--font-hebrew",
  subsets: ["hebrew"],
  weight: ["400", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Parents App",
  description: "Learning Studios Parents Application",
};

export default async function LocaleLayout({ // Make the function async
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { locale: Locale }; // Use Locale type for params
}>) {
  // Get the current locale from the URL params
  const locale = params.locale;

  // Check if the language is RTL
  const isRtl = isRtlLang(locale);

  // Load messages using our optimized utility
  const { messages } = await loadTranslationMessages(locale);

  // Determine font class based on locale
  const getFontClass = (locale: string, isRtl: boolean) => {
    if (locale === "ar") {
      return "font-arabic";
    } else if (locale === "he") {
      return "font-hebrew";
    }
    return "font-sans";
  };

  const fontClass = getFontClass(locale, isRtl);

  return (
    <div className={fontClass} dir={isRtl ? "rtl" : "ltr"}>
      <ErrorBoundary>
        <NextIntlClientProvider locale={locale} messages={messages}>
          {children}
          {/* BetaTag is a client component that will be rendered on the client side */}
          {typeof window !== 'undefined' ? <BetaTag /> : null}
        </NextIntlClientProvider>
      </ErrorBoundary>
    </div>
  );
}
