@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-arabic: 'Noto Sans Arabic', 'Inter', Arial, Helvetica, sans-serif;
  --font-hebrew: 'Noto Sans Hebrew', 'Inter', Arial, Helvetica, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-sans), 'Inter', Arial, Helvetica, sans-serif;
  font-size: 1.05rem; /* Slightly larger base font size */
  line-height: 1.6; /* Adjust line height for readability */
}

/* RTL language support */
html[dir="rtl"] body {
  text-align: right;
}

/* Font classes for different languages */
.font-sans {
  font-family: var(--font-sans), 'Inter', Arial, Helvetica, sans-serif;
}

.font-arabic {
  font-family: var(--font-arabic);
}

.font-hebrew {
  font-family: var(--font-hebrew);
}

/* RTL specific adjustments for UI elements */
html[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

html[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

html[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

code, pre {
  font-family: var(--font-mono), monospace;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeInScale {
  animation: fadeInScale 0.4s ease-out forwards;
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInSlide {
  animation: fadeInSlide 0.5s ease-out forwards;
}

/* Glass morphism utility */
.glassmorphism {
  @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-xl;
}

/* Make scrollbar prettier */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
