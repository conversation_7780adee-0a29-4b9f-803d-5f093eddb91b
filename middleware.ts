import { NextRequest, NextResponse } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './lib/i18n';

// Define protected routes (add any other routes that need authentication)
const protectedRoutes = ['/dashboard', '/purchases', '/learning-documentation', '/learning-studios'];
const loginRoute = '/login';

// Create the i18n middleware instance
const handleI18nRouting = createIntlMiddleware({
  locales: locales,
  defaultLocale: defaultLocale,
  localePrefix: 'always' // Keep locale prefix always
});

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = request.cookies.get('userToken')?.value;

  // --- Authentication Logic ---

  // Extract locale from pathname (assuming '/{locale}/...' structure)
  const pathnameParts = pathname.split('/');
  const locale = locales.find(loc => loc === pathnameParts[1]);
  const pathWithoutLocale = locale ? '/' + pathnameParts.slice(2).join('/') : pathname;

  // 1. Redirect unauthenticated users trying to access protected routes
  if (!token && protectedRoutes.some(route => pathWithoutLocale.startsWith(route))) {
    const localePrefix = locale ? `/${locale}` : `/${defaultLocale}`;
    const loginUrl = new URL(`${localePrefix}${loginRoute}`, request.url);
    return NextResponse.redirect(loginUrl);
  }

  // 2. Redirect authenticated users trying to access the login page
  if (token && pathWithoutLocale.startsWith(loginRoute)) {
    const localePrefix = locale ? `/${locale}` : `/${defaultLocale}`;
    const dashboardUrl = new URL(`${localePrefix}/dashboard`, request.url); // Redirect to dashboard
    return NextResponse.redirect(dashboardUrl);
  }

  // 3. Redirect unauthenticated users from the root path to the login page
  if (!token && pathWithoutLocale === '/') {
    const localePrefix = locale ? `/${locale}` : `/${defaultLocale}`;
    const loginUrl = new URL(`${localePrefix}${loginRoute}`, request.url);
    return NextResponse.redirect(loginUrl);
  }

  // --- i18n Logic ---
  // If no authentication or root redirect happened, let next-intl handle locale routing
  return handleI18nRouting(request);
}

export const config = {
  // Matcher adapted to include locale prefixes potentially
  matcher: [
    // Skip internal Next.js paths, API routes, and static files
    '/((?!api|_next/static|_next/image|favicon.ico|.*\..*).*)',
  ]
};
