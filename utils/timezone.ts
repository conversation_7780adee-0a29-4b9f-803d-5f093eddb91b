/**
 * Utility functions for timezone handling
 */

/**
 * Gets the browser's timezone as an IANA timezone string
 * @returns {string} IANA timezone string (e.g., 'Europe/Berlin')
 */
export function getBrowserTimezone(): string {
  if (typeof window !== 'undefined') {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
  // Default to UTC if not in browser environment
  return 'UTC';
}

/**
 * Formats a date for API requests in ISO format with timezone information
 * @param {Date} date - The date to format
 * @returns {string} ISO formatted date string
 */
export function formatDateForApi(date: Date): string {
  return date.toISOString();
}
