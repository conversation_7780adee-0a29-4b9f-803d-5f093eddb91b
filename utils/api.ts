/**
 * Utility functions for API calls
 */

/**
 * Get the API URL based on the environment
 * Uses the Next.js API route proxy in the browser
 * Uses the direct API URL in server-side rendering
 */
export function getApiUrl(): string {
  return typeof window !== 'undefined' ? '/api' : process.env.NEXT_PUBLIC_API_URL || '';
}

/**
 * Get the base URL with trailing slash
 */
export function getBaseUrl(): string {
  const url = getApiUrl();
  return url.endsWith('/') ? url : `${url}/`;
}
