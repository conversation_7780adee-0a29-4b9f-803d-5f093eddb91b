/**
 * API Configuration Utility
 *
 * This file provides a consistent way to access the API URL across the application.
 */

// Get the base API URL from environment variables with a fallback
const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://server-beta-fkw4.onrender.com';

// Remove any trailing slash to ensure consistency
const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

console.log('API Base URL:', normalizedBaseUrl);

/**
 * Returns the base API URL without a trailing slash
 */
export const getApiBaseUrl = (): string => {
  return normalizedBaseUrl;
};

/**
 * Constructs a full API endpoint URL
 * @param path - The API endpoint path (with or without leading slash), optional
 * @returns The full API URL
 */
export const getApiUrl = (path?: string): string => {
  // If no path is provided, return the base URL
  if (!path) {
    return normalizedBaseUrl;
  }

  // Ensure path starts with a slash
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${normalizedBaseUrl}${normalizedPath}`;
};

// Export a default API URL for backward compatibility
export const API_URL = normalizedBaseUrl;

export default {
  getApiBaseUrl,
  getApiUrl,
  API_URL
};
