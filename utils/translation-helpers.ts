import { useTranslations } from 'next-intl';
import { Locale } from '@/lib/i18n';

/**
 * Helper function to get translations for a component
 * This is a wrapper around useTranslations that provides type safety and fallbacks
 *
 * @param namespace The translation namespace to use
 * @returns A function to get translations
 */
export function useComponentTranslations(namespace: string) {
  try {
    // Use the next-intl hook
    const t = useTranslations(namespace);

    // Return a function that wraps the t function with error handling
    return (key: string, params?: Record<string, any>) => {
      try {
        return t(key, params);
      } catch (error) {
        console.warn(`Translation key not found: ${namespace}.${key}`);
        return `[${namespace}.${key}]`; // Return a placeholder that makes it obvious there's a missing translation
      }
    };
  } catch (error) {
    console.warn(`Failed to initialize translations for namespace: ${namespace}`, error);
    // Return a fallback function that just returns the key
    return (key: string) => `[${namespace}.${key}]`;
  }
}

/**
 * Helper function to format a translation key with parameters
 * This is useful for translations that use numbered placeholders like {0}, {1}, etc.
 *
 * @param text The text containing placeholders like {0}, {1}, etc.
 * @param params The parameters to replace the placeholders with
 * @returns The formatted text
 */
export function formatTranslation(text: string, ...params: any[]): string {
  return text.replace(/{(\d+)}/g, (match, index) => {
    const paramIndex = parseInt(index, 10);
    return paramIndex < params.length ? String(params[paramIndex]) : match;
  });
}

/**
 * Helper function to get a translation for a specific locale
 * This is useful for server components or when you need a translation in a specific locale
 *
 * @param locale The locale to get the translation for
 * @param namespace The translation namespace
 * @param key The translation key
 * @param params Optional parameters for the translation
 * @returns The translated string or a fallback
 */
export async function getTranslationForLocale(
  locale: Locale,
  namespace: string,
  key: string,
  params?: Record<string, any>
): Promise<string> {
  try {
    // Import the translation file for the specified locale
    const messages = (await import(`../translations/messages/${locale}.json`)).default;

    // Navigate to the specified namespace and key
    const parts = `${namespace}.${key}`.split('.');
    let value: any = messages;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        throw new Error(`Translation key not found: ${namespace}.${key}`);
      }
    }

    if (typeof value !== 'string') {
      throw new Error(`Translation value is not a string: ${namespace}.${key}`);
    }

    // Replace parameters if provided
    if (params) {
      return value.replace(/{([^}]+)}/g, (match, key) => {
        return params[key] !== undefined ? String(params[key]) : match;
      });
    }

    return value;
  } catch (error) {
    console.warn(`Failed to get translation for ${locale}.${namespace}.${key}:`, error);
    return `[${namespace}.${key}]`; // Return a placeholder
  }
}
