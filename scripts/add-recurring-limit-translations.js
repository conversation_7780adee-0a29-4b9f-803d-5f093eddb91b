/**
 * <PERSON><PERSON><PERSON> to add new translations for recurring booking limit factors to all language files
 */

const fs = require('fs');
const path = require('path');

// Define the directory where the translation files are located
const translationsDir = path.join(__dirname, '../translations/messages');

// Define the new translations to add
const newTranslations = {
  'en': {
    'limitedByBudget': 'Limited by budget',
    'limitedByCapacity': 'Limited by available space'
  },
  'de': {
    'limitedByBudget': 'Begrenzt durch Budget',
    'limitedByCapacity': 'Begrenzt durch verfügbaren Platz'
  },
  'fr': {
    'limitedByBudget': 'Limité par le budget',
    'limitedByCapacity': 'Limité par l\'espace disponible'
  },
  'es': {
    'limitedByBudget': 'Limitado por presupuesto',
    'limitedByCapacity': 'Limitado por espacio disponible'
  },
  'it': {
    'limitedByBudget': 'Limitato dal budget',
    'limitedByCapacity': 'Limitato dallo spazio disponibile'
  },
  'nl': {
    'limitedByBudget': 'Beperkt door budget',
    'limitedByCapacity': 'Beperkt door beschikbare ruimte'
  },
  'pt': {
    'limitedByBudget': 'Limitado pelo orçamento',
    'limitedByCapacity': 'Limitado pelo espaço disponível'
  },
  'pl': {
    'limitedByBudget': 'Ograniczone budżetem',
    'limitedByCapacity': 'Ograniczone dostępną przestrzenią'
  },
  'ja': {
    'limitedByBudget': '予算による制限',
    'limitedByCapacity': '利用可能なスペースによる制限'
  },
  'zh': {
    'limitedByBudget': '受预算限制',
    'limitedByCapacity': '受可用空间限制'
  },
  'ko': {
    'limitedByBudget': '예산에 의한 제한',
    'limitedByCapacity': '가용 공간에 의한 제한'
  },
  'ar': {
    'limitedByBudget': 'محدود بالميزانية',
    'limitedByCapacity': 'محدود بالمساحة المتاحة'
  },
  'he': {
    'limitedByBudget': 'מוגבל על ידי תקציב',
    'limitedByCapacity': 'מוגבל על ידי מקום זמין'
  },
  'hi': {
    'limitedByBudget': 'बजट द्वारा सीमित',
    'limitedByCapacity': 'उपलब्ध स्थान द्वारा सीमित'
  },
  'bn': {
    'limitedByBudget': 'বাজেট দ্বারা সীমিত',
    'limitedByCapacity': 'উপলব্ধ স্থান দ্বারা সীমিত'
  },
  'tr': {
    'limitedByBudget': 'Bütçe ile sınırlı',
    'limitedByCapacity': 'Mevcut alan ile sınırlı'
  },
  'cs': {
    'limitedByBudget': 'Omezeno rozpočtem',
    'limitedByCapacity': 'Omezeno dostupným prostorem'
  },
  'da': {
    'limitedByBudget': 'Begrænset af budget',
    'limitedByCapacity': 'Begrænset af tilgængelig plads'
  },
  'el': {
    'limitedByBudget': 'Περιορισμένο από τον προϋπολογισμό',
    'limitedByCapacity': 'Περιορισμένο από τον διαθέσιμο χώρο'
  },
  'uk': {
    'limitedByBudget': 'Обмежено бюджетом',
    'limitedByCapacity': 'Обмежено доступним простором'
  }
};

// Function to add translations to a file
const addTranslationsToFile = (filePath, translations) => {
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Parse the JSON
    const json = JSON.parse(fileContent);
    
    // Check if the file has the bookingConfirmation section
    if (json.dashboardPage && json.dashboardPage.bookingConfirmation) {
      // Add the new translations
      Object.keys(translations).forEach(key => {
        json.dashboardPage.bookingConfirmation[key] = translations[key];
      });
      
      // Write the updated JSON back to the file
      fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
      console.log(`Updated translations in ${filePath}`);
    } else {
      console.warn(`Skipping ${filePath}: bookingConfirmation section not found`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
};

// Process all language files
fs.readdir(translationsDir, (err, files) => {
  if (err) {
    console.error('Error reading translations directory:', err);
    return;
  }
  
  // Filter for JSON files
  const jsonFiles = files.filter(file => file.endsWith('.json'));
  
  // Process each file
  jsonFiles.forEach(file => {
    const langCode = file.replace('.json', '');
    const filePath = path.join(translationsDir, file);
    
    // Get translations for this language, or use English as fallback
    const translations = newTranslations[langCode] || newTranslations['en'];
    
    addTranslationsToFile(filePath, translations);
  });
});
