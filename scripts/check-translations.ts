/**
 * Translation Completeness Checker
 * 
 * This script checks all translation files for completeness against the default locale.
 * It identifies missing keys and provides a report on translation completeness.
 * 
 * Usage:
 * npx ts-node scripts/check-translations.ts
 */

import { locales, defaultLocale, Locale } from '../lib/i18n';
import { checkTranslationCompleteness } from '../utils/i18n';
import fs from 'fs';
import path from 'path';

async function main() {
  console.log('Translation Completeness Check');
  console.log('==============================');
  console.log(`Default locale: ${defaultLocale}`);
  console.log(`Checking locales: ${locales.filter(l => l !== defaultLocale).join(', ')}`);
  console.log('');

  let hasErrors = false;
  const summaryResults: Record<string, { complete: boolean; missingCount: number }> = {};

  // Check each non-default locale
  for (const locale of locales) {
    if (locale === defaultLocale) continue;

    console.log(`Checking ${locale}...`);
    const result = await checkTranslationCompleteness(locale as Locale);
    
    summaryResults[locale] = {
      complete: result.complete,
      missingCount: result.missingKeys.length
    };

    if (!result.complete) {
      hasErrors = true;
      console.log(`  ❌ Missing ${result.missingKeys.length} keys`);
      
      // Group missing keys by top-level section for better readability
      const groupedMissing: Record<string, string[]> = {};
      result.missingKeys.forEach(key => {
        const topSection = key.split('.')[0];
        if (!groupedMissing[topSection]) {
          groupedMissing[topSection] = [];
        }
        groupedMissing[topSection].push(key);
      });

      // Print grouped missing keys
      Object.entries(groupedMissing).forEach(([section, keys]) => {
        console.log(`    Section '${section}': ${keys.length} missing keys`);
        keys.forEach(key => console.log(`      - ${key}`));
      });
    } else {
      console.log(`  ✅ Complete`);
    }
    console.log('');
  }

  // Print summary table
  console.log('Summary');
  console.log('=======');
  console.log('Locale | Status    | Missing Keys');
  console.log('-------|-----------|-------------');
  
  Object.entries(summaryResults).forEach(([locale, result]) => {
    const status = result.complete ? '✅ Complete' : '❌ Incomplete';
    const missingCount = result.complete ? '0' : result.missingCount.toString();
    console.log(`${locale.padEnd(7)}| ${status.padEnd(11)}| ${missingCount}`);
  });

  // Generate a JSON report
  const report = {
    timestamp: new Date().toISOString(),
    defaultLocale,
    results: summaryResults
  };

  const reportsDir = path.join(__dirname, '../reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  fs.writeFileSync(
    path.join(reportsDir, 'translation-check.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('\nReport saved to reports/translation-check.json');

  // Exit with appropriate code
  process.exit(hasErrors ? 1 : 0);
}

main().catch(error => {
  console.error('Error running translation check:', error);
  process.exit(1);
});
