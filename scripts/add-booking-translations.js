/**
 * <PERSON><PERSON>t to add new booking translations to all language files
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

// Get all translation files
const translationFiles = fs.readdirSync(TRANSLATIONS_DIR)
  .filter(file => file.endsWith('.json'));

// Define the new translations to add
const newTranslations = {
  'en': {
    'recurringBookingCapacityExceeded': 'One or more of the recurring bookings would exceed the room capacity. Please reduce the number of occurrences or choose a different time slot.',
    'biweeklyRecurrenceInfo': 'The booking will repeat every two weeks on the same day for the specified number of occurrences.'
  },
  'de': {
    'recurringBookingCapacityExceeded': 'Eine oder mehrere der wiederkehrenden Buchungen würden die Raumkapazität überschreiten. Bitte reduzieren Sie die Anzahl der Wiederholungen oder wählen Sie einen anderen Zeitraum.',
    'biweeklyRecurrenceInfo': 'Die Buchung wird alle zwei Wochen am selben Tag für die angegebene Anzahl von Wiederholungen wiederholt.'
  },
  'ar': {
    'recurringBookingCapacityExceeded': 'واحد أو أكثر من الحجوزات المتكررة سيتجاوز سعة الغرفة. يرجى تقليل عدد المرات أو اختيار وقت آخر.',
    'biweeklyRecurrenceInfo': 'سيتكرر الحجز كل أسبوعين في نفس اليوم للعدد المحدد من المرات.'
  },
  'bn': {
    'recurringBookingCapacityExceeded': 'একটি বা একাধিক পুনরাবৃত্ত বুকিং রুমের ক্ষমতা অতিক্রম করবে। অনুগ্রহ করে পুনরাবৃত্তির সংখ্যা কমান বা অন্য সময় নির্বাচন করুন।',
    'biweeklyRecurrenceInfo': 'বুকিং নির্দিষ্ট সংখ্যক ঘটনার জন্য প্রতি দুই সপ্তাহে একই দিনে পুনরাবৃত্তি হবে।'
  },
  'cs': {
    'recurringBookingCapacityExceeded': 'Jedna nebo více opakovaných rezervací by překročila kapacitu místnosti. Snižte počet opakování nebo vyberte jiný časový úsek.',
    'biweeklyRecurrenceInfo': 'Rezervace se bude opakovat každé dva týdny ve stejný den po určený počet opakování.'
  },
  'da': {
    'recurringBookingCapacityExceeded': 'En eller flere af de tilbagevendende bookinger ville overstige lokalets kapacitet. Reducer venligst antallet af forekomster eller vælg et andet tidspunkt.',
    'biweeklyRecurrenceInfo': 'Bookingen gentages hver anden uge på samme dag i det angivne antal forekomster.'
  },
  'el': {
    'recurringBookingCapacityExceeded': 'Μία ή περισσότερες από τις επαναλαμβανόμενες κρατήσεις θα υπερβούν τη χωρητικότητα του δωματίου. Μειώστε τον αριθμό των επαναλήψεων ή επιλέξτε διαφορετική χρονική θυρίδα.',
    'biweeklyRecurrenceInfo': 'Η κράτηση θα επαναλαμβάνεται κάθε δύο εβδομάδες την ίδια ημέρα για τον καθορισμένο αριθμό επαναλήψεων.'
  },
  'es': {
    'recurringBookingCapacityExceeded': 'Una o más de las reservas recurrentes excederían la capacidad de la sala. Por favor, reduzca el número de ocurrencias o elija otro horario.',
    'biweeklyRecurrenceInfo': 'La reserva se repetirá cada dos semanas el mismo día durante el número especificado de ocurrencias.'
  },
  'fr': {
    'recurringBookingCapacityExceeded': 'Une ou plusieurs des réservations récurrentes dépasseraient la capacité de la salle. Veuillez réduire le nombre d\'occurrences ou choisir un autre créneau horaire.',
    'biweeklyRecurrenceInfo': 'La réservation se répétera toutes les deux semaines le même jour pour le nombre spécifié d\'occurrences.'
  },
  'he': {
    'recurringBookingCapacityExceeded': 'אחת או יותר מההזמנות החוזרות תחרוג מקיבולת החדר. אנא הפחת את מספר המופעים או בחר משבצת זמן אחרת.',
    'biweeklyRecurrenceInfo': 'ההזמנה תחזור על עצמה כל שבועיים באותו יום למספר המופעים שצוין.'
  },
  'hi': {
    'recurringBookingCapacityExceeded': 'आवर्ती बुकिंग में से एक या अधिक कमरे की क्षमता से अधिक होगी। कृपया घटनाओं की संख्या कम करें या कोई अन्य समय स्लॉट चुनें।',
    'biweeklyRecurrenceInfo': 'बुकिंग निर्दिष्ट संख्या में घटनाओं के लिए हर दो सप्ताह में एक ही दिन दोहराई जाएगी।'
  },
  'it': {
    'recurringBookingCapacityExceeded': 'Una o più delle prenotazioni ricorrenti supererebbe la capacità della stanza. Si prega di ridurre il numero di occorrenze o scegliere un altro orario.',
    'biweeklyRecurrenceInfo': 'La prenotazione si ripeterà ogni due settimane nello stesso giorno per il numero specificato di occorrenze.'
  },
  'ja': {
    'recurringBookingCapacityExceeded': '繰り返し予約の1つ以上が部屋の収容人数を超えてしまいます。発生回数を減らすか、別の時間枠を選択してください。',
    'biweeklyRecurrenceInfo': '予約は指定された回数だけ、2週間ごとに同じ曜日に繰り返されます。'
  },
  'ko': {
    'recurringBookingCapacityExceeded': '반복 예약 중 하나 이상이 방 수용 인원을 초과합니다. 반복 횟수를 줄이거나 다른 시간대를 선택해 주세요.',
    'biweeklyRecurrenceInfo': '예약은 지정된 횟수만큼 2주마다 같은 요일에 반복됩니다.'
  },
  'nl': {
    'recurringBookingCapacityExceeded': 'Een of meer van de terugkerende boekingen zou de capaciteit van de ruimte overschrijden. Verminder het aantal herhalingen of kies een ander tijdslot.',
    'biweeklyRecurrenceInfo': 'De boeking wordt elke twee weken op dezelfde dag herhaald voor het opgegeven aantal keren.'
  },
  'pl': {
    'recurringBookingCapacityExceeded': 'Jedna lub więcej rezerwacji cyklicznych przekroczyłaby pojemność pokoju. Proszę zmniejszyć liczbę wystąpień lub wybrać inny przedział czasowy.',
    'biweeklyRecurrenceInfo': 'Rezerwacja będzie powtarzana co dwa tygodnie w ten sam dzień przez określoną liczbę wystąpień.'
  },
  'pt': {
    'recurringBookingCapacityExceeded': 'Uma ou mais das reservas recorrentes excederia a capacidade da sala. Por favor, reduza o número de ocorrências ou escolha outro horário.',
    'biweeklyRecurrenceInfo': 'A reserva será repetida a cada duas semanas no mesmo dia pelo número especificado de ocorrências.'
  },
  'tr': {
    'recurringBookingCapacityExceeded': 'Tekrarlanan rezervasyonlardan bir veya daha fazlası oda kapasitesini aşacaktır. Lütfen tekrar sayısını azaltın veya başka bir zaman dilimi seçin.',
    'biweeklyRecurrenceInfo': 'Rezervasyon, belirtilen sayıda tekrar için iki haftada bir aynı günde tekrarlanacaktır.'
  },
  'uk': {
    'recurringBookingCapacityExceeded': 'Одне або кілька повторюваних бронювань перевищать місткість кімнати. Будь ласка, зменшіть кількість повторень або виберіть інший часовий проміжок.',
    'biweeklyRecurrenceInfo': 'Бронювання повторюватиметься кожні два тижні в той самий день протягом вказаної кількості повторень.'
  },
  'zh': {
    'recurringBookingCapacityExceeded': '一个或多个重复预订将超过房间容量。请减少重复次数或选择其他时间段。',
    'biweeklyRecurrenceInfo': '预订将每两周在同一天重复指定的次数。'
  }
};

// Process each translation file
translationFiles.forEach(file => {
  const locale = file.replace('.json', '');
  const filePath = path.join(TRANSLATIONS_DIR, file);
  
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(fileContent);
    
    // Check if the locale has translations defined
    if (!newTranslations[locale]) {
      console.log(`No new translations defined for ${locale}, using English as fallback`);
      
      // If no translations are defined for this locale, use English as fallback
      if (translations.dashboardPage && translations.dashboardPage.bookingConfirmation) {
        translations.dashboardPage.bookingConfirmation.recurringBookingCapacityExceeded = 
          newTranslations.en.recurringBookingCapacityExceeded;
        translations.dashboardPage.bookingConfirmation.biweeklyRecurrenceInfo = 
          newTranslations.en.biweeklyRecurrenceInfo;
      }
    } else {
      // Add the new translations
      if (translations.dashboardPage && translations.dashboardPage.bookingConfirmation) {
        translations.dashboardPage.bookingConfirmation.recurringBookingCapacityExceeded = 
          newTranslations[locale].recurringBookingCapacityExceeded;
        translations.dashboardPage.bookingConfirmation.biweeklyRecurrenceInfo = 
          newTranslations[locale].biweeklyRecurrenceInfo;
      }
    }
    
    // Write the updated translations back to the file
    fs.writeFileSync(filePath, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`Updated translations for ${locale}`);
  } catch (error) {
    console.error(`Error updating translations for ${locale}:`, error);
  }
});

console.log('All translation files updated successfully!');
