/**
 * Translation Optimization Script
 * 
 * This script optimizes translation files by:
 * 1. Removing duplicate keys
 * 2. Sorting keys alphabetically
 * 3. Validating translation completeness
 * 4. Generating a report of missing translations
 * 
 * Usage:
 * npx ts-node scripts/optimize-translations.ts
 */

import { locales, defaultLocale, Locale } from '../lib/i18n';
import fs from 'fs';
import path from 'path';

const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

interface TranslationStats {
  totalKeys: number;
  missingKeys: string[];
  duplicateKeys: string[];
  optimized: boolean;
}

async function main() {
  console.log('Translation Optimization');
  console.log('=======================');
  console.log(`Default locale: ${defaultLocale}`);
  console.log(`Processing locales: ${locales.join(', ')}`);
  console.log('');

  // Load default locale as reference
  const defaultTranslations = loadTranslationFile(defaultLocale);
  const defaultKeys = getAllKeys(defaultTranslations);
  console.log(`Default locale has ${defaultKeys.length} keys`);

  const stats: Record<string, TranslationStats> = {};

  // Process each locale
  for (const locale of locales) {
    console.log(`\nProcessing ${locale}...`);
    
    // Skip default locale for missing keys check
    const isDefaultLocale = locale === defaultLocale;
    
    // Load and parse the translation file
    const translationPath = path.join(TRANSLATIONS_DIR, `${locale}.json`);
    const translations = loadTranslationFile(locale);
    
    // Get all keys from this translation file
    const keys = getAllKeys(translations);
    console.log(`  Found ${keys.length} keys`);
    
    // Find duplicate keys
    const duplicateKeys = findDuplicateKeys(translations);
    if (duplicateKeys.length > 0) {
      console.log(`  ⚠️ Found ${duplicateKeys.length} duplicate keys`);
      duplicateKeys.forEach(key => console.log(`    - ${key}`));
    }
    
    // Find missing keys (compared to default locale)
    const missingKeys = isDefaultLocale ? [] : findMissingKeys(defaultKeys, keys);
    if (missingKeys.length > 0) {
      console.log(`  ❌ Missing ${missingKeys.length} keys`);
      // Group missing keys by top-level section for better readability
      const groupedMissing: Record<string, string[]> = {};
      missingKeys.forEach(key => {
        const topSection = key.split('.')[0];
        if (!groupedMissing[topSection]) {
          groupedMissing[topSection] = [];
        }
        groupedMissing[topSection].push(key);
      });

      // Print grouped missing keys
      Object.entries(groupedMissing).forEach(([section, keys]) => {
        console.log(`    Section '${section}': ${keys.length} missing keys`);
        keys.slice(0, 5).forEach(key => console.log(`      - ${key}`));
        if (keys.length > 5) {
          console.log(`      ... and ${keys.length - 5} more`);
        }
      });
    }
    
    // Optimize the translation file
    const optimized = optimizeTranslationFile(translations);
    
    // Save the optimized file
    const backupPath = path.join(TRANSLATIONS_DIR, `${locale}.json.bak`);
    fs.copyFileSync(translationPath, backupPath);
    fs.writeFileSync(translationPath, JSON.stringify(optimized, null, 2));
    console.log(`  ✅ Optimized and saved to ${locale}.json (backup at ${locale}.json.bak)`);
    
    // Save stats
    stats[locale] = {
      totalKeys: keys.length,
      missingKeys,
      duplicateKeys,
      optimized: true
    };
  }

  // Generate a report
  const report = {
    timestamp: new Date().toISOString(),
    defaultLocale,
    stats
  };

  const reportsDir = path.join(__dirname, '../reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  fs.writeFileSync(
    path.join(reportsDir, 'translation-optimization.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('\nReport saved to reports/translation-optimization.json');
  
  // Print summary
  console.log('\nSummary');
  console.log('=======');
  console.log('Locale | Total Keys | Missing Keys | Duplicate Keys');
  console.log('-------|------------|-------------|---------------');
  
  Object.entries(stats).forEach(([locale, stat]) => {
    console.log(
      `${locale.padEnd(7)}| ${String(stat.totalKeys).padEnd(12)}| ${String(stat.missingKeys.length).padEnd(13)}| ${stat.duplicateKeys.length}`
    );
  });
}

/**
 * Load a translation file
 */
function loadTranslationFile(locale: string): Record<string, any> {
  const filePath = path.join(TRANSLATIONS_DIR, `${locale}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Error loading translation file for ${locale}:`, error);
    return {};
  }
}

/**
 * Get all keys from a translation object (flattened with dot notation)
 */
function getAllKeys(obj: Record<string, any>, prefix = ''): string[] {
  let keys: string[] = [];
  
  for (const key in obj) {
    const newKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = [...keys, ...getAllKeys(obj[key], newKey)];
    } else {
      keys.push(newKey);
    }
  }
  
  return keys;
}

/**
 * Find duplicate keys in a translation object
 */
function findDuplicateKeys(obj: Record<string, any>): string[] {
  const keys = getAllKeys(obj);
  const uniqueKeys = new Set(keys);
  
  return keys.filter(key => {
    if (uniqueKeys.has(key)) {
      uniqueKeys.delete(key);
      return false;
    }
    return true;
  });
}

/**
 * Find missing keys compared to a reference set of keys
 */
function findMissingKeys(referenceKeys: string[], keys: string[]): string[] {
  const keySet = new Set(keys);
  return referenceKeys.filter(key => !keySet.has(key));
}

/**
 * Optimize a translation object by sorting keys and removing duplicates
 */
function optimizeTranslationFile(obj: Record<string, any>): Record<string, any> {
  // Create a new object with sorted keys
  const result: Record<string, any> = {};
  
  // Get all keys and sort them
  const keys = Object.keys(obj).sort();
  
  // Add each key to the result object
  for (const key of keys) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      result[key] = optimizeTranslationFile(obj[key]);
    } else {
      result[key] = obj[key];
    }
  }
  
  return result;
}

main().catch(error => {
  console.error('Error running translation optimization:', error);
  process.exit(1);
});
