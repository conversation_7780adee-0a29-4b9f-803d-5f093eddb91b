/**
 * <PERSON><PERSON><PERSON> to update event types in all translation files
 * 
 * This script updates the event types in all translation files to match
 * the event types in the assistants app.
 */

const fs = require('fs');
const path = require('path');

// List of all supported languages
const languages = [
  'ar', 'bn', 'cs', 'da', 'de', 'el', 'en', 'es', 'fr', 'he', 'hi', 'it', 'ja', 'ko', 'nl', 'pl', 'pt', 'tr', 'uk', 'zh'
];

// Event type translations for all languages
const eventTypeTranslations = {
  ar: {
    achievement: 'إنجاز',
    milestone: 'معلم رئيسي',
    assessment: 'تقييم',
    test: 'اختبار',
    exam: 'امتحان',
    report: 'تقرير',
    presentation: 'عرض تقديمي',
    project: 'مشروع',
    workshop: 'ورشة عمل',
    other: 'أخرى'
  },
  bn: {
    achievement: 'অর্জন',
    milestone: 'মাইলস্টোন',
    assessment: 'মূল্যায়ন',
    test: 'পরীক্ষা',
    exam: 'পরীক্ষা',
    report: 'প্রতিবেদন',
    presentation: 'উপস্থাপনা',
    project: 'প্রকল্প',
    workshop: 'কর্মশালা',
    other: 'অন্যান্য'
  },
  cs: {
    achievement: 'Úspěch',
    milestone: 'Milník',
    assessment: 'Hodnocení',
    test: 'Test',
    exam: 'Zkouška',
    report: 'Zpráva',
    presentation: 'Prezentace',
    project: 'Projekt',
    workshop: 'Workshop',
    other: 'Jiné'
  },
  da: {
    achievement: 'Præstation',
    milestone: 'Milepæl',
    assessment: 'Vurdering',
    test: 'Test',
    exam: 'Eksamen',
    report: 'Rapport',
    presentation: 'Præsentation',
    project: 'Projekt',
    workshop: 'Workshop',
    other: 'Andet'
  },
  de: {
    achievement: 'Erfolg',
    milestone: 'Meilenstein',
    assessment: 'Bewertung',
    test: 'Test',
    exam: 'Prüfung',
    report: 'Bericht',
    presentation: 'Präsentation',
    project: 'Projekt',
    workshop: 'Workshop',
    other: 'Sonstiges'
  },
  el: {
    achievement: 'Επίτευγμα',
    milestone: 'Ορόσημο',
    assessment: 'Αξιολόγηση',
    test: 'Τεστ',
    exam: 'Εξέταση',
    report: 'Αναφορά',
    presentation: 'Παρουσίαση',
    project: 'Έργο',
    workshop: 'Εργαστήριο',
    other: 'Άλλο'
  },
  en: {
    achievement: 'Achievement',
    milestone: 'Milestone',
    assessment: 'Assessment',
    test: 'Test',
    exam: 'Exam',
    report: 'Report',
    presentation: 'Presentation',
    project: 'Project',
    workshop: 'Workshop',
    other: 'Other'
  },
  es: {
    achievement: 'Logro',
    milestone: 'Hito',
    assessment: 'Evaluación',
    test: 'Prueba',
    exam: 'Examen',
    report: 'Informe',
    presentation: 'Presentación',
    project: 'Proyecto',
    workshop: 'Taller',
    other: 'Otro'
  },
  fr: {
    achievement: 'Réussite',
    milestone: 'Étape importante',
    assessment: 'Évaluation',
    test: 'Test',
    exam: 'Examen',
    report: 'Rapport',
    presentation: 'Présentation',
    project: 'Projet',
    workshop: 'Atelier',
    other: 'Autre'
  },
  he: {
    achievement: 'הישג',
    milestone: 'אבן דרך',
    assessment: 'הערכה',
    test: 'מבחן',
    exam: 'בחינה',
    report: 'דוח',
    presentation: 'מצגת',
    project: 'פרויקט',
    workshop: 'סדנה',
    other: 'אחר'
  },
  hi: {
    achievement: 'उपलब्धि',
    milestone: 'मील का पत्थर',
    assessment: 'मूल्यांकन',
    test: 'परीक्षण',
    exam: 'परीक्षा',
    report: 'रिपोर्ट',
    presentation: 'प्रस्तुति',
    project: 'परियोजना',
    workshop: 'कार्यशाला',
    other: 'अन्य'
  },
  it: {
    achievement: 'Risultato',
    milestone: 'Pietra miliare',
    assessment: 'Valutazione',
    test: 'Test',
    exam: 'Esame',
    report: 'Rapporto',
    presentation: 'Presentazione',
    project: 'Progetto',
    workshop: 'Workshop',
    other: 'Altro'
  },
  ja: {
    achievement: '達成',
    milestone: 'マイルストーン',
    assessment: '評価',
    test: 'テスト',
    exam: '試験',
    report: 'レポート',
    presentation: 'プレゼンテーション',
    project: 'プロジェクト',
    workshop: 'ワークショップ',
    other: 'その他'
  },
  ko: {
    achievement: '성취',
    milestone: '마일스톤',
    assessment: '평가',
    test: '테스트',
    exam: '시험',
    report: '보고서',
    presentation: '발표',
    project: '프로젝트',
    workshop: '워크숍',
    other: '기타'
  },
  nl: {
    achievement: 'Prestatie',
    milestone: 'Mijlpaal',
    assessment: 'Beoordeling',
    test: 'Test',
    exam: 'Examen',
    report: 'Rapport',
    presentation: 'Presentatie',
    project: 'Project',
    workshop: 'Workshop',
    other: 'Overig'
  },
  pl: {
    achievement: 'Osiągnięcie',
    milestone: 'Kamień milowy',
    assessment: 'Ocena',
    test: 'Test',
    exam: 'Egzamin',
    report: 'Raport',
    presentation: 'Prezentacja',
    project: 'Projekt',
    workshop: 'Warsztat',
    other: 'Inne'
  },
  pt: {
    achievement: 'Conquista',
    milestone: 'Marco',
    assessment: 'Avaliação',
    test: 'Teste',
    exam: 'Exame',
    report: 'Relatório',
    presentation: 'Apresentação',
    project: 'Projeto',
    workshop: 'Workshop',
    other: 'Outro'
  },
  tr: {
    achievement: 'Başarı',
    milestone: 'Kilometre Taşı',
    assessment: 'Değerlendirme',
    test: 'Test',
    exam: 'Sınav',
    report: 'Rapor',
    presentation: 'Sunum',
    project: 'Proje',
    workshop: 'Atölye',
    other: 'Diğer'
  },
  uk: {
    achievement: 'Досягнення',
    milestone: 'Віха',
    assessment: 'Оцінювання',
    test: 'Тест',
    exam: 'Іспит',
    report: 'Звіт',
    presentation: 'Презентація',
    project: 'Проект',
    workshop: 'Воркшоп',
    other: 'Інше'
  },
  zh: {
    achievement: '成就',
    milestone: '里程碑',
    assessment: '评估',
    test: '测试',
    exam: '考试',
    report: '报告',
    presentation: '演示',
    project: '项目',
    workshop: '工作坊',
    other: '其他'
  }
};

// Update each language file
languages.forEach(lang => {
  const filePath = path.join(__dirname, '..', 'translations', 'messages', `${lang}.json`);

  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const jsonContent = JSON.parse(fileContent);

    // Update the event types
    if (jsonContent.learningDocumentation) {
      jsonContent.learningDocumentation.eventTypes = eventTypeTranslations[lang] || eventTypeTranslations.en;
    }

    // Write the updated content back to the file
    fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2), 'utf8');
    console.log(`Updated ${lang}.json`);
  } catch (error) {
    console.error(`Error updating ${lang}.json:`, error);
  }
});

console.log('All translation files updated successfully!');
