/**
 * Simple script to check for missing translations in the booking confirmation section
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

// Get all translation files
const translationFiles = fs.readdirSync(TRANSLATIONS_DIR)
  .filter(file => file.endsWith('.json'));

// Load the English translations as the reference
const enTranslations = JSON.parse(
  fs.readFileSync(path.join(TRANSLATIONS_DIR, 'en.json'), 'utf8')
);

// Define the sections to check
const sectionsToCheck = [
  'common',
  'dashboardPage.bookingConfirmation'
];

// Function to get nested value from an object
function getNestedValue(obj, path) {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current === undefined || current === null) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
}

// Function to get all keys from a nested object
function getAllKeys(obj, prefix = '') {
  let keys = [];
  
  for (const key in obj) {
    const newPrefix = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = [...keys, ...getAllKeys(obj[key], newPrefix)];
    } else {
      keys.push(newPrefix);
    }
  }
  
  return keys;
}

// Get all keys from the sections we want to check
let keysToCheck = [];
for (const section of sectionsToCheck) {
  const sectionObj = getNestedValue(enTranslations, section);
  if (sectionObj) {
    const sectionKeys = getAllKeys(sectionObj, section);
    keysToCheck = [...keysToCheck, ...sectionKeys];
  }
}

console.log(`Found ${keysToCheck.length} keys to check in the English reference file.`);

// Check each translation file
for (const file of translationFiles) {
  if (file === 'en.json') continue; // Skip the English file
  
  const locale = file.replace('.json', '');
  console.log(`\nChecking ${locale}...`);
  
  const translations = JSON.parse(
    fs.readFileSync(path.join(TRANSLATIONS_DIR, file), 'utf8')
  );
  
  const missingKeys = [];
  
  for (const key of keysToCheck) {
    const value = getNestedValue(translations, key);
    if (value === undefined) {
      missingKeys.push(key);
    }
  }
  
  if (missingKeys.length > 0) {
    console.log(`  ❌ Missing ${missingKeys.length} keys:`);
    missingKeys.forEach(key => console.log(`    - ${key}`));
  } else {
    console.log(`  ✅ All keys present`);
  }
}
