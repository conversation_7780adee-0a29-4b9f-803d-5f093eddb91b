/**
 * <PERSON>ript to update translations for certificate details modal
 * Adds translations for all 20 supported languages
 */

const fs = require('fs');
const path = require('path');

// List of all language codes
const languages = [
  'ar', 'bn', 'cs', 'da', 'de', 'el', 'en', 'es', 
  'fr', 'he', 'hi', 'it', 'ja', 'ko', 'nl', 'pl', 
  'pt', 'tr', 'uk', 'zh'
];

// Translations for certificate details modal
const certificateDetailsTranslations = {
  ar: {
    certificateDetailsTitle: 'تفاصيل الشهادة',
    certificateImageLabel: 'صورة الشهادة',
    closeButton: 'إغلاق',
    imageLoadError: 'تعذر تحميل الصورة',
    titleLabel: 'عنوان الشهادة',
    issueDateLabel: 'تاريخ الإصدار',
    issueDateISOLabel: 'تاريخ الإصدار (ISO)',
    relatedSkillsLabel: 'المهارات ذات الصلة',
    certificateIdLabel: 'معرف الشهادة'
  },
  bn: {
    certificateDetailsTitle: 'সার্টিফিকেট বিবরণ',
    certificateImageLabel: 'সার্টিফিকেট ছবি',
    closeButton: 'বন্ধ করুন',
    imageLoadError: 'ছবি লোড করা যায়নি',
    titleLabel: 'সার্টিফিকেট শিরোনাম',
    issueDateLabel: 'ইস্যু তারিখ',
    issueDateISOLabel: 'ইস্যু তারিখ (ISO)',
    relatedSkillsLabel: 'সম্পর্কিত দক্ষতা',
    certificateIdLabel: 'সার্টিফিকেট আইডি'
  },
  cs: {
    certificateDetailsTitle: 'Detaily certifikátu',
    certificateImageLabel: 'Obrázek certifikátu',
    closeButton: 'Zavřít',
    imageLoadError: 'Obrázek nelze načíst',
    titleLabel: 'Název certifikátu',
    issueDateLabel: 'Datum vydání',
    issueDateISOLabel: 'Datum vydání (ISO)',
    relatedSkillsLabel: 'Související dovednosti',
    certificateIdLabel: 'ID certifikátu'
  },
  da: {
    certificateDetailsTitle: 'Certifikatdetaljer',
    certificateImageLabel: 'Certifikatbillede',
    closeButton: 'Luk',
    imageLoadError: 'Billedet kunne ikke indlæses',
    titleLabel: 'Certifikattitel',
    issueDateLabel: 'Udstedelsesdato',
    issueDateISOLabel: 'Udstedelsesdato (ISO)',
    relatedSkillsLabel: 'Relaterede færdigheder',
    certificateIdLabel: 'Certifikat-ID'
  },
  de: {
    certificateDetailsTitle: 'Zertifikatsdetails',
    certificateImageLabel: 'Zertifikatsbild',
    closeButton: 'Schließen',
    imageLoadError: 'Bild konnte nicht geladen werden',
    titleLabel: 'Zertifikatstitel',
    issueDateLabel: 'Ausstellungsdatum',
    issueDateISOLabel: 'Ausstellungsdatum (ISO)',
    relatedSkillsLabel: 'Zugehörige Fähigkeiten',
    certificateIdLabel: 'Zertifikats-ID'
  },
  el: {
    certificateDetailsTitle: 'Λεπτομέρειες Πιστοποιητικού',
    certificateImageLabel: 'Εικόνα Πιστοποιητικού',
    closeButton: 'Κλείσιμο',
    imageLoadError: 'Η εικόνα δεν μπόρεσε να φορτωθεί',
    titleLabel: 'Τίτλος Πιστοποιητικού',
    issueDateLabel: 'Ημερομηνία Έκδοσης',
    issueDateISOLabel: 'Ημερομηνία Έκδοσης (ISO)',
    relatedSkillsLabel: 'Σχετικές Δεξιότητες',
    certificateIdLabel: 'Αναγνωριστικό Πιστοποιητικού'
  },
  en: {
    certificateDetailsTitle: 'Certificate Details',
    certificateImageLabel: 'Certificate Image',
    closeButton: 'Close',
    imageLoadError: 'Image could not be loaded',
    titleLabel: 'Certificate Title',
    issueDateLabel: 'Issue Date',
    issueDateISOLabel: 'Issue Date (ISO)',
    relatedSkillsLabel: 'Related Skills',
    certificateIdLabel: 'Certificate ID'
  },
  es: {
    certificateDetailsTitle: 'Detalles del Certificado',
    certificateImageLabel: 'Imagen del Certificado',
    closeButton: 'Cerrar',
    imageLoadError: 'No se pudo cargar la imagen',
    titleLabel: 'Título del Certificado',
    issueDateLabel: 'Fecha de Emisión',
    issueDateISOLabel: 'Fecha de Emisión (ISO)',
    relatedSkillsLabel: 'Habilidades Relacionadas',
    certificateIdLabel: 'ID del Certificado'
  },
  fr: {
    certificateDetailsTitle: 'Détails du Certificat',
    certificateImageLabel: 'Image du Certificat',
    closeButton: 'Fermer',
    imageLoadError: 'Impossible de charger l\'image',
    titleLabel: 'Titre du Certificat',
    issueDateLabel: 'Date d\'Émission',
    issueDateISOLabel: 'Date d\'Émission (ISO)',
    relatedSkillsLabel: 'Compétences Associées',
    certificateIdLabel: 'ID du Certificat'
  },
  he: {
    certificateDetailsTitle: 'פרטי תעודה',
    certificateImageLabel: 'תמונת תעודה',
    closeButton: 'סגור',
    imageLoadError: 'לא ניתן לטעון את התמונה',
    titleLabel: 'כותרת התעודה',
    issueDateLabel: 'תאריך הנפקה',
    issueDateISOLabel: 'תאריך הנפקה (ISO)',
    relatedSkillsLabel: 'מיומנויות קשורות',
    certificateIdLabel: 'מזהה תעודה'
  },
  hi: {
    certificateDetailsTitle: 'प्रमाणपत्र विवरण',
    certificateImageLabel: 'प्रमाणपत्र छवि',
    closeButton: 'बंद करें',
    imageLoadError: 'छवि लोड नहीं की जा सकी',
    titleLabel: 'प्रमाणपत्र शीर्षक',
    issueDateLabel: 'जारी करने की तिथि',
    issueDateISOLabel: 'जारी करने की तिथि (ISO)',
    relatedSkillsLabel: 'संबंधित कौशल',
    certificateIdLabel: 'प्रमाणपत्र आईडी'
  },
  it: {
    certificateDetailsTitle: 'Dettagli del Certificato',
    certificateImageLabel: 'Immagine del Certificato',
    closeButton: 'Chiudi',
    imageLoadError: 'Impossibile caricare l\'immagine',
    titleLabel: 'Titolo del Certificato',
    issueDateLabel: 'Data di Emissione',
    issueDateISOLabel: 'Data di Emissione (ISO)',
    relatedSkillsLabel: 'Competenze Correlate',
    certificateIdLabel: 'ID del Certificato'
  },
  ja: {
    certificateDetailsTitle: '証明書の詳細',
    certificateImageLabel: '証明書の画像',
    closeButton: '閉じる',
    imageLoadError: '画像を読み込めませんでした',
    titleLabel: '証明書のタイトル',
    issueDateLabel: '発行日',
    issueDateISOLabel: '発行日 (ISO)',
    relatedSkillsLabel: '関連スキル',
    certificateIdLabel: '証明書ID'
  },
  ko: {
    certificateDetailsTitle: '인증서 상세 정보',
    certificateImageLabel: '인증서 이미지',
    closeButton: '닫기',
    imageLoadError: '이미지를 불러올 수 없습니다',
    titleLabel: '인증서 제목',
    issueDateLabel: '발급일',
    issueDateISOLabel: '발급일 (ISO)',
    relatedSkillsLabel: '관련 기술',
    certificateIdLabel: '인증서 ID'
  },
  nl: {
    certificateDetailsTitle: 'Certificaatdetails',
    certificateImageLabel: 'Certificaatafbeelding',
    closeButton: 'Sluiten',
    imageLoadError: 'Afbeelding kon niet worden geladen',
    titleLabel: 'Certificaattitel',
    issueDateLabel: 'Uitgiftedatum',
    issueDateISOLabel: 'Uitgiftedatum (ISO)',
    relatedSkillsLabel: 'Gerelateerde vaardigheden',
    certificateIdLabel: 'Certificaat-ID'
  },
  pl: {
    certificateDetailsTitle: 'Szczegóły Certyfikatu',
    certificateImageLabel: 'Obraz Certyfikatu',
    closeButton: 'Zamknij',
    imageLoadError: 'Nie można załadować obrazu',
    titleLabel: 'Tytuł Certyfikatu',
    issueDateLabel: 'Data Wydania',
    issueDateISOLabel: 'Data Wydania (ISO)',
    relatedSkillsLabel: 'Powiązane Umiejętności',
    certificateIdLabel: 'ID Certyfikatu'
  },
  pt: {
    certificateDetailsTitle: 'Detalhes do Certificado',
    certificateImageLabel: 'Imagem do Certificado',
    closeButton: 'Fechar',
    imageLoadError: 'Não foi possível carregar a imagem',
    titleLabel: 'Título do Certificado',
    issueDateLabel: 'Data de Emissão',
    issueDateISOLabel: 'Data de Emissão (ISO)',
    relatedSkillsLabel: 'Habilidades Relacionadas',
    certificateIdLabel: 'ID do Certificado'
  },
  tr: {
    certificateDetailsTitle: 'Sertifika Detayları',
    certificateImageLabel: 'Sertifika Resmi',
    closeButton: 'Kapat',
    imageLoadError: 'Resim yüklenemedi',
    titleLabel: 'Sertifika Başlığı',
    issueDateLabel: 'Veriliş Tarihi',
    issueDateISOLabel: 'Veriliş Tarihi (ISO)',
    relatedSkillsLabel: 'İlgili Beceriler',
    certificateIdLabel: 'Sertifika Kimliği'
  },
  uk: {
    certificateDetailsTitle: 'Деталі сертифіката',
    certificateImageLabel: 'Зображення сертифіката',
    closeButton: 'Закрити',
    imageLoadError: 'Не вдалося завантажити зображення',
    titleLabel: 'Назва сертифіката',
    issueDateLabel: 'Дата видачі',
    issueDateISOLabel: 'Дата видачі (ISO)',
    relatedSkillsLabel: 'Пов\'язані навички',
    certificateIdLabel: 'ID сертифіката'
  },
  zh: {
    certificateDetailsTitle: '证书详情',
    certificateImageLabel: '证书图片',
    closeButton: '关闭',
    imageLoadError: '无法加载图片',
    titleLabel: '证书标题',
    issueDateLabel: '颁发日期',
    issueDateISOLabel: '颁发日期 (ISO)',
    relatedSkillsLabel: '相关技能',
    certificateIdLabel: '证书ID'
  }
};

// Update each language file
languages.forEach(lang => {
  const filePath = path.join(__dirname, '..', 'translations', 'messages', `${lang}.json`);
  
  try {
    // Read the existing file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const jsonContent = JSON.parse(fileContent);
    
    // Check if the language has translations for this feature
    if (!certificateDetailsTranslations[lang]) {
      console.warn(`No translations found for language: ${lang}`);
      return;
    }
    
    // Ensure the LearningDocumentation.Certificates path exists
    if (!jsonContent.LearningDocumentation) {
      jsonContent.LearningDocumentation = {};
    }
    
    if (!jsonContent.LearningDocumentation.Certificates) {
      jsonContent.LearningDocumentation.Certificates = {};
    }
    
    // Add the new translations
    Object.assign(
      jsonContent.LearningDocumentation.Certificates,
      certificateDetailsTranslations[lang]
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2), 'utf8');
    console.log(`Updated translations for ${lang}`);
  } catch (error) {
    console.error(`Error updating ${lang}:`, error);
  }
});

console.log('Translation update completed!');
