#!/usr/bin/env node

/**
 * Environment Switcher Script (Direct File Edit Version)
 *
 * This script switches between beta and main environments by directly editing
 * the .env and .env.local files in the current directory.
 *
 * Usage:
 *   node scripts/switch-env-direct.js beta
 *   node scripts/switch-env-direct.js main
 */

const fs = require('fs');
const path = require('path');

// Get the environment from command line arguments
const env = process.argv[2];

// Validate the environment
if (!env || (env !== 'beta' && env !== 'main')) {
  console.error(`Error: Please specify a valid environment (beta or main)`);
  console.log(`Usage: node scripts/switch-env-direct.js [beta|main]`);
  process.exit(1);
}

// Environment configurations
const environments = {
  beta: {
    NEXT_PUBLIC_API_URL: 'https://server-beta-fkw4.onrender.com',
    NEXT_PUBLIC_BETA_ENABLED: 'true'
  },
  main: {
    NEXT_PUBLIC_API_URL: 'https://server-gnbo.onrender.com',
    NEXT_PUBLIC_BETA_ENABLED: 'false'
  }
};

// Get the current directory
const currentDir = process.cwd();

// Target files
const envLocalPath = path.join(currentDir, '.env.local');
const envPath = path.join(currentDir, '.env');

// Generate environment file content
const generateEnvContent = (envConfig) => {
  return `# API Configuration
NEXT_PUBLIC_API_URL=${envConfig.NEXT_PUBLIC_API_URL}

# Feature Flags
NEXT_PUBLIC_BETA_ENABLED=${envConfig.NEXT_PUBLIC_BETA_ENABLED}
`;
};

// Get the environment configuration
const envConfig = environments[env];
const envContent = generateEnvContent(envConfig);

console.log(`Switching to ${env} environment...`);

try {
  // Write to .env.local
  console.log(`Writing to ${envLocalPath}`);
  fs.writeFileSync(envLocalPath, envContent, 'utf8');

  // Write to .env
  console.log(`Writing to ${envPath}`);
  fs.writeFileSync(envPath, envContent, 'utf8');

  // Verify the files were created
  if (fs.existsSync(envLocalPath) && fs.existsSync(envPath)) {
    console.log(`✓ Environment variables applied to ${path.basename(currentDir)}`);

    // Verify content was written correctly
    const localContent = fs.readFileSync(envLocalPath, 'utf8');
    const envFileContent = fs.readFileSync(envPath, 'utf8');

    if (localContent === envContent && envFileContent === envContent) {
      console.log(`✓ Environment files verified`);
    } else {
      console.log(`✗ Environment files content verification failed`);
    }
  } else {
    console.log(`✗ Failed to create environment files`);
  }
} catch (error) {
  console.error(`✗ Error: ${error.message}`);
  process.exit(1);
}

console.log(`Environment switched to ${env}`);
console.log(`Note: You may need to restart your development server for changes to take effect`);
