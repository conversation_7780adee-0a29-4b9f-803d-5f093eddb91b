/**
 * <PERSON><PERSON>t to fix plural translations in all language files
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

// Get all translation files
const translationFiles = fs.readdirSync(TRANSLATIONS_DIR)
  .filter(file => file.endsWith('.json'));

// Plural translations to fix
const pluralTranslations = {
  'ar': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# طفل تتم إدارته} other {# أطفال تتم إدارتهم}}'
  },
  'es': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# niño gestionado} other {# niños gestionados}}'
  },
  'he': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# ילד מנוהל} other {# ילדים מנוהלים}}'
  },
  'hi': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# बच्चा प्रबंधित} other {# बच्चे प्रबंधित}}'
  },
  'ja': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# 人の子供を管理中} other {# 人の子供を管理中}}'
  },
  'pt': {
    'dashboardPage.childrenCardDescPlural': '{count, plural, one {# criança gerenciada} other {# crianças gerenciadas}}'
  }
};

// Function to set a nested value in an object
function setNestedValue(obj, path, value) {
  const parts = path.split('.');
  let current = obj;
  
  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    if (!current[part]) {
      current[part] = {};
    }
    current = current[part];
  }
  
  current[parts[parts.length - 1]] = value;
}

// Function to get a nested value from an object
function getNestedValue(obj, path) {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current === undefined || current === null) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
}

// Update each translation file
for (const file of translationFiles) {
  if (file === 'en.json' || file === 'de.json' || file === 'fr.json' || file === 'zh.json') continue; // Skip files that are already fixed
  
  const locale = file.replace('.json', '');
  console.log(`Updating ${locale}...`);
  
  if (!pluralTranslations[locale]) {
    console.log(`  No plural translations defined for ${locale}, skipping`);
    continue;
  }
  
  // Load the translation file
  const filePath = path.join(TRANSLATIONS_DIR, file);
  const translations = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  
  // Fix plural translations
  for (const [path, value] of Object.entries(pluralTranslations[locale])) {
    const currentValue = getNestedValue(translations, path);
    if (currentValue && currentValue.includes('[NEEDS TRANSLATION]')) {
      setNestedValue(translations, path, value);
      console.log(`  Fixed ${path}`);
    }
  }
  
  // Write the updated translations back to the file
  fs.writeFileSync(filePath, JSON.stringify(translations, null, 2));
  console.log(`  Updated ${locale} successfully`);
}

console.log('Done!');
