/**
 * <PERSON>ript to optimize translation files
 * - Sorts keys alphabetically
 * - Ensures consistent formatting
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

// Get all translation files
const translationFiles = fs.readdirSync(TRANSLATIONS_DIR)
  .filter(file => file.endsWith('.json'));

// Function to sort object keys recursively
function sortObjectKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sortObjectKeys);
  }
  
  const sortedObj = {};
  const keys = Object.keys(obj).sort();
  
  for (const key of keys) {
    sortedObj[key] = sortObjectKeys(obj[key]);
  }
  
  return sortedObj;
}

// Optimize each translation file
for (const file of translationFiles) {
  const filePath = path.join(TRANSLATIONS_DIR, file);
  console.log(`Optimizing ${file}...`);
  
  // Load the translation file
  const translations = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  
  // Create a backup
  const backupPath = path.join(TRANSLATIONS_DIR, `${file}.bak`);
  fs.writeFileSync(backupPath, fs.readFileSync(filePath));
  console.log(`  Created backup at ${backupPath}`);
  
  // Sort keys
  const sortedTranslations = sortObjectKeys(translations);
  
  // Write the optimized translations back to the file
  fs.writeFileSync(filePath, JSON.stringify(sortedTranslations, null, 2));
  console.log(`  Optimized ${file} successfully`);
}

console.log('Done!');
