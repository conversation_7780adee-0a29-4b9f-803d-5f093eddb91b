#!/usr/bin/env node

/**
 * Environment Switcher Script
 * 
 * This script switches between beta and main environments by copying the appropriate
 * environment file from the root of the project to the current directory.
 * 
 * Usage:
 *   node scripts/switch-env.js beta
 *   node scripts/switch-env.js main
 */

const fs = require('fs');
const path = require('path');

// Get the environment from command line arguments
const env = process.argv[2];

// Validate the environment
if (!env || (env !== 'beta' && env !== 'main')) {
  console.error(`Error: Please specify a valid environment (beta or main)`);
  console.log(`Usage: node scripts/switch-env.js [beta|main]`);
  process.exit(1);
}

// Find the root directory (where .env.beta and .env.main are located)
const findRootDir = (startDir) => {
  let currentDir = startDir;
  
  // Maximum number of directories to traverse up
  const maxLevels = 5;
  let level = 0;
  
  while (level < maxLevels) {
    // Check if .env.beta and .env.main exist in this directory
    if (
      fs.existsSync(path.join(currentDir, '.env.beta')) && 
      fs.existsSync(path.join(currentDir, '.env.main'))
    ) {
      return currentDir;
    }
    
    // Go up one directory
    const parentDir = path.dirname(currentDir);
    
    // If we've reached the root of the filesystem, stop
    if (parentDir === currentDir) {
      break;
    }
    
    currentDir = parentDir;
    level++;
  }
  
  return null;
};

// Get the current directory
const currentDir = process.cwd();

// Find the root directory
const rootDir = findRootDir(currentDir);

if (!rootDir) {
  console.error(`Error: Could not find root directory containing .env.beta and .env.main files`);
  console.error(`Make sure you're running this script from within the Learning Studio Apps project`);
  process.exit(1);
}

// Define the source environment file
const envFile = path.join(rootDir, `.env.${env}`);

// Check if the environment file exists
if (!fs.existsSync(envFile)) {
  console.error(`Error: Environment file '${envFile}' not found`);
  process.exit(1);
}

console.log(`Switching to ${env} environment...`);

// Read the environment file content
const envContent = fs.readFileSync(envFile, 'utf8');

// Target files
const envLocalPath = path.join(currentDir, '.env.local');
const envPath = path.join(currentDir, '.env');

try {
  // Remove existing files if they exist
  if (fs.existsSync(envLocalPath)) {
    fs.unlinkSync(envLocalPath);
  }
  if (fs.existsSync(envPath)) {
    fs.unlinkSync(envPath);
  }
  
  // Write new files
  fs.writeFileSync(envLocalPath, envContent);
  fs.writeFileSync(envPath, envContent);
  
  // Verify the files were created
  if (fs.existsSync(envLocalPath) && fs.existsSync(envPath)) {
    console.log(`✓ Environment variables applied to ${path.basename(currentDir)}`);
  } else {
    console.log(`✗ Failed to create environment files`);
  }
} catch (error) {
  console.error(`✗ Error: ${error.message}`);
}

console.log(`Environment switched to ${env}`);
console.log(`Note: You may need to restart your development server for changes to take effect`);
