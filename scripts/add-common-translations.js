/**
 * <PERSON><PERSON>t to add missing common translations to all language files
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, '../translations/messages');

// Get all translation files
const translationFiles = fs.readdirSync(TRANSLATIONS_DIR)
  .filter(file => file.endsWith('.json'));

// Load the English translations as the reference
const enTranslations = JSON.parse(
  fs.readFileSync(path.join(TRANSLATIONS_DIR, 'en.json'), 'utf8')
);

// Common translations to add
const commonTranslations = {
  'ar': {
    'home': 'الرئيسية',
    'shop': 'المتجر',
    'logout': 'تسجيل الخروج',
    'betaTag': 'تجريبي'
  },
  'es': {
    'home': 'Inicio',
    'shop': 'Tienda',
    'logout': '<PERSON><PERSON><PERSON> se<PERSON>',
    'betaTag': 'BETA'
  },
  'he': {
    'home': 'דף הבית',
    'shop': 'חנות',
    'logout': 'התנתק',
    'betaTag': 'בטא'
  },
  'hi': {
    'home': 'होम',
    'shop': 'दुकान',
    'logout': 'लॉगआउट',
    'betaTag': 'बीटा'
  },
  'ja': {
    'home': 'ホーム',
    'shop': 'ショップ',
    'logout': 'ログアウト',
    'betaTag': 'ベータ'
  },
  'pt': {
    'home': 'Início',
    'shop': 'Loja',
    'logout': 'Sair',
    'betaTag': 'BETA'
  }
};

// Update each translation file
for (const file of translationFiles) {
  if (file === 'en.json' || file === 'de.json' || file === 'fr.json' || file === 'zh.json') continue; // Skip files that are already fixed
  
  const locale = file.replace('.json', '');
  console.log(`Updating ${locale}...`);
  
  if (!commonTranslations[locale]) {
    console.log(`  No translations defined for ${locale}, skipping`);
    continue;
  }
  
  // Load the translation file
  const filePath = path.join(TRANSLATIONS_DIR, file);
  const translations = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  
  // Add missing translations
  if (!translations.common) {
    translations.common = {};
  }
  
  for (const [key, value] of Object.entries(commonTranslations[locale])) {
    if (!translations.common[key]) {
      translations.common[key] = value;
      console.log(`  Added ${key}: ${value}`);
    }
  }
  
  // Write the updated translations back to the file
  fs.writeFileSync(filePath, JSON.stringify(translations, null, 2));
  console.log(`  Updated ${locale} successfully`);
}

console.log('Done!');
