/**
 * <PERSON><PERSON><PERSON> to add the "outsideOpeningHours" translation to all language files
 */

const fs = require('fs');
const path = require('path');

// Define the path to the translations directory
const TRANSLATIONS_DIR = path.join(__dirname, 'translations/messages');

// List of all language files
const languages = [
  'ar', 'bn', 'cs', 'da', 'de', 'el', 'en', 'es', 'fr', 'he', 
  'hi', 'it', 'ja', 'ko', 'nl', 'pl', 'pt', 'tr', 'uk', 'zh'
];

// Translations for "Bookings are only possible within the studio opening hours" in different languages
const translations = {
  ar: "الحجوزات ممكنة فقط خلال ساعات عمل الاستوديو.",
  bn: "স্টুডিওর খোলার সময়ের মধ্যেই কেবল বুকিং সম্ভব।",
  cs: "Rezervace jsou možné pouze v rámci otevírací doby studia.",
  da: "Bookinger er kun mulige inden for studiets åbningstider.",
  de: "Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.",
  el: "Οι κρατήσεις είναι δυνατές μόνο εντός των ωρών λειτουργίας του στούντιο.",
  en: "Bookings are only possible within the studio opening hours.",
  es: "Las reservas solo son posibles dentro del horario de apertura del estudio.",
  fr: "Les réservations ne sont possibles que pendant les heures d'ouverture du studio.",
  he: "הזמנות אפשריות רק בשעות הפתיחה של הסטודיו.",
  hi: "बुकिंग केवल स्टूडियो के खुलने के समय के भीतर ही संभव है।",
  it: "Le prenotazioni sono possibili solo durante l'orario di apertura dello studio.",
  ja: "予約はスタジオの営業時間内でのみ可能です。",
  ko: "예약은 스튜디오 영업 시간 내에서만 가능합니다.",
  nl: "Boekingen zijn alleen mogelijk binnen de openingstijden van de studio.",
  pl: "Rezerwacje są możliwe tylko w godzinach otwarcia studia.",
  pt: "As reservas só são possíveis dentro do horário de funcionamento do estúdio.",
  tr: "Rezervasyonlar yalnızca stüdyo açılış saatleri içinde mümkündür.",
  uk: "Бронювання можливе лише в години роботи студії.",
  zh: "预订仅在工作室开放时间内可行。"
};

// Process each language file
for (const lang of languages) {
  const filePath = path.join(TRANSLATIONS_DIR, `${lang}.json`);
  
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const jsonContent = JSON.parse(fileContent);
    
    // Check if the dashboardPage.bookingConfirmation path exists
    if (jsonContent.dashboardPage && jsonContent.dashboardPage.bookingConfirmation) {
      // Add the outsideOpeningHours translation
      jsonContent.dashboardPage.bookingConfirmation.outsideOpeningHours = translations[lang] || translations.en;
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2), 'utf8');
      console.log(`Updated ${lang}.json`);
    } else {
      console.log(`Skipping ${lang}.json - dashboardPage.bookingConfirmation path not found`);
    }
  } catch (error) {
    console.error(`Error processing ${lang}.json:`, error.message);
  }
}

console.log('Translation update complete!');
