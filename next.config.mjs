import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

const nextConfig = {
  /* config options here */
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL
  },
  images: {
    domains: [
      'www.google.com',
      'google.com',
      'images.unsplash.com',
      'istockphoto.com',
      'www.istockphoto.com',
      'media.istockphoto.com',
      'cdn.pixabay.com',
      'pixabay.com',
      'img.freepik.com',
      'freepik.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
        pathname: '/**',
      }
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;"
  },
  async rewrites() {
    // Use environment variable for API URL
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';

    return [
      // Exclude specific API routes that have custom handlers
      // This allows our custom API route handlers to process the request
      {
        source: '/api/learning-events/:path*',
        destination: '/api/learning-events/:path*'
      },
      // Default fallback for all other API routes
      {
        source: '/api/:path*',
        destination: `${apiUrl}/:path*`
      }
    ];
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,DELETE,PATCH,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization' },
        ],
      },
    ];
  },
};

export default withNextIntl(nextConfig);
