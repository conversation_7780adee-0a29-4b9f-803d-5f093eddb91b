import { getRequestConfig } from 'next-intl/server';
import { loadTranslationMessages } from '@/utils/i18n';
import { defaultLocale } from '@/lib/i18n';

export default getRequestConfig(async ({ locale: requestedLocale }) => {
  // Use the centralized translation loading utility
  // Provide a fallback locale if requestedLocale is undefined
  const { messages, locale: localeToUse } = await loadTranslationMessages(requestedLocale ?? defaultLocale);

  // Provide the configuration object
  return {
    locale: localeToUse,
    messages: messages,
    // Set timeZone to ensure consistency across the app
    timeZone: 'Europe/Berlin'
  };
});