{"common": {"appName": "Parents App", "betaTag": "BETA", "home": "Home", "logout": "Logout", "shop": "Shop"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Belgium", "Brazil": "Brazil", "Bulgaria": "Bulgaria", "Canada": "Canada", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Denmark", "Finland": "Finland", "France": "France", "Germany": "Germany", "India": "India", "Italy": "Italy", "Japan": "Japan", "Luxembourg": "Luxembourg", "Mexico": "Mexico", "Netherlands": "Netherlands", "New Zealand": "New Zealand", "Norway": "Norway", "Russia": "Russia", "Singapore": "Singapore", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Sweden": "Sweden", "Switzerland": "Switzerland", "United Kingdom": "United Kingdom", "United States": "United States", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. All rights reserved.", "footerImprint": "Imprint", "footerPrivacy": "Privacy", "help": "Help", "navDashboard": "Dashboard", "navLearningProgress": "Learning Progress", "navLogout": "Logout", "navPurchases": "Purchases", "navShop": "Shop", "navStudios": "Studios", "privacy": "Privacy", "selectLanguage": "Select Language", "settings": "Settings", "toggleMenu": "Toggle <PERSON>"}, "dashboardPage": {"addChildBookingPermissionLabel": "Allow Child to Book Sessions?", "addChildBudgetLabel": "Initial Budget (€)", "addChildBudgetPlaceholder": "e.g., 50.00", "addChildButton": "Add New Child", "addChildCancelButton": "Cancel", "addChildCardDesc": "Create a new child account to manage", "addChildModalTitle": "Add New Child", "addChildNameLabel": "Child's Name", "addChildPasswordLabel": "Child's Password", "addChildPasswordPlaceholder": "Create password", "addChildSubmitButton": "Add Child", "addChildUsernameLabel": "<PERSON>'s <PERSON><PERSON><PERSON>", "addChildUsernamePlaceholder": "Create unique username", "addFirstChildButton": "Add Your First Child", "addFundsAmountLabel": "Amount (€)", "addFundsButton": "Add Funds", "addFundsCancelButton": "Cancel", "addFundsHelpText": "Enter the amount you want to add to the child's budget. The funds will be available immediately.", "addFundsModalTitle": "Add Funds to {0}'s Account", "addFundsSubmitButton": "Add Funds", "bookingAlertEventAdded": "New event \"{0}\" added!", "bookingAlertEventMoved": "{0} was moved to {1}", "bookingAlertEventResized": "{0} was resized to end at {1}", "bookingAlertSelectionCancelled": "Selection cancelled.", "bookingConfirmViewDetails": "View details for \"{0}\"? \nDescription: {1}", "bookingConfirmation": {"availability": "Availability", "available": "Available", "biweekly": "Bi-weekly", "bookingConflictDetailed": "Booking conflict detected for {childName} in {roomName}. The requested time ({requestedTime}) conflicts with an existing booking ({conflictingTime}). Please select a different time slot.", "bookingCost": "Booking Cost", "bookingDetails": "Booking Details", "bookingFailed": "Booking Failed", "budgetInformation": "Budget Information", "cancelButton": "Cancel", "checking": "Checking...", "child": "Child", "confirmButton": "Confirm Booking", "currentBudget": "Current Budget", "date": "Date", "deleteSuccessDescription": "Your booking has been deleted.", "deleteSuccessMessage": "Booking Successfully Deleted!", "duration": "Duration", "enableRecurringBooking": "Enable Recurring Booking", "error": "Error", "errorLoadingBudget": "Error loading budget information", "friday": "Friday", "hours": "hours", "insufficientBudget": "Insufficient Budget", "insufficientBudgetMessage": "The child does not have enough budget for this booking. Please add funds or select a shorter time slot.", "monday": "Monday", "monthly": "Monthly", "none": "None", "notAvailable": "Not Available", "numberOfBookings": "Number of Bookings", "numberOfOccurrences": "Number of Occurrences", "pricePerHour": "Price per hour", "processing": "Processing...", "recurrenceType": "Recurrence Type", "recurringBooking": "Recurring Booking", "remainingBudget": "Remaining Budget", "room": "Room", "saturday": "Saturday", "selectDaysOfWeek": "Select Days of Week", "selectType": "Select Type", "singleBookingCost": "Single Booking Cost", "studio": "Studio", "successDescription": "Your booking has been confirmed.", "successMessage": "Booking Successful!", "sunday": "Sunday", "thursday": "Thursday", "time": "Time", "timeSlotBooked": "This time slot is already booked. Please select another time.", "title": "Confirm Booking", "totalPrice": "Total Price", "tryAgainMessage": "Please try selecting a different time slot or contact support if the problem persists.", "outsideOpeningHours": "Bookings are only possible within the studio opening hours.", "recurringBookingCapacityExceeded": "One or more of the recurring bookings would exceed the room capacity. Please reduce the number of occurrences or choose a different time slot.", "biweeklyRecurrenceInfo": "The booking will repeat every two weeks on the same day for the specified number of occurrences.", "limitedByBudget": "Limited by budget", "limitedByCapacity": "Limited by available space", "tuesday": "Tuesday", "wednesday": "Wednesday", "weekly": "Weekly", "weeklyRecurrenceInfo": "The booking will repeat on the selected days for the specified number of weeks."}, "bookingDescriptionNone": "None", "bookingModalTitle": "Book Session for {0}", "bookingPromptEventTitle": "Enter a title for your event:", "childBookAction": "Book appointment", "childBookDisabled": "Booking disabled", "childBudgetAvailable": "Available", "childBudgetEmpty": "Empty", "childCardAddFundsButton": "Add Funds", "childCardBookButton": "Book Session", "childCardBookingLabel": "Booking Allowed:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Delete", "childCardEditButton": "Edit", "childCardNameLabel": "Name:", "childCardUsernameLabel": "Username:", "childStatusCanBook": "Child Can Book", "childStatusNoBooking": "Parent Only Booking", "childrenCardDescPlural": "{count, plural, one {# child managed} other {# children managed}}", "childrenCardDescSingular": "1 active child account", "childrenCardTitle": "Children", "classLabel": "Class/Grade", "countryLabel": "Country", "editChildAllowBookingsDescription": "If checked, the child can log in and book sessions themselves using their budget. If unchecked, only parents can book appointments for this child. Parents can always book appointments for their children regardless of this setting.", "editChildAllowBookingsLabel": "Allow Child to Book Sessions?", "editChildCancelButtonText": "Cancel", "editChildNameLabel": "Child's Name", "editChildNamePlaceholder": "Enter child's name", "editChildPasswordHelpText": "Only enter if you want to change the child's password.", "editChildPasswordLabelOptional": "New Password (Optional)", "editChildPasswordPlaceholderOptional": "Leave blank to keep current", "editChildSaveButtonText": "Save Changes", "editChildTitle": "Edit Child Details", "editChildUpdateErrorMessage": "Error updating child details. Please try again.", "editChildUpdateSuccessMessage": "Child details updated successfully!", "editChildUpdatingButtonText": "Updating...", "languageLabel": "Language", "managePrompt": "Manage your children's accounts and book learning sessions", "myChildrenTitle": "My Children", "noChildrenFoundDesc": "Use the form below to add your first child account to start managing bookings", "noChildrenFoundTitle": "No children found", "paymentHistoryButton": "Payment History", "paymentHistoryCloseButton": "Close", "paymentHistoryExportBills": "Download All Bills", "paymentHistoryExportButton": "Export Options", "paymentHistoryExportCsv": "Export as CSV", "paymentHistoryFilterAll": "All Transactions", "paymentHistoryFilterDeposits": "Deposits Only", "paymentHistoryFilterWithdrawals": "Withdrawals Only", "paymentHistoryModalTitle": "Payment History", "paymentHistorySearchPlaceholder": "Search...", "paymentHistorySummaryDeposits": "Total Deposits", "paymentHistorySummaryNet": "Net Balance Change", "paymentHistorySummaryWithdrawals": "Total Withdrawals", "paymentHistoryTableActions": "Actions", "paymentHistoryTableAmount": "Amount (€)", "paymentHistoryTableChild": "Child", "paymentHistoryTableDate": "Date", "paymentHistoryTableDesc": "Description", "paymentHistoryTableType": "Type", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "<PERSON><PERSON><PERSON>", "preferredStudioDesc": "This studio will be pre-selected when booking activities", "preferredStudioTitle": "Preferred Learning Studio", "regionDisabledText": "Region not applicable for selected country", "regionLabel": "Region", "schoolTypeLabel": "School Type", "selectChildCurrentBudgetLabel": "Current Budget:", "selectChildFundsCancelButton": "Cancel", "selectChildFundsModalTitle": "Select Child to Add Funds", "selectChildFundsPrompt": "Choose which child's account to top up:", "selectCountryFirst": "Select country first", "selectPlaceholder": "Select...", "selectStudioPlaceholder": "-- Select a Studio --", "statusAddChildError": "Error adding child: {0}", "statusAddChildSuccess": "Child added successfully", "statusAddFundsError": "Error adding funds: {0}", "statusAddFundsSuccess": "Successfully added €{0} to {1}'s account", "statusAuthRequired": "Authentication required.", "statusDeleteChildConfirm": "Are you sure you want to delete child ID {0}? This cannot be undone.", "statusDeleteChildError": "Error deleting child: {0}", "statusDeleteChildSuccess": "Child deleted successfully", "statusErrorChildren": "Error fetching children", "statusErrorInitialData": "Error fetching initial data: {0}", "statusErrorPreferredStudio": "Error fetching preferred studio", "statusErrorStudios": "Error fetching studios", "statusFindChildError": "Could not find child details.", "statusLoading": "Loading...", "statusUnexpectedError": "An unexpected error occurred.", "statusUpdateChildSuccess": "Child details updated successfully.", "statusUpdatePreferredError": "Error updating preferred studio: {0}", "statusUpdatePreferredSuccess": "Preferred studio updated successfully", "subjectsLabel": "Subjects", "totalBudgetCardTitle": "Total Budget", "welcomeMessage": "Welcome"}, "helpModal": {"bookingProcessIntro": "To book a learning session:", "bookingProcessLi1": "Select a child from the dashboard", "bookingProcessLi2": "Click the calendar icon to open the booking modal", "bookingProcessLi3": "Choose a date, time, and activity type", "bookingProcessLi4": "Confirm the booking (budget will be automatically deducted)", "bookingProcessLi5": "Track all bookings in the Purchases section", "bookingProcessTitle": "Booking Process", "contactSupportButton": "Contact Support", "faq1Answer": "Use the \"Add Funds\" button in the dashboard to top up your child's learning budget. You can use credit card, PayPal, or bank transfer.", "faq1Question": "How do I add funds to my child's account?", "faq2Answer": "Yes. When creating a child account, you set up a username and password. Your child can use these credentials to access their own limited dashboard.", "faq2Question": "Can my child log in separately?", "faq3Answer": "Navigate to the Purchases page, find the booking you want to cancel, and click \"View Details.\" You'll find a cancel option if the session is more than 24 hours away.", "faq3Question": "How do I cancel a booking?", "faqTitle": "Frequently Asked Questions", "gettingStartedLi1": "Add your children using the \"Add New Child\" button", "gettingStartedLi2": "Set your preferred learning studio in the dashboard", "gettingStartedLi3": "Manage each child's budget for activities", "gettingStartedLi4": "Book learning sessions and track progress", "gettingStartedTitle": "Getting Started", "gettingStartedWelcome": "Welcome to the Booking Parents app! This platform helps you manage your children's learning activities and bookings in one place.", "managingChildrenAddBudget": "Add Budget", "managingChildrenAddBudgetDesc": ": Top up your child's learning budget", "managingChildrenBook": "Book", "managingChildrenBookDesc": ": Schedule learning sessions at your preferred studio", "managingChildrenDelete": "Delete", "managingChildrenDeleteDesc": ": Remove a child account (cannot be undone)", "managingChildrenEdit": "Edit", "managingChildrenEditDesc": ": Update name, budget, and booking permissions", "managingChildrenIntro": "For each child, you can:", "managingChildrenTitle": "Managing Children", "needMoreHelpEmail": "Email:", "needMoreHelpHours": "Hours: Monday-Friday, 9am-5pm CET", "needMoreHelpIntro": "If you need additional assistance, our support team is here to help:", "needMoreHelpPhone": "Phone:", "needMoreHelpTitle": "Need More Help?", "title": "Help Center"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Close", "companyInfoTitle": "Company Information", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Germany", "directorsLabel": "Managing Directors:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Despite careful content control, we assume no liability for the content of external links. The operators of the linked pages are solely responsible for their content.", "disclaimerTitle": "Disclaimer", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Phone:", "regNumLabel": "Registration Number:", "regNumValue": "HRB 123456", "registerLabel": "Commercial Register:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Regulatory Information", "responsibleLabel": "Responsible for content according to § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Imprint", "vatIdLabel": "VAT ID:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "Arabic", "bn": "Bengali", "cs": "Czech", "da": "Danish", "de": "German", "el": "Greek", "en": "English", "es": "Spanish", "fi": "Finnish", "fr": "French", "gu": "Gujarati", "he": "Hebrew", "hi": "Hindi", "hu": "Hungarian", "it": "Italian", "ja": "Japanese", "jv": "Javanese", "ko": "Korean", "mr": "Marathi", "nl": "Dutch", "no": "Norwegian", "pa": "Punjabi", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "sv": "Swedish", "ta": "Tamil", "te": "Telugu", "th": "Thai", "tr": "Turkish", "uk": "Ukrainian", "ur": "Urdu", "vi": "Vietnamese", "zh": "Chinese"}, "login": {"childProgress": "Child Progress", "easyScheduling": "<PERSON>heduling", "errors": {"connectionError": "Connection error, please check your internet", "invalidCredentials": "Invalid username or password", "licenseValidationFailed": "License validation failed", "loginFailed": "<PERSON><PERSON> failed", "noActiveLicense": "No active license found", "serverError": "Server error, please try again later", "sessionExpired": "Session expired, please log in again", "tryAgainLater": "An error occurred, please try again later", "unableToVerifyLicense": "Unable to verify license", "userNotFound": "User not found", "wrongPassword": "Wrong password"}, "forgotPassword": "Forgot your password?", "languageSwitch": "Switch language", "loggingIn": "Logging in...", "login": "<PERSON><PERSON>", "password": "Password", "summary": "Book learning sessions for your child and track their progress in one place.", "title": "<PERSON><PERSON>", "username": "Username"}, "privacyModal": {"acceptButton": "I Understand and Accept", "childInfoDesc": "Information about your children, including name, age, and learning preferences.", "childInfoTitle": "Child Information:", "childrenIntro": "Our service requires information about children to provide our core booking services. We take additional precautions to protect children's data:", "childrenLi1": "We collect only the minimum necessary information about children", "childrenLi2": "We require parental consent before collecting information from children", "childrenLi3": "We do not make children's personal information publicly available", "childrenLi4": "Parents can review, delete, or refuse further collection of their child's information", "childrenLi5": "We implement additional security measures for children's data", "childrenTitle": "Children's Privacy", "contactAddress": "Address:", "contactEmail": "Email:", "contactIntro": "If you have any questions about this Privacy Policy, please contact us:", "contactPhone": "Phone:", "contactTitle": "Contact Us", "cookiesDesc": "We use cookies and similar tracking technologies to track activity on our Platform and hold certain information.", "cookiesTitle": "Cookies and Tracking:", "howWeUseIntro": "We may use the information we collect about you for various purposes:", "howWeUseLi1": "To provide and maintain our service", "howWeUseLi2": "To notify you about changes to our service", "howWeUseLi3": "To allow you to participate in interactive features when you choose to do so", "howWeUseLi4": "To provide customer support", "howWeUseLi5": "To gather analysis or valuable information to improve our service", "howWeUseLi6": "To monitor the usage of our service", "howWeUseLi7": "To detect, prevent and address technical issues", "howWeUseLi8": "To process payments and prevent fraudulent transactions", "howWeUseLi9": "To contact you with newsletters, marketing or promotional materials and other information", "howWeUseTitle": "How We Use Your Information", "infoCollectedIntro": "We may collect information about you in various ways, including:", "infoCollectedTitle": "Information We Collect", "intro": "At Booking Parents, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.", "lastUpdated": "Last updated:", "paymentDataDesc": "We collect payment information when you make purchases through our platform, though payment card details are not stored on our servers.", "paymentDataTitle": "Payment Data:", "personalDataDesc": "While using our service, we may ask you to provide personally identifiable information that can be used to contact or identify you, including name, email address, phone number, and postal address.", "personalDataTitle": "Personal Data:", "rightsAccessDesc": "You have the right to request copies of your personal information.", "rightsAccessTitle": "Right to Access:", "rightsContact": "To exercise any of these rights, please contact <NAME_EMAIL>.", "rightsErasureDesc": "You have the right to request that we delete your personal information.", "rightsErasureTitle": "Right to Erasure:", "rightsIntro": "Depending on your location, you may have certain rights regarding your personal information:", "rightsObjectDesc": "You have the right to object to our processing of your personal information.", "rightsObjectTitle": "Right to Object:", "rightsPortabilityDesc": "You have the right to request that we transfer your information to another organization or directly to you.", "rightsPortabilityTitle": "Right to Data Portability:", "rightsRectificationDesc": "You have the right to request that we correct inaccurate information about you.", "rightsRectificationTitle": "Right to Rectification:", "rightsRestrictDesc": "You have the right to request that we restrict the processing of your information.", "rightsRestrictTitle": "Right to Restrict Processing:", "rightsTitle": "Your Privacy Rights", "securityIntro1": "The security of your data is important to us but remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.", "securityIntro2": "Our security measures include:", "securityLi1": "Encryption of sensitive data in transit and at rest", "securityLi2": "Regular security assessments and audits", "securityLi3": "Employee training on data protection", "securityLi4": "Access controls and authentication requirements", "securityLi5": "Physical security measures for our facilities", "securityTitle": "Data Security", "sharingBusinessTransfersDesc": "In connection with any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company.", "sharingBusinessTransfersTitle": "Business Transfers:", "sharingConsentDesc": "We may disclose your personal information for any other purpose with your consent.", "sharingConsentTitle": "With Your Consent:", "sharingIntro": "We may share your personal information in the following situations:", "sharingLegalDesc": "If required to do so by law or in response to valid requests by public authorities.", "sharingLegalTitle": "Legal Requirements:", "sharingServiceProvidersDesc": "We may share your information with third-party service providers to facilitate our Service, provide the Service on our behalf, or perform service-related services.", "sharingServiceProvidersTitle": "With Service Providers:", "sharingStudiosDesc": "We share necessary information with learning studios to facilitate your bookings and learning sessions.", "sharingStudiosTitle": "With Learning Studios:", "sharingTitle": "Information Sharing and Disclosure", "title": "Privacy Policy", "usageDataDesc": "Information on how the Service is accessed and used, including your computer's Internet Protocol address, browser type, pages visited, time spent on those pages, and other diagnostic data.", "usageDataTitle": "Usage Data:"}, "settingsModal": {"appearanceSectionTitle": "Appearance", "bookingRemindersLabel": "Booking Reminders", "cancelButton": "Cancel", "changePasswordButton": "Change Password", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Dark Mode", "editProfileButton": "Edit Profile", "enableNotificationsLabel": "Enable Notifications", "fontSizeLabel": "Font Size", "highContrastLabel": "High Contrast", "langArabic": "Arabic", "langChinese": "Chinese", "langDutch": "Dutch", "langEnglish": "English", "langFrench": "French", "langGerman": "German", "langHebrew": "Hebrew", "langHindi": "Hindi", "langJapanese": "Japanese", "langSpanish": "Spanish", "languageLabel": "Language", "lowBudgetAlertsLabel": "Low Budget Alerts", "marketingUpdatesLabel": "Marketing Updates", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Preferences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Your Name", "profileSectionTitle": "Profile", "saveButton": "Save Changes", "timezoneLabel": "Time Zone", "title": "Settings", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biology", "Business Studies": "Business Studies", "Chemistry": "Chemistry", "Computer Science": "Computer Science", "Design and Technology": "Design and Technology", "Drama": "Drama", "Economics": "Economics", "English": "English", "Foreign Languages": "Foreign Languages", "Geography": "Geography", "History": "History", "Mathematics": "Mathematics", "Music": "Music", "Philosophy": "Philosophy", "Physical Education": "Physical Education", "Physics": "Physics", "Psychology": "Psychology", "Religious Studies": "Religious Studies", "Social Studies": "Social Studies"}, "purchases": {"purchaseHistory": "Purchase History", "viewAndManage": "View and manage all purchases and bookings", "downloadReport": "Download Report", "totalSpent": "Total Spent", "allCompletedPurchases": "All completed purchases", "totalPurchases": "Total Purchases", "acrossAllChildren": "Across all children", "pending": "Pending", "awaitingConfirmation": "Awaiting confirmation", "transactions": "Transactions", "all": "All", "completed": "Completed", "failed": "Failed", "id": "ID", "product": "Product", "child": "Child", "date": "Date", "amount": "Amount", "status": "Status", "action": "Action", "viewDetails": "View Details", "noPurchasesFound": "No purchases found", "noPurchasesYet": "You haven't made any purchases yet. Browse our products to get started.", "noPurchasesFiltered": "You don't have any {filter} purchases at the moment.", "selectChild": "Select Child", "allChildren": "All Children", "filterByChild": "Filter by child", "purchaseDetails": "Purchase Details", "actions": "Actions", "markAsCompleted": "<PERSON> as Completed", "markAsFailed": "<PERSON> as Failed"}, "learningDocumentation": {"pageTitle": "Learning Documentation", "timeframeAllTime": "All Time", "timeframePastMonth": "Past Month", "timeframePastQuarter": "Past 3 Months", "timeframePastYear": "Past Year", "exportReportButton": "Export Report", "selectChildTitle": "Select Child", "allChildrenButton": "All Children", "overallProgressTitle": "Overall Progress", "progressTowardMastery": "Progress Toward Mastery", "recentAchievementsTitle": "Recent Achievements", "viewFullTimelineButton": "View Full Timeline", "skillsOverviewTitle": "Skills Overview", "skillsSummaryText": "Summary of mastered and in-progress skills will go here.", "viewAllSkillsButton": "View All Skills", "certificatesTitle": "Certificates", "certificatesSummaryText": "Summary of earned certificates will go here.", "viewAllCertificatesButton": "View All Certificates", "documentationTitle": "Documentation Overview", "generalNotesLabel": "General Notes", "learningGoalsLabel": "Learning Goals", "strengthsLabel": "Strengths", "areasForImprovementLabel": "Areas for Improvement", "noDocumentationMessage": "No documentation has been added for this child yet.", "eventTypes": {"achievement": "Achievement", "milestone": "Milestone", "assessment": "Assessment", "test": "Test", "exam": "Exam", "report": "Report", "presentation": "Presentation", "project": "Project", "workshop": "Workshop", "other": "Other"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "lastUpdatedPrefix": "Last updated", "loadingMessage": "Loading..."}, "Timeline": {"pageTitle": "Learning Timeline", "timeframeAllTime": "All Time", "timeframePastMonth": "Past Month", "timeframePastQuarter": "Past 3 Months", "timeframePastYear": "Past Year", "exportTimelineButton": "Export Timeline", "selectChildTitle": "Select Child", "allChildrenButton": "All Children", "filterByEventTypeTitle": "Filter by Event Type", "allEventsButton": "All Events", "learningEventsTitle": "Learning Events", "skillProgressLabel": "Skill Progress", "relatedSkillsLabel": "Related Skills", "achievementLabel": "Achievement", "noEventsMatchMessage": "No events match your current filters.", "noEventsYetMessage": "No learning events have been recorded for this child yet.", "eventsCreatedInAssistantsMessage": "Events will appear here once they are created in the assistants app.", "resetFiltersButton": "Reset filters", "loading": "Loading events...", "error": "Error loading events. Please try again."}, "Certificates": {"pageTitle": "Certificates", "certificatesTitle": "Certificates & Achievements", "certificatesDescription": "View certificates earned by your child", "selectChildTitle": "Select Child", "allChildrenButton": "All Children", "downloadAllButton": "Download All", "certificateLabel": "Certificate", "awardedToText": "Awarded to", "viewDetailsButton": "View Details", "downloadButton": "Download", "loading": "Loading certificates...", "error": "Error loading certificates. Please try again.", "noCertificatesFound": "No certificates found for this child.", "noCertificatesMessage": "No certificates have been added for this child yet.", "certificateDetailsTitle": "Certificate Details", "certificateImageLabel": "Certificate Image", "closeButton": "Close", "imageLoadError": "Image could not be loaded", "titleLabel": "Certificate Title", "issueDateLabel": "Issue Date", "issueDateISOLabel": "Issue Date (ISO)", "relatedSkillsLabel": "Related Skills", "certificateIdLabel": "Certificate ID"}, "eventTypes": {"achievement": "Achievement", "milestone": "Milestone", "assessment": "Assessment", "test": "Test", "exam": "Exam", "report": "Report", "presentation": "Presentation", "project": "Project", "workshop": "Workshop", "other": "Other"}}}