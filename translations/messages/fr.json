{"common": {"appName": "App Parents", "betaTag": "BÊTA", "home": "Accueil", "logout": "Déconnexion", "shop": "Boutique"}, "countries": {"Argentina": "Argentine", "Australia": "Australie", "Austria": "<PERSON><PERSON><PERSON>", "Belgium": "Belgique", "Brazil": "Brésil", "Bulgaria": "Bulgarie", "Canada": "Canada", "Chile": "<PERSON><PERSON>", "China": "<PERSON>e", "Colombia": "<PERSON><PERSON><PERSON>", "Costa Rica": "Costa Rica", "Denmark": "Danemark", "Finland": "<PERSON><PERSON>", "France": "France", "Germany": "Allemagne", "India": "Inde", "Italy": "Italie", "Japan": "Japon", "Luxembourg": "Luxembourg", "Mexico": "Mexique", "Netherlands": "Pays-Bas", "New Zealand": "Nouvelle-Zélande", "Norway": "Norvège", "Russia": "<PERSON><PERSON>", "Singapore": "Singapour", "South Africa": "Afrique du Sud", "South Korea": "Corée du Sud", "Spain": "<PERSON><PERSON><PERSON><PERSON>", "Sweden": "<PERSON><PERSON><PERSON>", "Switzerland": "Suisse", "United Kingdom": "Royaume-Uni", "United States": "États-Unis", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Tous droits réservés.", "footerImprint": "Mentions légales", "footerPrivacy": "Confidentialité", "navDashboard": "Tableau de bord", "navLearningProgress": "<PERSON><PERSON><PERSON><PERSON> d'apprentissage", "navLogout": "Déconnexion", "navPurchases": "Achats", "navShop": "Boutique", "navStudios": "Studios"}, "dashboardPage": {"addChildBookingPermissionLabel": "Autoriser l'enfant à réserver des sessions ?", "addChildBudgetLabel": "Budget initial (€)", "addChildBudgetPlaceholder": "ex: 50.00", "addChildButton": "Ajouter un nouvel enfant", "addChildCancelButton": "Annuler", "addChildCardDesc": "<PERSON><PERSON><PERSON> un nouveau compte enfant à gérer", "addChildModalTitle": "Ajouter un nouvel enfant", "addChildNameLabel": "Nom de l'enfant", "addChildPasswordLabel": "Mot de passe de l'enfant", "addChildPasswordPlaceholder": "<PERSON><PERSON><PERSON> un mot de passe", "addChildSubmitButton": "A<PERSON><PERSON> l'enfant", "addChildUsernameLabel": "Nom d'utilisateur de l'enfant", "addChildUsernamePlaceholder": "<PERSON><PERSON><PERSON> un nom d'utilisateur unique", "addFirstChildButton": "Ajouter votre premier enfant", "addFundsAmountLabel": "Montant (€)", "addFundsButton": "Ajouter des fonds", "addFundsCancelButton": "Annuler", "addFundsHelpText": "Entrez le montant que vous souhaitez ajouter au budget de l'enfant. Les fonds seront disponibles immédiatement.", "addFundsModalTitle": "Ajouter des fonds au compte de {0}", "addFundsSubmitButton": "Ajouter des fonds", "bookingAlertEventAdded": "Nouvel événement \"{0}\" ajouté !", "bookingAlertEventMoved": "{0} a été déplacé vers {1}", "bookingAlertEventResized": "{0} a été redimensionné pour se terminer à {1}", "bookingAlertSelectionCancelled": "Sélection annulée.", "bookingConfirmViewDetails": "Voir les détails pour \"{0}\" ? \nDescription : {1}", "bookingConfirmation": {"availability": "Disponibilité", "available": "Disponible", "biweekly": "Bimensuel", "bookingConflictDetailed": "Conflit de réservation détecté pour {childName} dans {roomName}. L'horaire demandé ({requestedTime}) est en conflit avec une réservation existante ({conflictingTime}). Veuillez sélectionner un autre créneau horaire.", "bookingCost": "Coût de Réservation", "bookingDetails": "Détails de la Réservation", "bookingFailed": "Échec de la Réservation", "budgetInformation": "Informations Budgétaires", "cancelButton": "Annuler", "checking": "Vérification...", "child": "<PERSON><PERSON>", "confirmButton": "Confirmer la Réservation", "currentBudget": "Budget Actuel", "date": "Date", "deleteSuccessDescription": "Votre réservation a été supprimée.", "deleteSuccessMessage": "Réservation supprimée avec succès !", "duration": "<PERSON><PERSON><PERSON>", "enableRecurringBooking": "Activer la Réservation Récurrente", "error": "<PERSON><PERSON><PERSON>", "errorLoadingBudget": "Erreur lors du chargement des informations budgétaires", "friday": "<PERSON><PERSON><PERSON><PERSON>", "hours": "heures", "insufficientBudget": "Budget Insuffisant", "insufficientBudgetMessage": "L'enfant n'a pas assez de budget pour cette réservation. Veuillez ajouter des fonds ou sélectionner un créneau plus court.", "monday": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "none": "Aucun", "notAvailable": "Non Disponible", "numberOfBookings": "Nombre de Réservations", "numberOfOccurrences": "Nombre d'Occurrences", "pricePerHour": "Prix par heure", "processing": "Traitement en cours...", "recurrenceType": "Type de Récurrence", "recurringBooking": "Réservation Récurrente", "remainingBudget": "Budget Restant", "room": "Salle", "saturday": "<PERSON><PERSON>", "selectDaysOfWeek": "Sélectionner les Jours de la Semaine", "selectType": "Sélectionner le Type", "singleBookingCost": "Coût par Réservation", "studio": "Studio", "successDescription": "Votre réservation a été confirmée.", "successMessage": "Réservation Réussie !", "sunday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "timeSlotBooked": "Ce créneau horaire est déjà réservé. Veuillez en sélectionner un autre.", "title": "Confirmer la Réservation", "totalPrice": "Prix Total", "tryAgainMessage": "Veuillez essayer de sélectionner un créneau horaire différent ou contacter le support si le problème persiste.", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "weekly": "Hebdomadaire", "weeklyRecurrenceInfo": "La réservation se répétera les jours sélectionnés pendant le nombre de semaines spécifié.", "outsideOpeningHours": "Les réservations ne sont possibles que pendant les heures d'ouverture du studio.", "recurringBookingCapacityExceeded": "Une ou plusieurs des réservations récurrentes dépasseraient la capacité de la salle. Veuillez réduire le nombre d'occurrences ou choisir un autre créneau horaire.", "biweeklyRecurrenceInfo": "La réservation se répétera toutes les deux semaines le même jour pour le nombre spécifié d'occurrences.", "limitedByBudget": "Limité par le budget", "limitedByCapacity": "Limité par l'espace disponible"}, "bookingDescriptionNone": "Aucune", "bookingModalTitle": "Réserver une session pour {0}", "bookingPromptEventTitle": "Entrez un titre pour votre événement :", "childBookAction": "<PERSON><PERSON><PERSON> rendez-vous", "childBookDisabled": "Réservation désactivée", "childBudgetAvailable": "Disponible", "childBudgetEmpty": "Vide", "childCardAddFundsButton": "Ajouter des fonds", "childCardBookButton": "Réserver une session", "childCardBookingLabel": "Réservation autorisée :", "childCardBudgetLabel": "Budget :", "childCardDeleteButton": "<PERSON><PERSON><PERSON><PERSON>", "childCardEditButton": "Modifier", "childCardNameLabel": "Nom :", "childCardUsernameLabel": "Nom d'utilisateur :", "childStatusCanBook": "L'enfant peut réserver", "childStatusNoBooking": "Réservation par les parents uniquement", "childrenCardDescPlural": "{count, plural, one {# enfant géré} other {# enfants gérés}}", "childrenCardDescSingular": "1 compte enfant actif", "childrenCardTitle": "<PERSON><PERSON><PERSON>", "classLabel": "Classe/Niveau", "countryLabel": "Pays", "editChildAllowBookingsDescription": "<PERSON>, l'enfant peut se connecter et réserver des sessions en utilisant son budget. <PERSON> d<PERSON><PERSON>, seuls les parents peuvent réserver des rendez-vous pour cet enfant. Les parents peuvent toujours réserver des rendez-vous pour leurs enfants, indépendamment de ce paramètre.", "editChildAllowBookingsLabel": "Autoriser l'enfant à réserver des sessions ?", "editChildCancelButtonText": "Annuler", "editChildNameLabel": "Nom de l'enfant", "editChildNamePlaceholder": "Entrez le nom de l'enfant", "editChildPasswordHelpText": "Entrez uniquement si vous souhaitez changer le mot de passe de l'enfant.", "editChildPasswordLabelOptional": "Nouveau mot de passe (facultatif)", "editChildPasswordPlaceholderOptional": "Laisser vide pour conserver l'actuel", "editChildSaveButtonText": "Enregistrer les modifications", "editChildTitle": "Modifier les détails de l'enfant", "editChildUpdateErrorMessage": "Erreur lors de la mise à jour des détails de l'enfant. Veuillez réessayer.", "editChildUpdateSuccessMessage": "Détails de l'enfant mis à jour avec succès !", "editChildUpdatingButtonText": "Mise à jour...", "languageLabel": "<PERSON><PERSON>", "managePrompt": "<PERSON><PERSON><PERSON> les comptes de vos enfants et réservez des sessions d'apprentissage", "myChildrenTitle": "<PERSON><PERSON>", "noChildrenFoundDesc": "Utilisez le formulaire ci-dessous pour ajouter votre premier compte enfant et commencer à gérer les réservations", "noChildrenFoundTitle": "<PERSON><PERSON><PERSON> enfant trouvé", "paymentHistoryButton": "Historique des paiements", "paymentHistoryCloseButton": "<PERSON><PERSON><PERSON>", "paymentHistoryExportBills": "Télécharger toutes les factures", "paymentHistoryExportButton": "Options d'exportation", "paymentHistoryExportCsv": "Exporter en CSV", "paymentHistoryFilterAll": "Toutes les transactions", "paymentHistoryFilterDeposits": "Dépôts uniquement", "paymentHistoryFilterWithdrawals": "Retraits uniquement", "paymentHistoryModalTitle": "Historique des paiements", "paymentHistorySearchPlaceholder": "Rechercher...", "paymentHistorySummaryDeposits": "Total des dépôts", "paymentHistorySummaryNet": "Variation nette du solde", "paymentHistorySummaryWithdrawals": "Total des retraits", "paymentHistoryTableActions": "Actions", "paymentHistoryTableAmount": "Montant (€)", "paymentHistoryTableChild": "<PERSON><PERSON>", "paymentHistoryTableDate": "Date", "paymentHistoryTableDesc": "Description", "paymentHistoryTableType": "Type", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "Retrait", "preferredStudioDesc": "Ce studio sera présélectionné lors de la réservation d'activités", "preferredStudioTitle": "Studio d'apprentissage préféré", "regionDisabledText": "Région non applicable pour le pays sélectionné", "regionLabel": "Région", "schoolTypeLabel": "Type d'École", "selectChildCurrentBudgetLabel": "Budget actuel :", "selectChildFundsCancelButton": "Annuler", "selectChildFundsModalTitle": "Sélectionner l'enfant pour ajouter des fonds", "selectChildFundsPrompt": "Choisissez le compte enfant à recharger :", "selectCountryFirst": "Sélectionnez d'abord un pays", "selectPlaceholder": "Sélectionner...", "selectStudioPlaceholder": "-- Sélectionnez un studio --", "statusAddChildError": "Erreur lors de l'ajout de l'enfant : {0}", "statusAddChildSuccess": "<PERSON><PERSON> a<PERSON> avec succès", "statusAddFundsError": "Erreur lors de l'ajout de fonds : {0}", "statusAddFundsSuccess": "€{0} ajoutés avec succès au compte de {1}", "statusAuthRequired": "Authentification requise.", "statusDeleteChildConfirm": "Êtes-vous sûr de vouloir supprimer l'enfant ID {0} ? Cette action est irréversible.", "statusDeleteChildError": "Erreur lors de la suppression de l'enfant : {0}", "statusDeleteChildSuccess": "Enfant supprimé avec succès", "statusErrorChildren": "Erreur lors de la récupération des enfants", "statusErrorInitialData": "Erreur lors de la récupération des données initiales : {0}", "statusErrorPreferredStudio": "Erreur lors de la récupération du studio préféré", "statusErrorStudios": "Erreur lors de la récupération des studios", "statusFindChildError": "Impossible de trouver les détails de l'enfant.", "statusLoading": "Chargement...", "statusUnexpectedError": "Une erreur inattendue s'est produite.", "statusUpdateChildSuccess": "<PERSON><PERSON><PERSON> de l'enfant mis à jour avec succès.", "statusUpdatePreferredError": "Erreur lors de la mise à jour du studio préféré : {0}", "statusUpdatePreferredSuccess": "Studio préféré mis à jour avec succès", "subjectsLabel": "<PERSON><PERSON><PERSON>", "totalBudgetCardTitle": "Budget total", "welcomeMessage": "Bienvenue"}, "helpModal": {"bookingProcessIntro": "Pour réserver une session d'apprentissage :", "bookingProcessLi1": "Sélectionnez un enfant depuis le tableau de bord", "bookingProcessLi2": "Cliquez sur l'icône du calendrier pour ouvrir le modal de réservation", "bookingProcessLi3": "Choisissez une date, une heure et un type d'activité", "bookingProcessLi4": "Confirmez la réservation (le budget sera automatiquement déduit)", "bookingProcessLi5": "<PERSON><PERSON><PERSON> toutes les réservations dans la section Achats", "bookingProcessTitle": "Processus de réservation", "contactSupportButton": "<PERSON>er le support", "faq1Answer": "Utilisez le bouton \"Ajouter des fonds\" dans le tableau de bord pour recharger le budget d'apprentissage de votre enfant. Vous pouvez utiliser une carte de crédit, PayPal ou un virement bancaire.", "faq1Question": "Comment ajouter des fonds au compte de mon enfant ?", "faq2Answer": "<PERSON><PERSON>. <PERSON><PERSON> de la création d'un compte enfant, vous configurez un nom d'utilisateur et un mot de passe. Votre enfant peut utiliser ces identifiants pour accéder à son propre tableau de bord limité.", "faq2Question": "Mon enfant peut-il se connecter séparément ?", "faq3Answer": "Accédez à la page A<PERSON>ts, trouvez la réservation que vous souhaitez annuler et cliquez sur \"Voir les détails\". Vous trouverez une option d'annulation si la session est dans plus de 24 heures.", "faq3Question": "Comment annuler une réservation ?", "faqTitle": "Questions fréquemment posées", "gettingStartedLi1": "<PERSON><PERSON><PERSON>z vos enfants en utilisant le bouton \"Ajouter un nouvel enfant\"", "gettingStartedLi2": "Définissez votre studio d'apprentissage préféré dans le tableau de bord", "gettingStartedLi3": "<PERSON><PERSON><PERSON> le budget de chaque enfant pour les activités", "gettingStartedLi4": "Réservez des sessions d'apprentissage et suivez les progrès", "gettingStartedTitle": "Pour commencer", "gettingStartedWelcome": "Bienvenue dans l'application Booking Parents ! Cette plateforme vous aide à gérer les activités d'apprentissage et les réservations de vos enfants en un seul endroit.", "managingChildrenAddBudget": "Ajouter un budget", "managingChildrenAddBudgetDesc": ": Recharger le budget d'apprentissage de votre enfant", "managingChildrenBook": "Réserver", "managingChildrenBookDesc": ": Planifier des sessions d'apprentissage dans votre studio préféré", "managingChildrenDelete": "<PERSON><PERSON><PERSON><PERSON>", "managingChildrenDeleteDesc": ": Supprimer un compte enfant (irréversible)", "managingChildrenEdit": "Modifier", "managingChildrenEditDesc": ": Mettre à jour le nom, le budget et les autorisations de réservation", "managingChildrenIntro": "<PERSON>ur chaque enfant, vous pouvez :", "managingChildrenTitle": "Gestion des enfants", "needMoreHelpEmail": "Email :", "needMoreHelpHours": "Horaires : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 9h-17h CET", "needMoreHelpIntro": "Si vous avez besoin d'une assistance supplémentaire, notre équipe de support est là pour vous aider :", "needMoreHelpPhone": "Téléphone :", "needMoreHelpTitle": "Besoin d'aide supplémentaire ?", "title": "Centre d'aide"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "<PERSON><PERSON><PERSON>", "companyInfoTitle": "Informations sur l'entreprise", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Allemagne", "directorsLabel": "Directeurs généraux :", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Malgré un contrôle minutieux du contenu, nous n'assumons aucune responsabilité pour le contenu des liens externes. Les exploitants des pages liées sont seuls responsables de leur contenu.", "disclaimerTitle": "Clause de non-responsabilité", "emailLabel": "Email :", "legalTitle": "Légal", "phoneLabel": "Téléphone :", "regNumLabel": "Numéro d'enregistrement :", "regNumValue": "HRB 123456", "registerLabel": "Registre du commerce :", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Informations réglementaires", "responsibleLabel": "Responsable du contenu selon § 55 Abs. 2 RStV :", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Mentions légales", "vatIdLabel": "N° TVA :", "vatIdValue": "DE123456789", "webLabel": "Web :"}, "languages": {"ar": "<PERSON><PERSON>", "bn": "Bengali", "cs": "Tchèque", "da": "<PERSON><PERSON>", "de": "Allemand", "el": "Grec", "en": "<PERSON><PERSON><PERSON>", "es": "Espagnol", "fi": "<PERSON><PERSON>", "fr": "Français", "gu": "Gujarati", "he": "<PERSON><PERSON><PERSON><PERSON>", "hi": "Hindi", "hu": "Hongrois", "it": "Italien", "ja": "Japonais", "jv": "Javanais", "ko": "<PERSON><PERSON><PERSON>", "mr": "Marathi", "nl": "Néerlandais", "no": "Norvégien", "pa": "<PERSON><PERSON><PERSON><PERSON>", "pl": "Polonais", "pt": "Portugais", "ro": "<PERSON><PERSON><PERSON><PERSON>", "ru": "<PERSON><PERSON>", "sv": "<PERSON><PERSON><PERSON><PERSON>", "ta": "Tamoul", "te": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "th": "<PERSON><PERSON><PERSON>", "tr": "<PERSON><PERSON>", "uk": "<PERSON><PERSON><PERSON><PERSON>", "ur": "<PERSON><PERSON><PERSON>", "vi": "<PERSON><PERSON>", "zh": "<PERSON><PERSON>"}, "login": {"childProgress": "<PERSON><PERSON><PERSON><PERSON> l'Enfant", "easyScheduling": "Planification Facile", "errors": {"connectionError": "Erreur de connexion, veuillez vérifier votre internet", "invalidCredentials": "Nom d'utilisateur ou mot de passe invalide", "licenseValidationFailed": "Échec de la validation de la licence", "loginFailed": "Échec de la connexion", "noActiveLicense": "Aucune licence active trouvée", "serverError": "<PERSON><PERSON><PERSON> serveur, ve<PERSON><PERSON><PERSON> réessayer plus tard", "sessionExpired": "Session expirée, ve<PERSON><PERSON>z vous reconnecter", "tryAgainLater": "Une erreur s'est produite, veuillez réessayer plus tard", "unableToVerifyLicense": "Impossible de vérifier la licence", "userNotFound": "Utilisateur non trouvé", "wrongPassword": "Mot de passe incorrect"}, "forgotPassword": "Mot de passe oublié ?", "languageSwitch": "Changer de langue", "loggingIn": "Connexion en cours...", "login": "Se connecter", "password": "Mot de passe", "summary": "Réservez des sessions d'apprentissage pour votre enfant et suivez ses progrès en un seul endroit.", "title": "Connexion", "username": "Nom d'utilisateur"}, "privacyModal": {"acceptButton": "Je comprends et j'accepte", "childInfoDesc": "Informations sur vos enfants, y compris le nom, l'âge et les préférences d'apprentissage.", "childInfoTitle": "Informations sur l'enfant :", "childrenIntro": "Notre service nécessite des informations sur les enfants pour fournir nos services de réservation principaux. Nous prenons des précautions supplémentaires pour protéger les données des enfants :", "childrenLi1": "Nous collectons uniquement les informations minimales nécessaires sur les enfants", "childrenLi2": "Nous exigeons le consentement parental avant de collecter des informations auprès des enfants", "childrenLi3": "Nous ne rendons pas publiques les informations personnelles des enfants", "childrenLi4": "Les parents peuvent examiner, supprimer ou refuser la collecte ultérieure des informations de leur enfant", "childrenLi5": "Nous mettons en œuvre des mesures de sécurité supplémentaires pour les données des enfants", "childrenTitle": "Confidentialité des enfants", "contactAddress": "<PERSON><PERSON><PERSON> :", "contactEmail": "Email :", "contactIntro": "Si vous avez des questions sur cette politique de confidentialité, veuillez nous contacter :", "contactPhone": "Téléphone :", "contactTitle": "Contactez-nous", "cookiesDesc": "Nous utilisons des cookies et des technologies de suivi similaires pour suivre l'activité sur notre Plateforme et conserver certaines informations.", "cookiesTitle": "Cookies et suivi :", "howWeUseIntro": "Nous pouvons utiliser les informations que nous collectons à votre sujet à diverses fins :", "howWeUseLi1": "Pour fournir et maintenir notre service", "howWeUseLi2": "Pour vous informer des modifications apportées à notre service", "howWeUseLi3": "Pour vous permettre de participer aux fonctionnalités interactives lorsque vous choisissez de le faire", "howWeUseLi4": "Pour fournir un support client", "howWeUseLi5": "Pour recueillir des analyses ou des informations précieuses afin d'améliorer notre service", "howWeUseLi6": "Pour surveiller l'utilisation de notre service", "howWeUseLi7": "Pour dé<PERSON><PERSON>, prévenir et résoudre les problèmes techniques", "howWeUseLi8": "Pour traiter les paiements et prévenir les transactions frauduleuses", "howWeUseLi9": "Pour vous contacter avec des newsletters, des supports marketing ou promotionnels et d'autres informations", "howWeUseTitle": "Comment nous utilisons vos informations", "infoCollectedIntro": "Nous pouvons collecter des informations vous concernant de différentes manières, notamment :", "infoCollectedTitle": "Informations que nous collectons", "intro": "Chez Booking Parents, nous prenons votre vie privée très au sérieux. Cette politique de confidentialité explique comment nous collectons, utilisons, divulguons et protégeons vos informations lorsque vous utilisez notre plateforme. Veuillez lire attentivement cette politique de confidentialité. Si vous n'êtes pas d'accord avec les termes de cette politique de confidentialité, veuillez ne pas accéder à l'application.", "lastUpdated": "Dernière mise à jour :", "paymentDataDesc": "Nous collectons des informations de paiement lorsque vous effectuez des achats via notre plateforme, bien que les détails de la carte de paiement ne soient pas stockés sur nos serveurs.", "paymentDataTitle": "Données de paiement :", "personalDataDesc": "Lors de l'utilisation de notre service, nous pouvons vous demander de fournir des informations personnellement identifiables qui peuvent être utilisées pour vous contacter ou vous identifier, y compris le nom, l'adresse e-mail, le numéro de téléphone et l'adresse postale.", "personalDataTitle": "Données <PERSON>les :", "rightsAccessDesc": "Vous avez le droit de demander des copies de vos informations personnelles.", "rightsAccessTitle": "<PERSON><PERSON> d'a<PERSON> :", "rightsContact": "Pour exercer l'un de ces droits, veuillez nous contacter à <EMAIL>.", "rightsErasureDesc": "Vous avez le droit de demander que nous supprimions vos informations personnelles.", "rightsErasureTitle": "Droit à l'effacement :", "rightsIntro": "Selon votre emplacement, vous pouvez avoir certains droits concernant vos informations personnelles :", "rightsObjectDesc": "Vous avez le droit de vous opposer à notre traitement de vos informations personnelles.", "rightsObjectTitle": "Droit d'opposition :", "rightsPortabilityDesc": "Vous avez le droit de demander que nous transférions vos informations à une autre organisation ou directement à vous.", "rightsPortabilityTitle": "Droit à la portabilité des données :", "rightsRectificationDesc": "Vous avez le droit de demander que nous corrigions les informations inexactes vous concernant.", "rightsRectificationTitle": "Droit de rectification :", "rightsRestrictDesc": "Vous avez le droit de demander que nous limitions le traitement de vos informations.", "rightsRestrictTitle": "Droit de limiter le traitement :", "rightsTitle": "Vos droits en matière de confidentialité", "securityIntro1": "La sécurité de vos données est importante pour nous, mais n'oubliez pas qu'aucune méthode de transmission sur Internet ou méthode de stockage électronique n'est sécurisée à 100 %. Bien que nous nous efforcions d'utiliser des moyens commercialement acceptables pour protéger vos Données personnelles, nous ne pouvons garantir leur sécurité absolue.", "securityIntro2": "Nos mesures de sécurité comprennent :", "securityLi1": "Chiffrement des données sensibles en transit et au repos", "securityLi2": "Évaluations et audits de sécurité réguliers", "securityLi3": "Formation des employés sur la protection des données", "securityLi4": "Contrôles d'accès et exigences d'authentification", "securityLi5": "Mesures de sécurité physique pour nos installations", "securityTitle": "Sécurité des données", "sharingBusinessTransfersDesc": "Dans le cadre de toute fusion, vente d'actifs de l'entreprise, financement ou acquisition de tout ou partie de notre entreprise par une autre société.", "sharingBusinessTransfersTitle": "Transferts d'entreprise :", "sharingConsentDesc": "Nous pouvons divulguer vos informations personnelles à toute autre fin avec votre consentement.", "sharingConsentTitle": "Avec votre consentement :", "sharingIntro": "Nous pouvons partager vos informations personnelles dans les situations suivantes :", "sharingLegalDesc": "Si la loi l'exige ou en réponse à des demandes valides des autorités publiques.", "sharingLegalTitle": "Exigences légales :", "sharingServiceProvidersDesc": "Nous pouvons partager vos informations avec des fournisseurs de services tiers pour faciliter notre Service, fournir le Service en notre nom ou effectuer des services liés au service.", "sharingServiceProvidersTitle": "Avec les fournisseurs de services :", "sharingStudiosDesc": "Nous partageons les informations nécessaires avec les studios d'apprentissage pour faciliter vos réservations et sessions d'apprentissage.", "sharingStudiosTitle": "Avec les studios d'apprentissage :", "sharingTitle": "Partage et divulgation d'informations", "title": "Politique de confidentialité", "usageDataDesc": "Informations sur la manière dont le Service est accédé et utilisé, y compris l'adresse de protocole Internet de votre ordinateur, le type de navigateur, les pages visitées, le temps passé sur ces pages et d'autres données de diagnostic.", "usageDataTitle": "Données d'utilisation :"}, "settingsModal": {"appearanceSectionTitle": "Apparence", "bookingRemindersLabel": "Rappels de réservation", "cancelButton": "Annuler", "changePasswordButton": "Changer le mot de passe", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Mode sombre", "editProfileButton": "Modifier le profil", "enableNotificationsLabel": "Activer les notifications", "fontSizeLabel": "Taille de la police", "highContrastLabel": "Contraste élevé", "langArabic": "<PERSON><PERSON>", "langChinese": "<PERSON><PERSON>", "langDutch": "Néerlandais", "langEnglish": "<PERSON><PERSON><PERSON>", "langFrench": "Français", "langGerman": "Allemand", "langHebrew": "<PERSON><PERSON><PERSON><PERSON>", "langHindi": "Hindi", "langJapanese": "Japonais", "langSpanish": "Espagnol", "languageLabel": "<PERSON><PERSON>", "lowBudgetAlertsLabel": "Alertes de budget faible", "marketingUpdatesLabel": "Mises à jour marketing", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Préférences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Votre Nom", "profileSectionTitle": "Profil", "saveButton": "Enregistrer les modifications", "timezoneLabel": "<PERSON><PERSON> ho<PERSON>", "title": "Paramètres", "tzLondon": "Londres (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biologie", "Business Studies": "Études commerciales", "Chemistry": "<PERSON><PERSON>", "Computer Science": "Informatique", "Design and Technology": "Design et Technologie", "Drama": "Théâtre", "Economics": "Économie", "English": "<PERSON><PERSON><PERSON>", "Foreign Languages": "<PERSON>ues étrang<PERSON>", "Geography": "Géographie", "History": "Histoire", "Mathematics": "Mathématiques", "Music": "Musique", "Philosophy": "Philosophie", "Physical Education": "Éducation physique", "Physics": "Physique", "Psychology": "Psychologie", "Religious Studies": "Études religieuses", "Social Studies": "Études sociales"}, "purchases": {"purchaseHistory": "Historique des Achats", "viewAndManage": "Voir et gérer tous les achats et réservations", "downloadReport": "Télécharger le Rapport", "totalSpent": "Dépenses Totales", "allCompletedPurchases": "Tous les achats terminés", "totalPurchases": "<PERSON><PERSON><PERSON>", "acrossAllChildren": "Pour tous les enfants", "pending": "En attente", "awaitingConfirmation": "En attente de confirmation", "transactions": "Transactions", "all": "Toutes", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "id": "ID", "product": "Produit", "child": "<PERSON><PERSON>", "date": "Date", "amount": "<PERSON><PERSON>", "status": "Statut", "action": "Action", "viewDetails": "Voir les Détails", "noPurchasesFound": "<PERSON><PERSON><PERSON> achat trouvé", "noPurchasesYet": "Vous n'avez encore effectué aucun achat. Parcourez nos produits pour commencer.", "noPurchasesFiltered": "Vous n'avez aucun achat {filter} pour le moment.", "selectChild": "<PERSON><PERSON><PERSON><PERSON><PERSON> un Enfant", "allChildren": "<PERSON><PERSON> les Enfants", "filterByChild": "Filtrer par enfant", "purchaseDetails": "Détails de l'Achat", "actions": "Actions", "markAsCompleted": "Marquer comme Terminé", "markAsFailed": "Marquer comme <PERSON>"}, "learningDocumentation": {"pageTitle": "Documentation d'Apprentissage", "timeframeAllTime": "Tout le temps", "timeframePastMonth": "<PERSON><PERSON>", "timeframePastQuarter": "3 Derniers Mois", "timeframePastYear": "<PERSON><PERSON>", "exportReportButton": "Exporter le Rapport", "selectChildTitle": "Sélectionner l'Enfant", "allChildrenButton": "<PERSON><PERSON> les Enfants", "overallProgressTitle": "Progrès Global", "progressTowardMastery": "Progrès vers la Maîtrise", "recentAchievementsTitle": "Réalisations Récentes", "viewFullTimelineButton": "Voir la Chronologie Complète", "skillsOverviewTitle": "Aperçu des Compétences", "skillsSummaryText": "Un résumé des compétences maîtrisées et en cours ira ici.", "viewAllSkillsButton": "Voir Toutes les Compétences", "certificatesTitle": "Certificats", "certificatesSummaryText": "Un résumé des certificats obtenus ira ici.", "viewAllCertificatesButton": "Voir Tous les Certificats", "documentationTitle": "Aperçu de la Documentation", "generalNotesLabel": "Notes Générales", "learningGoalsLabel": "Objectifs d'Apprentissage", "strengthsLabel": "Points Forts", "areasForImprovementLabel": "Domaines à Améliorer", "noDocumentationMessage": "Aucune documentation n'a encore été ajoutée pour cet enfant.", "eventTypes": {"achievement": "Réussite", "milestone": "Étape importante", "assessment": "Évaluation", "test": "Test", "exam": "Examen", "report": "Rapport", "presentation": "Présentation", "project": "Projet", "workshop": "Atelier", "other": "<PERSON><PERSON>"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Documentation de Progrès", "skillsTitle": "Progrès des Compétences", "skillsDescription": "Voir les progrès sur diverses compétences", "childSelectLabel": "Sélectionner l'Enfant", "skillAreaLabel": "Domaine de Compétence", "skillLabel": "Compétence", "skillLevelLabel": "Niveau de Compétence", "progressLabel": "<PERSON>g<PERSON><PERSON> (%)", "notesLabel": "Notes", "optionalText": "Optionnel", "saveButton": "Enregistrer", "cancelButton": "Annuler", "closeButton": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "-- S<PERSON><PERSON><PERSON>ner --", "successMessage": "Documentation de progrès enregistrée avec succès!", "errorMessage": "Échec de l'enregistrement de la documentation de progrès. Veuillez réessayer.", "errorFetchingHistory": "Erreur lors de la récupération de l'historique des progrès", "existingSkillsTitle": "Enregistrements de Progrès", "selectChildFirst": "Veuillez d'abord sélectionner un enfant", "loadingProgressMessage": "Chargement des progrès...", "noExistingSkillsMessage": "Aucune documentation de progrès trouvée pour cet enfant", "selectSkillAreaFirst": "Veuillez d'abord sélectionner un domaine de compétence", "noSkillLevelsMessage": "Aucun niveau de compétence disponible", "errorFetchingSkillLevels": "Erreur lors de la récupération des niveaux de compétence", "errorFetchingSkills": "Erreur lors de la récupération des compétences", "errorFetchingProgress": "Erreur lors de la récupération des progrès", "noLevelAssigned": "Aucun niveau <PERSON>", "noSkillAreasMessage": "Aucun domaine de compétence disponible", "historyTitle": "Historique des Progrès", "historyEmpty": "Aucune entrée de progrès antérieure trouvée", "historyDate": "Date", "levelPrefix": "Niveau", "progressPrefix": "Progrès", "notesPrefix": "Notes", "loadingMessage": "Chargement...", "lastUpdatedPrefix": "Dernière mise à jour"}, "Timeline": {"pageTitle": "Chronologie d'Apprentissage", "timeframeAllTime": "<PERSON><PERSON> le Te<PERSON>", "timeframePastMonth": "<PERSON><PERSON>", "timeframePastQuarter": "3 Derniers Mois", "timeframePastYear": "<PERSON><PERSON>", "exportTimelineButton": "Exporter la Chronologie", "selectChildTitle": "Sélectionner l'Enfant", "allChildrenButton": "<PERSON><PERSON> les Enfants", "filterByEventTypeTitle": "Filtrer par Type d'Événement", "allEventsButton": "Tous les Événements", "learningEventsTitle": "Événements d'Apprentissage", "skillProgressLabel": "Progrès des Compétences", "relatedSkillsLabel": "Compétences Associées", "achievementLabel": "Réussite", "noEventsMatchMessage": "Aucun événement ne correspond à vos filtres actuels.", "noEventsYetMessage": "Aucun événement d'apprentissage n'a été enregistré pour cet enfant.", "eventsCreatedInAssistantsMessage": "Les événements apparaîtront ici une fois qu'ils seront créés dans l'application d'assistants.", "resetFiltersButton": "Réinitialiser les filtres", "loading": "Chargement des événements...", "error": "Erreur lors du chargement des événements. Veuillez réessayer."}, "Certificates": {"pageTitle": "Certificats", "certificatesTitle": "Certificats et Réussites", "certificatesDescription": "Voir les certificats obtenus par votre enfant", "selectChildTitle": "Sélectionner l'Enfant", "allChildrenButton": "<PERSON><PERSON> les Enfants", "downloadAllButton": "<PERSON><PERSON>", "certificateLabel": "Certificat", "awardedToText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewDetailsButton": "Voir les Détails", "downloadButton": "Télécharger", "loading": "Chargement des certificats...", "error": "Erreur lors du chargement des certificats. Veuillez réessayer.", "noCertificatesFound": "Aucun certificat trouvé pour cet enfant.", "noCertificatesMessage": "Aucun certificat n'a encore été ajouté pour cet enfant.", "certificateDetailsTitle": "Détails du Certificat", "certificateImageLabel": "Image du Certificat", "closeButton": "<PERSON><PERSON><PERSON>", "imageLoadError": "Impossible de charger l'image", "titleLabel": "Titre du Certificat", "issueDateLabel": "Date d'Émission", "issueDateISOLabel": "Date d'Émission (ISO)", "relatedSkillsLabel": "Compétences Associées", "certificateIdLabel": "ID du Certificat"}, "eventTypes": {"achievement": "Réussite", "milestone": "Étape importante", "assessment": "Évaluation", "test": "Test", "exam": "Examen", "report": "Rapport", "presentation": "Présentation", "project": "Projet", "workshop": "Atelier", "other": "<PERSON><PERSON>"}}}