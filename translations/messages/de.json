{"common": {"appName": "Eltern App", "betaTag": "BETA", "home": "Startseite", "logout": "Abmelden", "shop": "Shop"}, "countries": {"Argentina": "Argentinien", "Australia": "Australien", "Austria": "Österreich", "Belgium": "Belgien", "Brazil": "Brasilien", "Bulgaria": "Bulgarien", "Canada": "Ka<PERSON><PERSON>", "Chile": "Chile", "China": "China", "Colombia": "Kolumbien", "Costa Rica": "Costa Rica", "Denmark": "Dänemark", "Finland": "Finnland", "France": "<PERSON><PERSON><PERSON>", "Germany": "Deutschland", "India": "Indien", "Italy": "Italien", "Japan": "Japan", "Luxembourg": "Luxemburg", "Mexico": "Mexiko", "Netherlands": "Niederlande", "New Zealand": "Neuseeland", "Norway": "Norwegen", "Russia": "Russland", "Singapore": "Singapur", "South Africa": "Südafrika", "South Korea": "Südkorea", "Spain": "Spanien", "Sweden": "Schweden", "Switzerland": "Schweiz", "United Kingdom": "Vereinigtes Königreich", "United States": "Vereinigte Staaten", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Alle Rechte vorbehalten.", "footerImprint": "Impressum", "footerPrivacy": "Datenschutz", "help": "<PERSON><PERSON><PERSON>", "navDashboard": "Dashboard", "navLearningProgress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navLogout": "Abmelden", "navPurchases": "Einkäufe", "navShop": "Shop", "navStudios": "Studios", "privacy": "Datenschutz", "selectLanguage": "Sprache auswählen", "settings": "Einstellungen", "toggleMenu": "<PERSON><PERSON>"}, "dashboardPage": {"addChildBookingPermissionLabel": "Kind erlauben, <PERSON><PERSON><PERSON>n zu buchen?", "addChildBudgetLabel": "Anfangsbudget (€)", "addChildBudgetPlaceholder": "z.B. 50.00", "addChildButton": "Neues Kind hinzufügen", "addChildCancelButton": "Abbrechen", "addChildCardDesc": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Kinderkonto zur Verwaltung", "addChildModalTitle": "Neues Kind hinzufügen", "addChildNameLabel": "Name des Kindes", "addChildPasswordLabel": "Passwort des Kindes", "addChildPasswordPlaceholder": "Passwort erstellen", "addChildSubmitButton": "<PERSON> hinzufügen", "addChildUsernameLabel": "Benutzername des Kindes", "addChildUsernamePlaceholder": "Eindeutigen Benutzernamen erstellen", "addFirstChildButton": "<PERSON>rstes Kind hinzufügen", "addFundsAmountLabel": "Betrag (€)", "addFundsButton": "<PERSON>eld hinzufügen", "addFundsCancelButton": "Abbrechen", "addFundsHelpText": "<PERSON><PERSON><PERSON> Si<PERSON> den Betrag ein, den Si<PERSON> dem Budget des Kindes hinzufügen möchten. Das Geld wird sofort verfügbar sein.", "addFundsModalTitle": "Geld zum Konto von {0} hinzufügen", "addFundsSubmitButton": "<PERSON>eld hinzufügen", "bookingAlertEventAdded": "<PERSON><PERSON><PERSON> Ereignis \"{0}\" hinzugefügt!", "bookingAlertEventMoved": "{0} wurde verschoben nach {1}", "bookingAlertEventResized": "{0} wurde in der Größe geä<PERSON>t, um um {1} zu enden", "bookingAlertSelectionCancelled": "Auswahl abgebrochen.", "bookingConfirmViewDetails": "Details für \"{0}\" anzeigen? \nBeschreibung: {1}", "bookingModalTitle": "Sitzung buchen für {0}", "bookingConfirmation": {"availability": "Verfügbarkeit", "available": "Verfügbar", "biweekly": "Zweiwö<PERSON>tl<PERSON>", "bookingConflictDetailed": "Buchungskonflikt erkannt für {childName} in {roomName}. Die angeforderte Zeit ({requestedTime}) überschneidet sich mit einer bestehenden Buchung ({conflictingTime}). Bitte wählen Sie einen anderen Zeitraum.", "bookingCost": "Buchungskosten", "bookingDetails": "Buchungsdetails", "bookingFailed": "Buchung fehlgeschlagen", "budgetInformation": "Budget-Informationen", "cancelButton": "Abbrechen", "checking": "Überprüfung...", "child": "Kind", "confirmButton": "Buchung bestätigen", "currentBudget": "Aktuelles Budget", "date": "Datum", "deleteSuccessDescription": "Ihre Buchung wurde gelöscht.", "deleteSuccessMessage": "Buchung erfolgreich gelöscht!", "duration": "<PERSON><PERSON>", "enableRecurringBooking": "Wiederkehrende Buchung aktivieren", "error": "<PERSON><PERSON>", "errorLoadingBudget": "Fehler beim Laden der Budget-Informationen", "friday": "Freitag", "hours": "Stunden", "insufficientBudget": "Unzureichendes Budget", "insufficientBudgetMessage": "Das Kind hat nicht genügend Budget für diese Buchung. Bitte fügen Sie Geld hinzu oder wählen Sie einen kürzeren Zeitraum.", "monday": "Montag", "monthly": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON>", "notAvailable": "Nicht verfügbar", "numberOfBookings": "Anzahl der Buchungen", "numberOfOccurrences": "Anzahl der Wiederholungen", "pricePerHour": "Preis pro Stunde", "processing": "Verarbeitung...", "recurrenceType": "Wiederholungstyp", "recurringBooking": "Wiederkehrende Buchung", "remainingBudget": "Verbleibendes Budget", "room": "<PERSON><PERSON>", "saturday": "Samstag", "selectDaysOfWeek": "Wochentage auswählen", "selectType": "Typ auswählen", "singleBookingCost": "Kosten pro Buchung", "studio": "Studio", "successDescription": "Ihre Buchung wurde bestätigt.", "successMessage": "Buchung erfolgreich!", "sunday": "Sonntag", "thursday": "Don<PERSON><PERSON>", "time": "Zeit", "timeSlotBooked": "Dieser Zeitraum ist bereits gebucht. Bitte wählen Sie e<PERSON> andere Zeit.", "title": "Buchung bestätigen", "totalPrice": "Gesamtpreis", "tryAgainMessage": "<PERSON>te versuchen Si<PERSON>, einen anderen Zeitraum zu wählen, oder kontaktieren <PERSON> den Support, wenn das Problem weiterhin besteht.", "outsideOpeningHours": "Buchungen sind nur innerhalb der Öffnungszeiten des Studios möglich.", "recurringBookingCapacityExceeded": "Eine oder mehrere der wiederkehrenden Buchungen würden die Raumkapazität überschreiten. Bitte reduzieren Sie die Anzahl der Wiederholungen oder wählen Sie einen anderen Zeitraum.", "biweeklyRecurrenceInfo": "Die Buchung wird alle zwei Wochen am selben Tag für die angegebene Anzahl von Wiederholungen wiederholt.", "limitedByBudget": "Begrenzt durch Budget", "limitedByCapacity": "Begrenzt durch verfügbaren Platz", "tuesday": "Dienstag", "wednesday": "Mittwoch", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weeklyRecurrenceInfo": "Die Buchung wird an den ausgewählten Tagen für die angegebene Anzahl von W<PERSON> wiederholt."}, "bookingDescriptionNone": "<PERSON><PERSON>", "bookingPromptEventTitle": "<PERSON><PERSON><PERSON> Si<PERSON> einen Titel für Ihr Ereignis ein:", "childBookAction": "<PERSON><PERSON><PERSON> buchen", "childBookDisabled": "Buchung deaktiviert", "childBudgetAvailable": "Verfügbar", "childBudgetEmpty": "<PERSON><PERSON>", "childCardAddFundsButton": "<PERSON>eld hinzufügen", "childCardBookButton": "Sitzung buchen", "childCardBookingLabel": "Buchung erlaubt:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Löschen", "childCardEditButton": "<PERSON><PERSON><PERSON>", "childCardNameLabel": "Name:", "childCardUsernameLabel": "Benutzername:", "childStatusCanBook": "<PERSON> kann buchen", "childStatusNoBooking": "Nur Eltern können buchen", "childrenCardDescPlural": "{count, plural, one {# Kind verwaltet} other {# Kinder verwaltet}}", "childrenCardDescSingular": "1 aktives Kinderkonto", "childrenCardTitle": "Kinder", "classLabel": "Klasse/Stufe", "countryLabel": "Land", "editChildAllowBookingsDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, kann sich das Kind anmelden und Sitzungen mit seinem Budget buchen. <PERSON><PERSON>, können nur Eltern Termine für dieses Kind buchen. Eltern können immer Termine für ihre Kinder buchen, unabhä<PERSON><PERSON> von dieser Einstellung.", "editChildAllowBookingsLabel": "Kind erlauben, <PERSON><PERSON><PERSON>n zu buchen?", "editChildCancelButtonText": "Abbrechen", "editChildNameLabel": "Name des Kindes", "editChildNamePlaceholder": "Namen des Kindes eingeben", "editChildPasswordHelpText": "<PERSON><PERSON> e<PERSON>, wenn Sie das Passwort des Kindes ändern möchten.", "editChildPasswordLabelOptional": "Neues Passwort (Optional)", "editChildPasswordPlaceholderOptional": "<PERSON><PERSON>, um aktuell zu behalten", "editChildSaveButtonText": "Änderungen speichern", "editChildTitle": "Kinderdetails bearbeiten", "editChildUpdateErrorMessage": "Fehler beim Aktualisieren der Kinderdetails. Bitte versuchen Sie es erneut.", "editChildUpdateSuccessMessage": "Kinderdetails erfolgreich aktualisiert!", "editChildUpdatingButtonText": "Aktualisiere...", "languageLabel": "<PERSON><PERSON><PERSON>", "managePrompt": "Verwalten Sie die Konten Ihrer Kinder und buchen Sie Lernsitzungen", "myChildrenTitle": "<PERSON><PERSON>", "noChildrenFoundDesc": "Verwenden Sie das Formular unten, um Ihr erstes Kinderkonto hinzuzufügen und mit der Verwaltung von Buchungen zu beginnen", "noChildrenFoundTitle": "<PERSON><PERSON> gefunden", "paymentHistoryButton": "Zahlungsverlauf", "paymentHistoryCloseButton": "Schließen", "paymentHistoryExportBills": "Alle Rechnungen herunterladen", "paymentHistoryExportButton": "Exportoptionen", "paymentHistoryExportCsv": "Als CSV exportieren", "paymentHistoryFilterAll": "Alle Transaktionen", "paymentHistoryFilterDeposits": "Nur Einzahlungen", "paymentHistoryFilterWithdrawals": "<PERSON><PERSON>", "paymentHistoryModalTitle": "Zahlungsverlauf", "paymentHistorySearchPlaceholder": "Suchen...", "paymentHistorySummaryDeposits": "Gesamteinzahlungen", "paymentHistorySummaryNet": "Nettosaldoänderung", "paymentHistorySummaryWithdrawals": "Gesamtauszahlungen", "paymentHistoryTableActions": "Aktionen", "paymentHistoryTableAmount": "Betrag (€)", "paymentHistoryTableChild": "Kind", "paymentHistoryTableDate": "Datum", "paymentHistoryTableDesc": "Beschreibung", "paymentHistoryTableType": "<PERSON><PERSON>", "paymentHistoryTypeDeposit": "Einzahlung", "paymentHistoryTypeWithdrawal": "Auszahlung", "preferredStudioDesc": "Dieses Studio wird bei der Buchung von Aktivitäten vorausgewählt", "preferredStudioTitle": "Bevorzugtes Lernstudio", "regionDisabledText": "<PERSON>cht zutreffend für dieses Land", "regionLabel": "Region", "schoolTypeLabel": "Schultyp", "selectChildCurrentBudgetLabel": "Aktuelles Budget:", "selectChildFundsCancelButton": "Abbrechen", "selectChildFundsModalTitle": "Kind zum Geld hinzufügen auswählen", "selectChildFundsPrompt": "Wählen Sie das Konto des Kindes zum Aufladen:", "selectCountryFirst": "Zuerst Land auswählen", "selectPlaceholder": "Auswählen...", "selectStudioPlaceholder": "-- Studio auswählen --", "statusAddChildError": "Fehler beim Hinzufügen des Kindes: {0}", "statusAddChildSuccess": "<PERSON> erfolgreich hinzugefügt", "statusAddFundsError": "<PERSON><PERSON> beim Hi<PERSON>ufü<PERSON>: {0}", "statusAddFundsSuccess": "Erfolgreich €{0} zum Konto von {1} hinzugefügt", "statusAuthRequired": "Authentifizierung erforderlich.", "statusDeleteChildConfirm": "<PERSON>d <PERSON> sic<PERSON>, dass Sie Kind ID {0} löschen möchten? Dies kann nicht rückgängig gemacht werden.", "statusDeleteChildError": "<PERSON><PERSON> beim Löschen des Kindes: {0}", "statusDeleteChildSuccess": "Kind erfolgreich <PERSON>", "statusErrorChildren": "Fehler beim Abrufen der Kinder", "statusErrorInitialData": "Fehler beim Abrufen der Initialdaten: {0}", "statusErrorPreferredStudio": "Fehler beim Abrufen des bevorzugten Studios", "statusErrorStudios": "Fehler beim Abrufen der Studios", "statusFindChildError": "Kinderdetails konnten nicht gefunden werden.", "statusLoading": "Wird geladen...", "statusUnexpectedError": "Ein unerwarteter Fehler ist aufgetreten.", "statusUpdateChildSuccess": "Kinderdetails erfolgreich aktualisiert.", "statusUpdatePreferredError": "Fehler beim Aktualisieren des bevorzugten Studios: {0}", "statusUpdatePreferredSuccess": "Bevorzugtes Studio erfolgreich aktualisiert", "subjectsLabel": "<PERSON><PERSON><PERSON>", "totalBudgetCardTitle": "Gesamtbudget", "welcomeMessage": "<PERSON><PERSON><PERSON><PERSON>"}, "helpModal": {"bookingProcessIntro": "So buchen Sie eine Lernsitzung:", "bookingProcessLi1": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Kind aus dem Dashboard aus", "bookingProcessLi2": "Klicken Sie auf das Kalendersymbol, um das Buchungsmodal zu öffnen", "bookingProcessLi3": "Wählen Sie Datum, Uhrzeit und Aktivitätstyp aus", "bookingProcessLi4": "Bestätigen Sie die Buchung (Budget wird automatisch abgezogen)", "bookingProcessLi5": "Verfolgen Sie alle Buchungen im Bereich \"Einkäufe\"", "bookingProcessTitle": "Buchungsprozess", "contactSupportButton": "Support kontaktieren", "faq1Answer": "Verwenden Sie die Schaltfläche \"Geld hinzufügen\" im Dashboard, um das Lernbudget Ihres Kindes aufzuladen. Sie können Kreditkarte, PayPal oder Banküberweisung verwenden.", "faq1Question": "Wie füge ich Geld zum Konto meines Kindes hinzu?", "faq2Answer": "<PERSON><PERSON><PERSON> <PERSON><PERSON> eines Kinderkontos richten Si<PERSON> einen Benutzernamen und ein Passwort ein. Ihr Kind kann diese Anmeldeinformationen verwenden, um auf sein eigenes eingeschränktes Dashboard zuzugreifen.", "faq2Question": "Kann sich mein Kind separat anmelden?", "faq3Answer": "Navigieren Sie zur Seite \"Einkäufe\", suchen Sie die zu stornierende Buchung und klicken Sie auf \"Details anzeigen\". Sie finden eine Stornierungsoption, wenn die Sitzung mehr als 24 Stunden entfernt ist.", "faq3Question": "Wie storniere ich eine Buchung?", "faqTitle": "Häufig gestellte Fragen", "gettingStartedLi1": "Fügen Sie Ihre Kinder über die Schaltfläche \"Neues Kind hinzufügen\" hinzu", "gettingStartedLi2": "Legen Sie Ihr bevorzugtes Lernstudio im Dashboard fest", "gettingStartedLi3": "Verwalten Sie das Budget jedes Kindes für Aktivitäten", "gettingStartedLi4": "Buchen Sie Lernsitzungen und verfolgen Sie den Fortschritt", "gettingStartedTitle": "<PERSON><PERSON><PERSON>", "gettingStartedWelcome": "Willkommen bei der Booking Parents App! Diese Plattform hilft Ihnen, die Lernaktivitäten und Buchungen Ihrer Kinder an einem Ort zu verwalten.", "managingChildrenAddBudget": "Budget hinzufügen", "managingChildrenAddBudgetDesc": ": <PERSON> Lernbudget Ihres Kindes aufladen", "managingChildrenBook": "Buchen", "managingChildrenBookDesc": ": Lernsitzungen in Ihrem bevorzugten Studio planen", "managingChildrenDelete": "Löschen", "managingChildrenDeleteDesc": ": Kinderkonto entfernen (kann nicht rückgängig gemacht werden)", "managingChildrenEdit": "<PERSON><PERSON><PERSON>", "managingChildrenEditDesc": ": Namen, Budget und Buchungsberechtigungen aktualisieren", "managingChildrenIntro": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> können Sie:", "managingChildrenTitle": "Kinder verwalten", "needMoreHelpEmail": "E-Mail:", "needMoreHelpHours": "Zeiten: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 9-17 Uhr MEZ", "needMoreHelpIntro": "<PERSON>n Sie zusätzliche Unterstützung benötigen, hilft Ihnen unser Support-Team gerne weiter:", "needMoreHelpPhone": "Telefon:", "needMoreHelpTitle": "Benötigen Sie weitere Hilfe?", "title": "Hilfe-Center"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Schließen", "companyInfoTitle": "Firmeninformationen", "companyName": "Booking Parents GmbH", "contactTitle": "Kontakt", "country": "Deutschland", "directorsLabel": "Geschäftsführer:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "<PERSON><PERSON><PERSON> sorgfältiger inhaltlicher Kontrolle übernehmen wir keine Haftung für die Inhalte externer Links. Für den Inhalt der verlinkten Seiten sind ausschließlich deren Betreiber verantwortlich.", "disclaimerTitle": "Haftungsausschluss", "emailLabel": "E-Mail:", "legalTitle": "Rechtliches", "phoneLabel": "Telefon:", "regNumLabel": "Registernummer:", "regNumValue": "HRB 123456", "registerLabel": "Handelsregister:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Aufsichtsbehörde Informationen", "responsibleLabel": "Verantwortlich für den Inhalt nach § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Impressum", "vatIdLabel": "USt-IdNr.:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "Arabisch", "bn": "<PERSON><PERSON>", "cs": "Tschechisch", "da": "D<PERSON><PERSON><PERSON>", "de": "De<PERSON>ch", "el": "Griechisch", "en": "<PERSON><PERSON><PERSON>", "es": "Spanisch", "fi": "Finnisch", "fr": "Franzö<PERSON><PERSON>", "gu": "Gujarati", "he": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hi": "Hindi", "hu": "Ungarisch", "it": "Italienisch", "ja": "Japanisch", "jv": "Javanisch", "ko": "Koreanisch", "mr": "Marathi", "nl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON><PERSON>", "pa": "Punjabi", "pl": "Polnisch", "pt": "Portugiesisch", "ro": "Rumänisch", "ru": "<PERSON><PERSON>", "sv": "Schwedisch", "ta": "Tamil", "te": "Telugu", "th": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr": "Türkisch", "uk": "U<PERSON><PERSON><PERSON>", "ur": "Urdu", "vi": "Vietnamesisch", "zh": "<PERSON><PERSON><PERSON>"}, "login": {"childProgress": "Kindfortschritt", "easyScheduling": "Einfache Terminplanung", "errors": {"connectionError": "Verbindungsfehler, bitte überprüfen Sie Ihre Internetverbindung", "invalidCredentials": "Ungültiger Benutzername oder Passwort", "licenseValidationFailed": "Lizenzvalidierung fehlgeschlagen", "loginFailed": "Anmeldung fehlgeschlagen", "noActiveLicense": "Keine aktive Lizenz gefunden", "serverError": "<PERSON><PERSON><PERSON>, bitte versuchen Sie es später erneut", "sessionExpired": "Sit<PERSON>ng abgelaufen, bitte erneut anmelden", "tryAgainLater": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später erneut", "unableToVerifyLicense": "Lizenz konnte nicht überprüft werden", "userNotFound": "Benutzer nicht gefunden", "wrongPassword": "Falsches Passwort"}, "forgotPassword": "Passwort vergessen?", "languageSwitch": "Sprache wechseln", "loggingIn": "Anmeldung läuft...", "login": "Anmelden", "password": "Passwort", "summary": "Buchen Sie Lernsitzungen für Ihr Kind und verfolgen Sie dessen Fortschritte an einem Ort.", "title": "Anmelden", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "privacyModal": {"acceptButton": "Ich verstehe und akzeptiere", "childInfoDesc": "Informationen über Ihre Kinder, einschließlich Name, Alter und Lernpräferenzen.", "childInfoTitle": "Kinderinformationen:", "childrenIntro": "Unser Dienst benötigt <PERSON>en über Kinder, um unsere Kernbuchungsdienste bereitzustellen. Wir treffen zusätzliche Vorkehrungen zum Schutz der Daten von Kindern:", "childrenLi1": "Wir sammeln nur die minimal notwendigen Informationen über Kinder", "childrenLi2": "Wir benötigen die Zustimmung der Eltern, bevor wir <PERSON><PERSON> von Kindern sammeln", "childrenLi3": "Wir machen die persönlichen Informationen von Kindern nicht öffentlich zugänglich", "childrenLi4": "Eltern können die Informationen ihres Kindes überprüfen, löschen oder die weitere Sammlung verweigern", "childrenLi5": "Wir implementieren zusätzliche Sicherheitsmaßnahmen für Kinderdaten", "childrenTitle": "Privatsp<PERSON><PERSON><PERSON>", "contactAddress": "<PERSON><PERSON><PERSON>:", "contactEmail": "E-Mail:", "contactIntro": "<PERSON>n Sie Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Si<PERSON> un<PERSON> bitte:", "contactPhone": "Telefon:", "contactTitle": "Kontaktieren Sie uns", "cookiesDesc": "Wir verwenden Cookies und ähnliche Tracking-Technologien, um Aktivitäten auf unserer Plattform zu verfolgen und bestimmte Informationen zu speichern.", "cookiesTitle": "Cookies und Tracking:", "howWeUseIntro": "Wir können die über Sie gesammelten Informationen für verschiedene Zwecke verwenden:", "howWeUseLi1": "Zur Bereitstellung und Wartung unseres Dienstes", "howWeUseLi2": "Um Sie über Änderungen an unserem Dienst zu informieren", "howWeUseLi3": "Um Ihnen die Teilnahme an interaktiven Funktionen zu ermöglichen, wenn Sie dies wünschen", "howWeUseLi4": "Zur Bereitstellung von Kundensupport", "howWeUseLi5": "Um Analysen oder wertvolle Informationen zur Verbesserung unseres Dienstes zu sammeln", "howWeUseLi6": "Zur Überwachung der Nutzung unseres Dienstes", "howWeUseLi7": "<PERSON>ur Erkennung, Verhinderung und Behebung technischer Probleme", "howWeUseLi8": "Zur Abwicklung von Zahlungen und zur Verhinderung betrügerischer Transaktionen", "howWeUseLi9": "Um Sie mit Newslettern, Marketing- oder Werbematerialien und anderen Informationen zu kontaktieren", "howWeUseTitle": "Wie wir Ihre Informationen verwenden", "infoCollectedIntro": "Wir können Informationen über Sie auf verschiedene Weise sammeln, e<PERSON><PERSON><PERSON><PERSON>lich:", "infoCollectedTitle": "<PERSON><PERSON>, die wir sammeln", "intro": "Bei Booking Parents nehmen wir Ihre Privatsphäre ernst. Diese Datenschutzrichtlinie erklärt, wie wir Ihre Informationen sammeln, verwenden, offenlegen und schützen, wenn Sie unsere Plattform nutzen. Bitte lesen Sie diese Datenschutzrichtlinie sorgfältig durch. Wenn Sie mit den Bedingungen dieser Datenschutzrichtlinie nicht einverstanden sind, greifen Sie bitte nicht auf die Anwendung zu.", "lastUpdated": "Zuletzt aktualisiert:", "paymentDataDesc": "Wir sammeln Zahlungsinformationen, wenn Sie über unsere Plattform Einkäufe tätigen, obwohl Zahlungskartendetails nicht auf unseren Servern gespeichert werden.", "paymentDataTitle": "Zahlungsdaten:", "personalDataDesc": "Während der Nutzung unseres Dienstes bitten wir Sie möglicherweise um die Angabe personenbezogener Daten, die zur Kontaktaufnahme oder Identifizierung verwendet werden können, einschließlich Name, E-Mail-Adresse, Telefonnummer und Postanschrift.", "personalDataTitle": "Persönliche Daten:", "rightsAccessDesc": "<PERSON><PERSON> haben <PERSON> Recht, Kopien Ihrer persönlichen Informationen anzufordern.", "rightsAccessTitle": "Recht auf Auskunft:", "rightsContact": "Um eines dieser Rechte auszuüben, kontaktieren Sie uns <NAME_EMAIL>.", "rightsErasureDesc": "Sie haben das Recht zu verlangen, dass wir Ihre persönlichen Informationen löschen.", "rightsErasureTitle": "Recht auf Löschung:", "rightsIntro": "A<PERSON><PERSON><PERSON><PERSON><PERSON> von Ihrem Standort haben Sie möglicherweise bestimmte Rechte bezüglich Ihrer persönlichen Informationen:", "rightsObjectDesc": "Sie haben das Recht, unserer Verarbeitung Ihrer persönlichen Informationen zu widersprechen.", "rightsObjectTitle": "Widerspruchsrecht:", "rightsPortabilityDesc": "Sie haben das Recht zu verlangen, dass wir Ihre Informationen an eine andere Organisation oder direkt an Sie übertragen.", "rightsPortabilityTitle": "Recht auf Datenübertragbarkeit:", "rightsRectificationDesc": "Sie haben das Recht zu verlangen, dass wir unrichtige Informationen über Sie korrigieren.", "rightsRectificationTitle": "Recht auf Berichtigung:", "rightsRestrictDesc": "Sie haben das Recht zu verlangen, dass wir die Verarbeitung Ihrer Informationen einschränken.", "rightsRestrictTitle": "Recht auf Einschränkung der Verarbeitung:", "rightsTitle": "<PERSON><PERSON><PERSON>tz<PERSON>e", "securityIntro1": "Die Sicherheit Ihrer Daten ist uns wichtig, aber denken Si<PERSON>, dass keine Methode der Übertragung über das Internet oder Methode der elektronischen Speicherung 100% sicher ist. Obwohl wir uns bem<PERSON>hen, kommerziell akzeptable Mittel zum Schutz Ihrer persönlichen Daten zu verwenden, können wir deren absolute Sicherheit nicht garantieren.", "securityIntro2": "Unsere Sicherheitsmaßnahmen umfassen:", "securityLi1": "Verschlüsselung sensibler Daten während der Übertragung und im Ruhezustand", "securityLi2": "Regelmäßige Sicherheitsbewertungen und Audits", "securityLi3": "Mitarbeiterschulungen zum Datenschutz", "securityLi4": "Zugriffskontrollen und Authentifizierungsanforderungen", "securityLi5": "Physische Sicherheitsmaßnahmen für unsere Einrichtungen", "securityTitle": "Datensicherheit", "sharingBusinessTransfersDesc": "<PERSON><PERSON> mit einer Fusion, dem Verkauf von Unternehmensvermögen, der Finanzierung oder dem Erwerb unseres gesamten Geschäfts oder eines Teils davon durch ein anderes Unternehmen.", "sharingBusinessTransfersTitle": "Geschäftsübertragungen:", "sharingConsentDesc": "Wir können Ihre persönlichen Informationen für jeden anderen Zweck mit Ihrer Zustimmung offenlegen.", "sharingConsentTitle": "<PERSON><PERSON>:", "sharingIntro": "Wir können Ihre persönlichen Informationen in folgenden Situationen weitergeben:", "sharingLegalDesc": "<PERSON><PERSON> dies gesetzlich vorgeschrieben ist oder als Reaktion auf gültige Anfragen von Behörden.", "sharingLegalTitle": "Gesetzliche Anforderungen:", "sharingServiceProvidersDesc": "Wir können Ihre Informationen an Drittanbieter weitergeben, um unseren Dienst zu erleichtern, den Dienst in unserem Namen bereitzustellen oder dienstbezogene Dienstleistungen zu erbringen.", "sharingServiceProvidersTitle": "<PERSON><PERSON> Dienstleistern:", "sharingStudiosDesc": "Wir geben notwendige Informationen an Lernstudios weiter, um Ihre Buchungen und Lernsitzungen zu ermöglichen.", "sharingStudiosTitle": "<PERSON><PERSON>:", "sharingTitle": "Informationsweitergabe und Offenlegung", "title": "Datenschutzrichtlinie", "usageDataDesc": "<PERSON><PERSON>, wie auf den Dienst zugegriffen und dieser genutzt wird, einschließlich der Internetprotokolladresse Ihres Computers, Browsertyp, besuchte Seiten, auf diesen Seiten verbrachte Zeit und andere Diagnosedaten.", "usageDataTitle": "Nutzungsdaten:"}, "settingsModal": {"appearanceSectionTitle": "Erscheinungsbild", "bookingRemindersLabel": "Buchungserinnerungen", "cancelButton": "Abbrechen", "changePasswordButton": "Passwort ändern", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "Währung", "currencyUSD": "USD ($)", "darkModeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editProfileButton": "<PERSON><PERSON>", "enableNotificationsLabel": "Benachrichtigungen aktivieren", "fontSizeLabel": "Schriftgröße", "highContrastLabel": "<PERSON><PERSON>", "langArabic": "Arabisch", "langChinese": "<PERSON><PERSON><PERSON>", "langDutch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "langEnglish": "<PERSON><PERSON><PERSON>", "langFrench": "Franzö<PERSON><PERSON>", "langGerman": "De<PERSON>ch", "langHebrew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "langHindi": "Hindi", "langJapanese": "Japanisch", "langSpanish": "Spanisch", "languageLabel": "<PERSON><PERSON><PERSON>", "lowBudgetAlertsLabel": "Warnungen bei niedrigem Budget", "marketingUpdatesLabel": "Marketing-Updates", "notificationsSectionTitle": "Benachrichtigungen", "preferencesSectionTitle": "Präferenzen", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Ihr Name", "profileSectionTitle": "Profil", "saveButton": "Änderungen speichern", "timezoneLabel": "Zeitzone", "title": "Einstellungen", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokio (JST)"}, "subjects": {"Art": "<PERSON><PERSON>", "Biology": "Biologie", "Business Studies": "Betriebswirtschaftslehre", "Chemistry": "<PERSON><PERSON><PERSON>", "Computer Science": "Informatik", "Design and Technology": "Design und Technologie", "Drama": "Theater", "Economics": "Wirtschaft", "English": "<PERSON><PERSON><PERSON>", "Foreign Languages": "Fremdsprachen", "Geography": "Geographie", "History": "Geschichte", "Mathematics": "Mathematik", "Music": "Mu<PERSON>", "Philosophy": "Philosophie", "Physical Education": "Sportunterricht", "Physics": "Physik", "Psychology": "Psychologie", "Religious Studies": "Religionslehre", "Social Studies": "Sozialkunde"}, "purchases": {"purchaseHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAndManage": "Alle Käufe und Buchungen anzeigen und verwalten", "downloadReport": "<PERSON><PERSON>t herunterladen", "totalSpent": "Gesamtausgaben", "allCompletedPurchases": "Alle abgeschlossenen Käufe", "totalPurchases": "Gesamtkäufe", "acrossAllChildren": "<PERSON>ber alle Kinder hinweg", "pending": "<PERSON><PERSON><PERSON><PERSON>", "awaitingConfirmation": "Warte auf Bestätigung", "transactions": "Transaktionen", "all": "Alle", "completed": "Abgeschlossen", "failed": "Fehlgeschlagen", "id": "ID", "product": "Produkt", "child": "Kind", "date": "Datum", "amount": "Betrag", "status": "Status", "action": "Aktion", "viewDetails": "Details anzeigen", "noPurchasesFound": "<PERSON><PERSON> gefunden", "noPurchasesYet": "Sie haben noch keine Käufe getätigt. Durchsuchen Sie unsere Produkte, um zu beginnen.", "noPurchasesFiltered": "Sie haben derzeit keine {filter} Käufe.", "selectChild": "Kind auswählen", "allChildren": "<PERSON><PERSON>", "filterByChild": "<PERSON><PERSON>n", "purchaseDetails": "Kaufdetails", "actions": "Aktionen", "markAsCompleted": "Als abgeschlossen markieren", "markAsFailed": "Als fehlgeschlagen markieren"}, "learningDocumentation": {"pageTitle": "Lerndokumentation", "timeframeAllTime": "Gesamter Zeit<PERSON>um", "timeframePastMonth": "Letzter Monat", "timeframePastQuarter": "Letzte 3 Monate", "timeframePastYear": "Letztes Jahr", "exportReportButton": "Bericht exportieren", "selectChildTitle": "Kind auswählen", "allChildrenButton": "<PERSON><PERSON>", "overallProgressTitle": "Gesamtfortschritt", "progressTowardMastery": "Fortschritt zur Beherrschung", "recentAchievementsTitle": "Neueste Erfolge", "viewFullTimelineButton": "Vollständige Zeitleiste anzeigen", "skillsOverviewTitle": "Fähigkeiten Übersicht", "skillsSummaryText": "Zusammenfassung der beherrschten und in Bearbeitung befindlichen Fähigkeiten wird hier angezeigt.", "viewAllSkillsButton": "Alle Fähigkeiten anzeigen", "certificatesTitle": "Zertifikate", "certificatesSummaryText": "Zusammenfassung der erworbenen Zertifikate wird hier angezeigt.", "viewAllCertificatesButton": "Alle Zertifikate anzeigen", "documentationTitle": "Dokumentationsübersicht", "generalNotesLabel": "Allgemeine Notizen", "learningGoalsLabel": "<PERSON><PERSON><PERSON><PERSON>", "strengthsLabel": "<PERSON><PERSON><PERSON><PERSON>", "areasForImprovementLabel": "Verbesserungsbereiche", "noDocumentationMessage": "<PERSON><PERSON><PERSON> dieses Kind wurde noch keine Dokumentation hinzugefügt.", "eventTypes": {"achievement": "Erfolg", "milestone": "<PERSON><PERSON><PERSON>", "assessment": "Bewertung", "test": "Test", "exam": "Prüfung", "report": "Bericht", "presentation": "Präsentation", "project": "Projekt", "workshop": "Workshop", "other": "Sonstiges"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Fortschrittsdokumentation", "skillsTitle": "Fähigkeitsfortschritt", "skillsDescription": "Fortschritt bei verschiedenen Fähigkeiten anzeigen", "childSelectLabel": "Kind auswählen", "skillAreaLabel": "Fähigkeitsbereich", "skillLabel": "Fähigkeit", "skillLevelLabel": "Fähigkeitsstufe", "progressLabel": "Fortschritt (%)", "notesLabel": "Notizen", "optionalText": "Optional", "saveButton": "Speichern", "cancelButton": "Abbrechen", "closeButton": "Schließen", "selectPlaceholder": "-- Auswählen --", "successMessage": "Fortschrittsdokumentation erfolgreich gespeichert!", "errorMessage": "Fehler beim Speichern der Fortschrittsdokumentation. Bitte versuchen Sie es erneut.", "errorFetchingHistory": "Fehler beim Abrufen des Fortschrittsverlaufs", "existingSkillsTitle": "Fortschrittsaufzeichnungen", "selectChildFirst": "Bitte wählen Si<PERSON> zu<PERSON>t ein Kind aus", "loadingProgressMessage": "<PERSON><PERSON><PERSON>t wird geladen...", "noExistingSkillsMessage": "<PERSON>ine Fortschrittsdokumentation für dieses Kind gefunden", "selectSkillAreaFirst": "Bitte wählen Si<PERSON> zu<PERSON>t einen Fähigkeitsbereich aus", "noSkillLevelsMessage": "<PERSON><PERSON> Fähigkeitsstufen verfügbar", "errorFetchingSkillLevels": "Fehler beim Abrufen der Fähigkeitsstufen", "errorFetchingSkills": "Fehler beim Abrufen der Fähigkeiten", "errorFetchingProgress": "Fehler beim Abrufen des Fortschritts", "noLevelAssigned": "<PERSON><PERSON>n", "noSkillAreasMessage": "<PERSON>ine Fähigkeitsbereiche verfügbar", "historyTitle": "Fortschrittsverlauf", "historyEmpty": "<PERSON>ine früheren Fortschrittseinträge gefunden", "historyDate": "Datum", "levelPrefix": "Stuf<PERSON>", "progressPrefix": "Fort<PERSON><PERSON>t", "notesPrefix": "Notizen", "loadingMessage": "Wird geladen...", "lastUpdatedPrefix": "Zuletzt aktualisiert"}, "Timeline": {"pageTitle": "Lern-Zeitlinie", "timeframeAllTime": "Gesamter Zeit<PERSON>um", "timeframePastMonth": "Letzter Monat", "timeframePastQuarter": "Letzte 3 Monate", "timeframePastYear": "Letztes Jahr", "exportTimelineButton": "Zeitlinie exportieren", "selectChildTitle": "Kind auswählen", "allChildrenButton": "<PERSON><PERSON>", "filterByEventTypeTitle": "<PERSON><PERSON> Ereignistyp <PERSON>n", "allEventsButton": "Alle Ereignisse", "learningEventsTitle": "Lernereignisse", "skillProgressLabel": "Fortschritt der Fähigkeiten", "relatedSkillsLabel": "Zugehörige Fähigkeiten", "achievementLabel": "Erfolg", "noEventsMatchMessage": "<PERSON><PERSON> Ereignisse entsprechen Ihren aktuellen Filtern.", "noEventsYetMessage": "<PERSON><PERSON><PERSON> dieses Kind wurden noch keine Lernereignisse erfasst.", "eventsCreatedInAssistantsMessage": "Ereignisse werden hier angezeigt, sobald sie in der Assistenten-App erstellt wurden.", "resetFiltersButton": "<PERSON><PERSON>", "loading": "Ereignisse werden geladen...", "error": "Fehler beim Laden der Ereignisse. Bitte versuchen Sie es erneut."}, "Certificates": {"pageTitle": "Zertifikate", "certificatesTitle": "Zertifikate & Erfolge", "certificatesDescription": "Zertif<PERSON>te anzeigen, die Ihr Kind erworben hat", "selectChildTitle": "Kind auswählen", "allChildrenButton": "<PERSON><PERSON>", "downloadAllButton": "Alle herunterladen", "certificateLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "awardedToText": "<PERSON><PERSON><PERSON><PERSON> an", "viewDetailsButton": "Details anzeigen", "downloadButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Zertifikate werden geladen...", "error": "Fehler beim Laden der Zertifikate. Bitte versuchen Sie es erneut.", "noCertificatesFound": "<PERSON><PERSON> Zertifikate für dieses Kind gefunden.", "noCertificatesMessage": "<PERSON><PERSON><PERSON> dieses Kind wurden noch keine Zertifikate hinzugefügt.", "certificateDetailsTitle": "Zertifikatsdetails", "certificateImageLabel": "Zertifikatsbild", "closeButton": "Schließen", "imageLoadError": "Bild konnte nicht geladen werden", "titleLabel": "Zertifikatstitel", "issueDateLabel": "Ausstellungsdatum", "issueDateISOLabel": "Ausstellungsdatum (ISO)", "relatedSkillsLabel": "Zugehörige Fähigkeiten", "certificateIdLabel": "Zertifikats-ID"}, "eventTypes": {"achievement": "Erfolg", "milestone": "<PERSON><PERSON><PERSON>", "assessment": "Bewertung", "test": "Test", "exam": "Prüfung", "report": "Bericht", "presentation": "Präsentation", "project": "Projekt", "workshop": "Workshop", "other": "Sonstiges"}}}