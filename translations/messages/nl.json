{"common": {"appName": "<PERSON>ude<PERSON> App", "betaTag": "BETA", "home": "Home", "logout": "Uitloggen", "shop": "<PERSON><PERSON>"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Belgium", "Brazil": "Brazil", "Bulgaria": "Bulgaria", "Canada": "Canada", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Denmark", "Finland": "Finland", "France": "France", "Germany": "Germany", "India": "India", "Italy": "Italy", "Japan": "Japan", "Luxembourg": "Luxembourg", "Mexico": "Mexico", "Netherlands": "Netherlands", "New Zealand": "New Zealand", "Norway": "Norway", "Russia": "Russia", "Singapore": "Singapore", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Sweden": "Sweden", "Switzerland": "Switzerland", "United Kingdom": "United Kingdom", "United States": "United States", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Alle rechten voorbehouden.", "footerImprint": "Colofon", "footerPrivacy": "Privacy", "navDashboard": "Dashboard", "navLearningProgress": "Leervoortgang", "navLogout": "Uitloggen", "navPurchases": "Aankopen", "navShop": "<PERSON><PERSON>", "navStudios": "Studio's"}, "dashboardPage": {"addChildBookingPermissionLabel": "Allow Child to Book Sessions?", "addChildBudgetLabel": "Initial Budget (€)", "addChildBudgetPlaceholder": "e.g., 50.00", "addChildButton": "Add New Child", "addChildCancelButton": "Cancel", "addChildCardDesc": "Create a new child account to manage", "addChildModalTitle": "Add New Child", "addChildNameLabel": "Child's Name", "addChildPasswordLabel": "Child's Password", "addChildPasswordPlaceholder": "Create password", "addChildSubmitButton": "Add Child", "addChildUsernameLabel": "<PERSON>'s <PERSON><PERSON><PERSON>", "addChildUsernamePlaceholder": "Create unique username", "addFirstChildButton": "Add Your First Child", "addFundsAmountLabel": "Amount (€)", "addFundsButton": "Add Funds", "addFundsCancelButton": "Cancel", "addFundsHelpText": "Enter the amount you want to add to the child's budget. The funds will be available immediately.", "addFundsModalTitle": "Add Funds to {0}'s Account", "addFundsSubmitButton": "Add Funds", "bookingAlertEventAdded": "New event \"{0}\" added!", "bookingAlertEventMoved": "{0} was moved to {1}", "bookingAlertEventResized": "{0} was resized to end at {1}", "bookingAlertSelectionCancelled": "Selection cancelled.", "bookingConfirmViewDetails": "View details for \"{0}\"? \nDescription: {1}", "bookingConfirmation": {"availability": "Availability", "available": "Available", "biweekly": "Bi-weekly", "bookingConflictDetailed": "Booking conflict detected for {childName} in {roomName}. The requested time ({requestedTime}) conflicts with an existing booking ({conflictingTime}). Please select a different time slot.", "bookingCost": "Booking Cost", "bookingDetails": "Booking Details", "bookingFailed": "Booking Failed", "budgetInformation": "Budget Information", "cancelButton": "Cancel", "checking": "Checking...", "child": "Child", "confirmButton": "Confirm Booking", "currentBudget": "Current Budget", "date": "Date", "deleteSuccessDescription": "Your booking has been deleted.", "deleteSuccessMessage": "Booking Successfully Deleted!", "duration": "Duration", "enableRecurringBooking": "Enable Recurring Booking", "error": "Error", "errorLoadingBudget": "Error loading budget information", "friday": "Friday", "hours": "hours", "insufficientBudget": "Insufficient Budget", "insufficientBudgetMessage": "The child does not have enough budget for this booking. Please add funds or select a shorter time slot.", "monday": "Monday", "monthly": "Monthly", "none": "None", "notAvailable": "Not Available", "numberOfBookings": "Number of Bookings", "numberOfOccurrences": "Number of Occurrences", "pricePerHour": "Price per hour", "processing": "Processing...", "recurrenceType": "Recurrence Type", "recurringBooking": "Recurring Booking", "remainingBudget": "Remaining Budget", "room": "Room", "saturday": "Saturday", "selectDaysOfWeek": "Select Days of Week", "selectType": "Select Type", "singleBookingCost": "Single Booking Cost", "studio": "Studio", "successDescription": "Your booking has been confirmed.", "successMessage": "Booking Successful!", "sunday": "Sunday", "thursday": "Thursday", "time": "Time", "timeSlotBooked": "This time slot is already booked. Please select another time.", "title": "Confirm Booking", "totalPrice": "Total Price", "tryAgainMessage": "Please try selecting a different time slot or contact support if the problem persists.", "tuesday": "Tuesday", "wednesday": "Wednesday", "weekly": "Weekly", "weeklyRecurrenceInfo": "The booking will repeat on the selected days for the specified number of weeks.", "outsideOpeningHours": "Boekingen zijn alleen mogelijk binnen de openingstijden van de studio.", "recurringBookingCapacityExceeded": "<PERSON><PERSON> of meer van de te<PERSON>ende boekingen zou de capaciteit van de ruimte overschrijden. Verminder het aantal herhalingen of kies een ander tij<PERSON>.", "biweeklyRecurrenceInfo": "De boeking wordt elke twee weken op dezelfde dag herhaald voor het opgegeven aantal keren.", "limitedByBudget": "Beperkt door budget", "limitedByCapacity": "Beperkt door beschikbare ruimte"}, "bookingDescriptionNone": "None", "bookingModalTitle": "Book Session for {0}", "bookingPromptEventTitle": "Enter a title for your event:", "childBookAction": "Book appointment", "childBookDisabled": "Booking disabled", "childBudgetAvailable": "Available", "childBudgetEmpty": "Empty", "childCardAddFundsButton": "Add Funds", "childCardBookButton": "Book Session", "childCardBookingLabel": "Booking Allowed:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Delete", "childCardEditButton": "Edit", "childCardNameLabel": "Name:", "childCardUsernameLabel": "Username:", "childStatusCanBook": "Child Can Book", "childStatusNoBooking": "Parent Only Booking", "childrenCardDescPlural": "{count, plural, one {# child managed} other {# children managed}}", "childrenCardDescSingular": "1 active child account", "childrenCardTitle": "Children", "classLabel": "Class/Grade", "countryLabel": "Country", "editChildAllowBookingsDescription": "If checked, the child can log in and book sessions themselves using their budget. If unchecked, only parents can book appointments for this child. Parents can always book appointments for their children regardless of this setting.", "editChildAllowBookingsLabel": "Allow Child to Book Sessions?", "editChildCancelButtonText": "Cancel", "editChildNameLabel": "Child's Name", "editChildNamePlaceholder": "Enter child's name", "editChildPasswordHelpText": "Only enter if you want to change the child's password.", "editChildPasswordLabelOptional": "New Password (Optional)", "editChildPasswordPlaceholderOptional": "Leave blank to keep current", "editChildSaveButtonText": "Save Changes", "editChildTitle": "Edit Child Details", "editChildUpdateErrorMessage": "Error updating child details. Please try again.", "editChildUpdateSuccessMessage": "Child details updated successfully!", "editChildUpdatingButtonText": "Updating...", "languageLabel": "Language", "managePrompt": "Manage your children's accounts and book learning sessions", "myChildrenTitle": "My Children", "noChildrenFoundDesc": "Use the form below to add your first child account to start managing bookings", "noChildrenFoundTitle": "No children found", "paymentHistoryButton": "Payment History", "paymentHistoryCloseButton": "Close", "paymentHistoryExportBills": "Download All Bills", "paymentHistoryExportButton": "Export Options", "paymentHistoryExportCsv": "Export as CSV", "paymentHistoryFilterAll": "All Transactions", "paymentHistoryFilterDeposits": "Deposits Only", "paymentHistoryFilterWithdrawals": "Withdrawals Only", "paymentHistoryModalTitle": "Payment History", "paymentHistorySearchPlaceholder": "Search...", "paymentHistorySummaryDeposits": "Total Deposits", "paymentHistorySummaryNet": "Net Balance Change", "paymentHistorySummaryWithdrawals": "Total Withdrawals", "paymentHistoryTableActions": "Actions", "paymentHistoryTableAmount": "Amount (€)", "paymentHistoryTableChild": "Child", "paymentHistoryTableDate": "Date", "paymentHistoryTableDesc": "Description", "paymentHistoryTableType": "Type", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "<PERSON><PERSON><PERSON>", "preferredStudioDesc": "This studio will be pre-selected when booking activities", "preferredStudioTitle": "Preferred Learning Studio", "regionDisabledText": "Region not applicable for selected country", "regionLabel": "Region", "schoolTypeLabel": "School Type", "selectChildCurrentBudgetLabel": "Current Budget:", "selectChildFundsCancelButton": "Cancel", "selectChildFundsModalTitle": "Select Child to Add Funds", "selectChildFundsPrompt": "Choose which child's account to top up:", "selectCountryFirst": "Select country first", "selectPlaceholder": "Select...", "selectStudioPlaceholder": "-- Select a Studio --", "statusAddChildError": "Error adding child: {0}", "statusAddChildSuccess": "Child added successfully", "statusAddFundsError": "Error adding funds: {0}", "statusAddFundsSuccess": "Successfully added €{0} to {1}'s account", "statusAuthRequired": "Authentication required.", "statusDeleteChildConfirm": "Are you sure you want to delete child ID {0}? This cannot be undone.", "statusDeleteChildError": "Error deleting child: {0}", "statusDeleteChildSuccess": "Child deleted successfully", "statusErrorChildren": "Error fetching children", "statusErrorInitialData": "Error fetching initial data: {0}", "statusErrorPreferredStudio": "Error fetching preferred studio", "statusErrorStudios": "Error fetching studios", "statusFindChildError": "Could not find child details.", "statusLoading": "Loading...", "statusUnexpectedError": "An unexpected error occurred.", "statusUpdateChildSuccess": "Child details updated successfully.", "statusUpdatePreferredError": "Error updating preferred studio: {0}", "statusUpdatePreferredSuccess": "Preferred studio updated successfully", "subjectsLabel": "Subjects", "totalBudgetCardTitle": "Total Budget", "welcomeMessage": "Welcome"}, "helpModal": {"bookingProcessIntro": "Om een leersessie te boeken:", "bookingProcessLi1": "Selecteer een kind vanaf het dashboard", "bookingProcessLi2": "Klik op het kalenderpictogram om het boekingsvenster te openen", "bookingProcessLi3": "<PERSON><PERSON> een datum, tijd en activiteitstype", "bookingProcessLi4": "Bevestig de boeking (budget wordt automatisch afgeschreven)", "bookingProcessLi5": "Volg alle boekingen in de sectie Aankopen", "bookingProcessTitle": "Boekingsproces", "contactSupportButton": "Contact Opnemen met Support", "faq1Answer": "Gebruik de knop \"<PERSON><PERSON>n\" in het dashboard om het leerbudget van je kind aan te vullen. Je kunt creditcard, PayPal of bankoverschrijving gebruiken.", "faq1Question": "Hoe voeg ik geld toe aan het account van mijn kind?", "faq2Answer": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> het aanmaken van een kindaccount stel je een gebruikers<PERSON>am en wachtwoord in. Je kind kan deze inloggegevens gebruiken om toegang te krijgen tot zijn eigen beperkte dashboard.", "faq2Question": "Kan mijn kind apart inloggen?", "faq3Answer": "<PERSON>a naar de pagina <PERSON>, zoe<PERSON> de boeking die je wilt annuleren en klik op \"Details Bekijken\". Je vindt een annuleringsoptie als de sessie meer dan 24 uur later plaatsvindt.", "faq3Question": "Hoe annuleer ik een boeking?", "faqTitle": "Veelgestelde Vragen", "gettingStartedLi1": "Voeg je kinderen toe met de knop \"Nieuw Kind Toevoegen\"", "gettingStartedLi2": "<PERSON><PERSON> je voorkeursstudio in op het dashboard", "gettingStartedLi3": "<PERSON><PERSON>r het budget van elk kind voor activiteiten", "gettingStartedLi4": "<PERSON><PERSON> le<PERSON> en volg de voortgang", "gettingStartedTitle": "<PERSON><PERSON>", "gettingStartedWelcome": "Welkom bij de Booking Parents app! Dit platform helpt je om de leeractiviteiten en boekingen van je kinderen op één plek te beheren.", "managingChildrenAddBudget": "Budget Toevoegen", "managingChildrenAddBudgetDesc": ": <PERSON><PERSON> het leerbudget van je kind aan", "managingChildrenBook": "<PERSON><PERSON><PERSON>", "managingChildrenBookDesc": ": Plan leersessies bij je voorkeursstudio", "managingChildrenDelete": "Verwijderen", "managingChildrenDeleteDesc": ": <PERSON><PERSON><PERSON><PERSON><PERSON> een kindaccount (kan niet ongedaan worden gemaakt)", "managingChildrenEdit": "Bewerken", "managingChildrenEditDesc": ": Werk naam, budget en boekingsrechten bij", "managingChildrenIntro": "Voor elk kind kun je:", "managingChildrenTitle": "<PERSON><PERSON><PERSON>", "needMoreHelpEmail": "E-mail:", "needMoreHelpHours": "Uren: Maandag-Vrijdag, 9:00-17:00 CET", "needMoreHelpIntro": "Als je extra hulp nodig hebt, staat ons supportteam voor je klaar:", "needMoreHelpPhone": "Telefoon:", "needMoreHelpTitle": "<PERSON><PERSON>?", "title": "Helpcentrum"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Close", "companyInfoTitle": "Company Information", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Germany", "directorsLabel": "Managing Directors:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Despite careful content control, we assume no liability for the content of external links. The operators of the linked pages are solely responsible for their content.", "disclaimerTitle": "Disclaimer", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Phone:", "regNumLabel": "Registration Number:", "regNumValue": "HRB 123456", "registerLabel": "Commercial Register:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Regulatory Information", "responsibleLabel": "Responsible for content according to § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Imprint", "vatIdLabel": "VAT ID:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "Arabic", "bn": "Bengali", "cs": "Czech", "da": "Danish", "de": "German", "el": "Greek", "en": "English", "es": "Spanish", "fi": "Finnish", "fr": "French", "gu": "Gujarati", "he": "Hebrew", "hi": "Hindi", "hu": "Hungarian", "it": "Italian", "ja": "Japanese", "jv": "Javanese", "ko": "Korean", "mr": "Marathi", "nl": "Dutch", "no": "Norwegian", "pa": "Punjabi", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "sv": "Swedish", "ta": "Tamil", "te": "Telugu", "th": "Thai", "tr": "Turkish", "uk": "Ukrainian", "ur": "Urdu", "vi": "Vietnamese", "zh": "Chinese"}, "login": {"childProgress": "Voortgang Kind", "easyScheduling": "Eenvoudige Planning", "errors": {"connectionError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, controleer je internetverbinding", "invalidCredentials": "Ongeldige gebruikersnaam of wachtwoord", "licenseValidationFailed": "Licentie<PERSON><PERSON><PERSON> mis<PERSON>t", "loginFailed": "Inloggen mislukt", "noActiveLicense": "<PERSON>n actieve licentie gevonden", "serverError": "<PERSON><PERSON><PERSON>, probeer het later opnieuw", "sessionExpired": "<PERSON><PERSON> verlopen, log opnieuw in", "tryAgainLater": "Er is een fout opgetreden, probeer het later opnieuw", "unableToVerifyLicense": "Kan licentie niet verifi<PERSON>ren", "userNotFound": "Gebruiker niet gevonden", "wrongPassword": "Verkeerd wa<PERSON>"}, "forgotPassword": "Wachtwoord vergeten?", "languageSwitch": "Taal wijzigen", "loggingIn": "Inloggen...", "login": "Inloggen", "password": "Wachtwoord", "summary": "<PERSON><PERSON> le<PERSON> voor je kind en volg hun voortgang op één plek.", "title": "Inloggen", "username": "Gebruikersnaam"}, "privacyModal": {"acceptButton": "I Understand and Accept", "childInfoDesc": "Information about your children, including name, age, and learning preferences.", "childInfoTitle": "Child Information:", "childrenIntro": "Our service requires information about children to provide our core booking services. We take additional precautions to protect children's data:", "childrenLi1": "We collect only the minimum necessary information about children", "childrenLi2": "We require parental consent before collecting information from children", "childrenLi3": "We do not make children's personal information publicly available", "childrenLi4": "Parents can review, delete, or refuse further collection of their child's information", "childrenLi5": "We implement additional security measures for children's data", "childrenTitle": "Children's Privacy", "contactAddress": "Address:", "contactEmail": "Email:", "contactIntro": "If you have any questions about this Privacy Policy, please contact us:", "contactPhone": "Phone:", "contactTitle": "Contact Us", "cookiesDesc": "We use cookies and similar tracking technologies to track activity on our Platform and hold certain information.", "cookiesTitle": "Cookies and Tracking:", "howWeUseIntro": "We may use the information we collect about you for various purposes:", "howWeUseLi1": "To provide and maintain our service", "howWeUseLi2": "To notify you about changes to our service", "howWeUseLi3": "To allow you to participate in interactive features when you choose to do so", "howWeUseLi4": "To provide customer support", "howWeUseLi5": "To gather analysis or valuable information to improve our service", "howWeUseLi6": "To monitor the usage of our service", "howWeUseLi7": "To detect, prevent and address technical issues", "howWeUseLi8": "To process payments and prevent fraudulent transactions", "howWeUseLi9": "To contact you with newsletters, marketing or promotional materials and other information", "howWeUseTitle": "How We Use Your Information", "infoCollectedIntro": "We may collect information about you in various ways, including:", "infoCollectedTitle": "Information We Collect", "intro": "At Booking Parents, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.", "lastUpdated": "Last updated:", "paymentDataDesc": "We collect payment information when you make purchases through our platform, though payment card details are not stored on our servers.", "paymentDataTitle": "Payment Data:", "personalDataDesc": "While using our service, we may ask you to provide personally identifiable information that can be used to contact or identify you, including name, email address, phone number, and postal address.", "personalDataTitle": "Personal Data:", "rightsAccessDesc": "You have the right to request copies of your personal information.", "rightsAccessTitle": "Right to Access:", "rightsContact": "To exercise any of these rights, please contact <NAME_EMAIL>.", "rightsErasureDesc": "You have the right to request that we delete your personal information.", "rightsErasureTitle": "Right to Erasure:", "rightsIntro": "Depending on your location, you may have certain rights regarding your personal information:", "rightsObjectDesc": "You have the right to object to our processing of your personal information.", "rightsObjectTitle": "Right to Object:", "rightsPortabilityDesc": "You have the right to request that we transfer your information to another organization or directly to you.", "rightsPortabilityTitle": "Right to Data Portability:", "rightsRectificationDesc": "You have the right to request that we correct inaccurate information about you.", "rightsRectificationTitle": "Right to Rectification:", "rightsRestrictDesc": "You have the right to request that we restrict the processing of your information.", "rightsRestrictTitle": "Right to Restrict Processing:", "rightsTitle": "Your Privacy Rights", "securityIntro1": "The security of your data is important to us but remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.", "securityIntro2": "Our security measures include:", "securityLi1": "Encryption of sensitive data in transit and at rest", "securityLi2": "Regular security assessments and audits", "securityLi3": "Employee training on data protection", "securityLi4": "Access controls and authentication requirements", "securityLi5": "Physical security measures for our facilities", "securityTitle": "Data Security", "sharingBusinessTransfersDesc": "In connection with any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company.", "sharingBusinessTransfersTitle": "Business Transfers:", "sharingConsentDesc": "We may disclose your personal information for any other purpose with your consent.", "sharingConsentTitle": "With Your Consent:", "sharingIntro": "We may share your personal information in the following situations:", "sharingLegalDesc": "If required to do so by law or in response to valid requests by public authorities.", "sharingLegalTitle": "Legal Requirements:", "sharingServiceProvidersDesc": "We may share your information with third-party service providers to facilitate our Service, provide the Service on our behalf, or perform service-related services.", "sharingServiceProvidersTitle": "With Service Providers:", "sharingStudiosDesc": "We share necessary information with learning studios to facilitate your bookings and learning sessions.", "sharingStudiosTitle": "With Learning Studios:", "sharingTitle": "Information Sharing and Disclosure", "title": "Privacy Policy", "usageDataDesc": "Information on how the Service is accessed and used, including your computer's Internet Protocol address, browser type, pages visited, time spent on those pages, and other diagnostic data.", "usageDataTitle": "Usage Data:"}, "settingsModal": {"appearanceSectionTitle": "Appearance", "bookingRemindersLabel": "Booking Reminders", "cancelButton": "Cancel", "changePasswordButton": "Change Password", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Dark Mode", "editProfileButton": "Edit Profile", "enableNotificationsLabel": "Enable Notifications", "fontSizeLabel": "Font Size", "highContrastLabel": "High Contrast", "langArabic": "Arabic", "langChinese": "Chinese", "langDutch": "Dutch", "langEnglish": "English", "langFrench": "French", "langGerman": "German", "langHebrew": "Hebrew", "langHindi": "Hindi", "langJapanese": "Japanese", "langSpanish": "Spanish", "languageLabel": "Language", "lowBudgetAlertsLabel": "Low Budget Alerts", "marketingUpdatesLabel": "Marketing Updates", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Preferences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Your Name", "profileSectionTitle": "Profile", "saveButton": "Save Changes", "timezoneLabel": "Time Zone", "title": "Settings", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biology", "Business Studies": "Business Studies", "Chemistry": "Chemistry", "Computer Science": "Computer Science", "Design and Technology": "Design and Technology", "Drama": "Drama", "Economics": "Economics", "English": "English", "Foreign Languages": "Foreign Languages", "Geography": "Geography", "History": "History", "Mathematics": "Mathematics", "Music": "Music", "Philosophy": "Philosophy", "Physical Education": "Physical Education", "Physics": "Physics", "Psychology": "Psychology", "Religious Studies": "Religious Studies", "Social Studies": "Social Studies"}, "purchases": {"purchaseHistory": "A<PERSON><PERSON><PERSON>ges<PERSON><PERSON><PERSON>", "viewAndManage": "Bekijk en beheer alle aankopen en boekingen", "downloadReport": "Rapport downloaden", "totalSpent": "To<PERSON><PERSON> besteed", "allCompletedPurchases": "Alle voltooide aankopen", "totalPurchases": "Totale aankopen", "acrossAllChildren": "Voor alle kinderen", "pending": "In behandeling", "awaitingConfirmation": "Wacht op bevestiging", "transactions": "Transacties", "all": "Alle", "completed": "Voltooid", "failed": "Mislukt", "id": "ID", "product": "Product", "child": "Kind", "date": "Datum", "amount": "Bedrag", "status": "Status", "action": "<PERSON><PERSON>", "viewDetails": "Details bekijken", "noPurchasesFound": "<PERSON><PERSON> a<PERSON> gevo<PERSON>", "noPurchasesYet": "Je hebt nog geen aankopen gedaan. Bekijk onze producten om te beginnen.", "noPurchasesFiltered": "Je hebt momenteel geen {filter} aanko<PERSON>.", "selectChild": "Kind selecteren", "allChildren": "Alle kinderen", "filterByChild": "Filteren op kind", "purchaseDetails": "Aankoopdetails", "actions": "Acties", "markAsCompleted": "Markeren als voltooid", "markAsFailed": "<PERSON><PERSON><PERSON> als mislukt"}, "learningDocumentation": {"pageTitle": "Leerdocumentatie", "timeframeAllTime": "Alle tijd", "timeframePastMonth": "Afgelopen maand", "timeframePastQuarter": "Afgelopen 3 maanden", "timeframePastYear": "Afgelopen jaar", "exportReportButton": "Rapport exporteren", "selectChildTitle": "Kind selecteren", "allChildrenButton": "Alle kinderen", "overallProgressTitle": "Algemene voortgang", "progressTowardMastery": "<PERSON><PERSON><PERSON><PERSON><PERSON> naar beheersing", "recentAchievementsTitle": "Recente prestaties", "viewFullTimelineButton": "Volledige tijdlijn be<PERSON>jken", "skillsOverviewTitle": "Vaardigheden overzicht", "skillsSummaryText": "<PERSON><PERSON><PERSON><PERSON> van beheerste en in-progress vaardigheden komt hier.", "viewAllSkillsButton": "Alle vaardigheden bekijken", "certificatesTitle": "<PERSON>n", "certificatesSummaryText": "<PERSON><PERSON><PERSON><PERSON> van be<PERSON>e certificaten komt hier.", "viewAllCertificatesButton": "Alle certificaten bekijken", "documentationTitle": "Documentatie Overzicht", "generalNotesLabel": "Algemene Notities", "learningGoalsLabel": "<PERSON><PERSON><PERSON><PERSON>", "strengthsLabel": "<PERSON><PERSON><PERSON>", "areasForImprovementLabel": "Verbeterpunten", "noDocumentationMessage": "Er is nog geen documentatie toegevoegd voor dit kind.", "eventTypes": {"achievement": "Prestatie", "milestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assessment": "Beoordeling", "test": "Test", "exam": "Examen", "report": "Rapport", "presentation": "<PERSON><PERSON><PERSON>", "project": "Project", "workshop": "Workshop", "other": "<PERSON><PERSON>"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "Laatst bijgewerkt"}, "eventTypes": {"achievement": "Prestatie", "milestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assessment": "Beoordeling", "test": "Test", "exam": "Examen", "report": "Rapport", "presentation": "<PERSON><PERSON><PERSON>", "project": "Project", "workshop": "Workshop", "other": "<PERSON><PERSON>"}, "Certificates": {"certificateDetailsTitle": "Certificaatdetails", "certificateImageLabel": "Certificaatafbeelding", "closeButton": "Sluiten", "imageLoadError": "Afbeelding kon niet worden geladen", "titleLabel": "Certificaattitel", "issueDateLabel": "Uitgiftedatum", "issueDateISOLabel": "Uitgiftedatum (ISO)", "relatedSkillsLabel": "Gerelateerd<PERSON> v<PERSON>n", "certificateIdLabel": "Certificaat-ID"}}}