{"common": {"appName": "Forældre App", "betaTag": "BETA", "home": "<PERSON><PERSON><PERSON>", "logout": "Log ud", "shop": "<PERSON><PERSON>"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Belgium", "Brazil": "Brazil", "Bulgaria": "Bulgaria", "Canada": "Canada", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Denmark", "Finland": "Finland", "France": "France", "Germany": "Germany", "India": "India", "Italy": "Italy", "Japan": "Japan", "Luxembourg": "Luxembourg", "Mexico": "Mexico", "Netherlands": "Netherlands", "New Zealand": "New Zealand", "Norway": "Norway", "Russia": "Russia", "Singapore": "Singapore", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Sweden": "Sweden", "Switzerland": "Switzerland", "United Kingdom": "United Kingdom", "United States": "United States", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Alle rettigheder forbeholdes.", "footerImprint": "Kolofon", "footerPrivacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navDashboard": "Dashboard", "navLearningProgress": "<PERSON><PERSON><PERSON>forl<PERSON><PERSON>", "navLogout": "Log ud", "navPurchases": "<PERSON><PERSON><PERSON>", "navShop": "<PERSON><PERSON>", "navStudios": "<PERSON><PERSON><PERSON>"}, "dashboardPage": {"addChildBookingPermissionLabel": "Allow Child to Book Sessions?", "addChildBudgetLabel": "Initial Budget (€)", "addChildBudgetPlaceholder": "e.g., 50.00", "addChildButton": "Add New Child", "addChildCancelButton": "Cancel", "addChildCardDesc": "Create a new child account to manage", "addChildModalTitle": "Add New Child", "addChildNameLabel": "Child's Name", "addChildPasswordLabel": "Child's Password", "addChildPasswordPlaceholder": "Create password", "addChildSubmitButton": "Add Child", "addChildUsernameLabel": "<PERSON>'s <PERSON><PERSON><PERSON>", "addChildUsernamePlaceholder": "Create unique username", "addFirstChildButton": "Add Your First Child", "addFundsAmountLabel": "Amount (€)", "addFundsButton": "Add Funds", "addFundsCancelButton": "Cancel", "addFundsHelpText": "Enter the amount you want to add to the child's budget. The funds will be available immediately.", "addFundsModalTitle": "Add Funds to {0}'s Account", "addFundsSubmitButton": "Add Funds", "bookingAlertEventAdded": "New event \"{0}\" added!", "bookingAlertEventMoved": "{0} was moved to {1}", "bookingAlertEventResized": "{0} was resized to end at {1}", "bookingAlertSelectionCancelled": "Selection cancelled.", "bookingConfirmViewDetails": "View details for \"{0}\"? \nDescription: {1}", "bookingConfirmation": {"availability": "Availability", "available": "Available", "biweekly": "Bi-weekly", "bookingConflictDetailed": "Booking conflict detected for {childName} in {roomName}. The requested time ({requestedTime}) conflicts with an existing booking ({conflictingTime}). Please select a different time slot.", "bookingCost": "Booking Cost", "bookingDetails": "Booking Details", "bookingFailed": "Booking Failed", "budgetInformation": "Budget Information", "cancelButton": "Cancel", "checking": "Checking...", "child": "Child", "confirmButton": "Confirm Booking", "currentBudget": "Current Budget", "date": "Date", "deleteSuccessDescription": "Your booking has been deleted.", "deleteSuccessMessage": "Booking Successfully Deleted!", "duration": "Duration", "enableRecurringBooking": "Enable Recurring Booking", "error": "Error", "errorLoadingBudget": "Error loading budget information", "friday": "Friday", "hours": "hours", "insufficientBudget": "Insufficient Budget", "insufficientBudgetMessage": "The child does not have enough budget for this booking. Please add funds or select a shorter time slot.", "monday": "Monday", "monthly": "Monthly", "none": "None", "notAvailable": "Not Available", "numberOfBookings": "Number of Bookings", "numberOfOccurrences": "Number of Occurrences", "pricePerHour": "Price per hour", "processing": "Processing...", "recurrenceType": "Recurrence Type", "recurringBooking": "Recurring Booking", "remainingBudget": "Remaining Budget", "room": "Room", "saturday": "Saturday", "selectDaysOfWeek": "Select Days of Week", "selectType": "Select Type", "singleBookingCost": "Single Booking Cost", "studio": "Studio", "successDescription": "Your booking has been confirmed.", "successMessage": "Booking Successful!", "sunday": "Sunday", "thursday": "Thursday", "time": "Time", "timeSlotBooked": "This time slot is already booked. Please select another time.", "title": "Confirm Booking", "totalPrice": "Total Price", "tryAgainMessage": "Please try selecting a different time slot or contact support if the problem persists.", "tuesday": "Tuesday", "wednesday": "Wednesday", "weekly": "Weekly", "weeklyRecurrenceInfo": "The booking will repeat on the selected days for the specified number of weeks.", "outsideOpeningHours": "Bookinger er kun mulige inden for studiets åbningstider.", "recurringBookingCapacityExceeded": "En eller flere af de tilbagevendende bookinger ville overstige lokalets kapacitet. Reducer venligst antallet af forekomster eller vælg et andet tidspunkt.", "biweeklyRecurrenceInfo": "Bookingen gentages hver anden uge på samme dag i det angivne antal forekomster.", "limitedByBudget": "Begrænset af budget", "limitedByCapacity": "Begrænset af tilgængelig plads"}, "bookingDescriptionNone": "None", "bookingModalTitle": "Book Session for {0}", "bookingPromptEventTitle": "Enter a title for your event:", "childBookAction": "Book appointment", "childBookDisabled": "Booking disabled", "childBudgetAvailable": "Available", "childBudgetEmpty": "Empty", "childCardAddFundsButton": "Add Funds", "childCardBookButton": "Book Session", "childCardBookingLabel": "Booking Allowed:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Delete", "childCardEditButton": "Edit", "childCardNameLabel": "Name:", "childCardUsernameLabel": "Username:", "childStatusCanBook": "Child Can Book", "childStatusNoBooking": "Parent Only Booking", "childrenCardDescPlural": "{count, plural, one {# child managed} other {# children managed}}", "childrenCardDescSingular": "1 active child account", "childrenCardTitle": "Children", "classLabel": "Class/Grade", "countryLabel": "Country", "editChildAllowBookingsDescription": "If checked, the child can log in and book sessions themselves using their budget. If unchecked, only parents can book appointments for this child. Parents can always book appointments for their children regardless of this setting.", "editChildAllowBookingsLabel": "Allow Child to Book Sessions?", "editChildCancelButtonText": "Cancel", "editChildNameLabel": "Child's Name", "editChildNamePlaceholder": "Enter child's name", "editChildPasswordHelpText": "Only enter if you want to change the child's password.", "editChildPasswordLabelOptional": "New Password (Optional)", "editChildPasswordPlaceholderOptional": "Leave blank to keep current", "editChildSaveButtonText": "Save Changes", "editChildTitle": "Edit Child Details", "editChildUpdateErrorMessage": "Error updating child details. Please try again.", "editChildUpdateSuccessMessage": "Child details updated successfully!", "editChildUpdatingButtonText": "Updating...", "languageLabel": "Language", "managePrompt": "Manage your children's accounts and book learning sessions", "myChildrenTitle": "My Children", "noChildrenFoundDesc": "Use the form below to add your first child account to start managing bookings", "noChildrenFoundTitle": "No children found", "paymentHistoryButton": "Payment History", "paymentHistoryCloseButton": "Close", "paymentHistoryExportBills": "Download All Bills", "paymentHistoryExportButton": "Export Options", "paymentHistoryExportCsv": "Export as CSV", "paymentHistoryFilterAll": "All Transactions", "paymentHistoryFilterDeposits": "Deposits Only", "paymentHistoryFilterWithdrawals": "Withdrawals Only", "paymentHistoryModalTitle": "Payment History", "paymentHistorySearchPlaceholder": "Search...", "paymentHistorySummaryDeposits": "Total Deposits", "paymentHistorySummaryNet": "Net Balance Change", "paymentHistorySummaryWithdrawals": "Total Withdrawals", "paymentHistoryTableActions": "Actions", "paymentHistoryTableAmount": "Amount (€)", "paymentHistoryTableChild": "Child", "paymentHistoryTableDate": "Date", "paymentHistoryTableDesc": "Description", "paymentHistoryTableType": "Type", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "<PERSON><PERSON><PERSON>", "preferredStudioDesc": "This studio will be pre-selected when booking activities", "preferredStudioTitle": "Preferred Learning Studio", "regionDisabledText": "Region not applicable for selected country", "regionLabel": "Region", "schoolTypeLabel": "School Type", "selectChildCurrentBudgetLabel": "Current Budget:", "selectChildFundsCancelButton": "Cancel", "selectChildFundsModalTitle": "Select Child to Add Funds", "selectChildFundsPrompt": "Choose which child's account to top up:", "selectCountryFirst": "Select country first", "selectPlaceholder": "Select...", "selectStudioPlaceholder": "-- Select a Studio --", "statusAddChildError": "Error adding child: {0}", "statusAddChildSuccess": "Child added successfully", "statusAddFundsError": "Error adding funds: {0}", "statusAddFundsSuccess": "Successfully added €{0} to {1}'s account", "statusAuthRequired": "Authentication required.", "statusDeleteChildConfirm": "Are you sure you want to delete child ID {0}? This cannot be undone.", "statusDeleteChildError": "Error deleting child: {0}", "statusDeleteChildSuccess": "Child deleted successfully", "statusErrorChildren": "Error fetching children", "statusErrorInitialData": "Error fetching initial data: {0}", "statusErrorPreferredStudio": "Error fetching preferred studio", "statusErrorStudios": "Error fetching studios", "statusFindChildError": "Could not find child details.", "statusLoading": "Loading...", "statusUnexpectedError": "An unexpected error occurred.", "statusUpdateChildSuccess": "Child details updated successfully.", "statusUpdatePreferredError": "Error updating preferred studio: {0}", "statusUpdatePreferredSuccess": "Preferred studio updated successfully", "subjectsLabel": "Subjects", "totalBudgetCardTitle": "Total Budget", "welcomeMessage": "Welcome"}, "helpModal": {"bookingProcessIntro": "For at booke en læringssession:", "bookingProcessLi1": "Vælg et barn fra dashboardet", "bookingProcessLi2": "Klik på kalenderikonet for at åbne bookingvinduet", "bookingProcessLi3": "Vælg en dato, tid og aktivitetstype", "bookingProcessLi4": "Bekræft bookingen (budget vil automatisk blive fratrukket)", "bookingProcessLi5": "Følg alle bookinger i Køb-sektionen", "bookingProcessTitle": "Bookingproces", "contactSupportButton": "Kontakt Support", "faq1Answer": "Brug knappen \"Tilføj Midler\" i dashboardet for at fylde dit barns læringsbudget op. Du kan bruge kreditkort, PayPal eller bankoverførsel.", "faq1Question": "<PERSON><PERSON><PERSON> tilføjer jeg midler til mit barns konto?", "faq2Answer": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du opretter en barnekonto, opsætter du et brugernavn og en adgangskode. Dit barn kan bruge disse legitimationsoplysninger til at få adgang til deres eget begrænsede dashboard.", "faq2Question": "Kan mit barn logge ind separat?", "faq3Answer": "<PERSON><PERSON><PERSON> til <PERSON>-<PERSON>, find den booking, du vil annullere, og klik på \"Se Detaljer\". Du finder en annulleringsmulighed, hvis sessionen er mere end 24 timer væk.", "faq3Question": "<PERSON><PERSON><PERSON> annullerer jeg en booking?", "faqTitle": "<PERSON><PERSON>", "gettingStartedLi1": "Til<PERSON><PERSON><PERSON> dine børn ved hjæ<PERSON>p af knappen \"Tilføj Nyt Barn\"", "gettingStartedLi2": "Indstil dit foretrukne læringsstudie i dashboardet", "gettingStartedLi3": "Administrer hvert barns budget til aktiviteter", "gettingStartedLi4": "Book læringssessioner og følg fremskridt", "gettingStartedTitle": "Kom i Gang", "gettingStartedWelcome": "Velkommen til Booking Parents-appen! Denne platform hjælper dig med at administrere dine børns læringsaktiviteter og bookinger på ét sted.", "managingChildrenAddBudget": "Tilføj Budget", "managingChildrenAddBudgetDesc": ": Fyld dit barns læringsbudget op", "managingChildrenBook": "Book", "managingChildrenBookDesc": ": Planlæg læringssessioner i dit foretrukne studie", "managingChildrenDelete": "Slet", "managingChildrenDeleteDesc": ": <PERSON><PERSON><PERSON> en barnek<PERSON>o (kan ikke fortrydes)", "managingChildrenEdit": "<PERSON><PERSON>", "managingChildrenEditDesc": ": <PERSON><PERSON><PERSON> navn, budget og bookingtilladelser", "managingChildrenIntro": "For hvert barn kan du:", "managingChildrenTitle": "Administration af Børn", "needMoreHelpEmail": "E-mail:", "needMoreHelpHours": "Åbningstider: Mandag-Fredag, 9:00-17:00 CET", "needMoreHelpIntro": "<PERSON><PERSON> du har brug for yderligere assistance, er vores supportteam her for at hjælpe:", "needMoreHelpPhone": "Telefon:", "needMoreHelpTitle": "Brug for Mere Hjælp?", "title": "Hjælpecenter"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Close", "companyInfoTitle": "Company Information", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Germany", "directorsLabel": "Managing Directors:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Despite careful content control, we assume no liability for the content of external links. The operators of the linked pages are solely responsible for their content.", "disclaimerTitle": "Disclaimer", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Phone:", "regNumLabel": "Registration Number:", "regNumValue": "HRB 123456", "registerLabel": "Commercial Register:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Regulatory Information", "responsibleLabel": "Responsible for content according to § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Imprint", "vatIdLabel": "VAT ID:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "Arabic", "bn": "Bengali", "cs": "Czech", "da": "Danish", "de": "German", "el": "Greek", "en": "English", "es": "Spanish", "fi": "Finnish", "fr": "French", "gu": "Gujarati", "he": "Hebrew", "hi": "Hindi", "hu": "Hungarian", "it": "Italian", "ja": "Japanese", "jv": "Javanese", "ko": "Korean", "mr": "Marathi", "nl": "Dutch", "no": "Norwegian", "pa": "Punjabi", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "sv": "Swedish", "ta": "Tamil", "te": "Telugu", "th": "Thai", "tr": "Turkish", "uk": "Ukrainian", "ur": "Urdu", "vi": "Vietnamese", "zh": "Chinese"}, "login": {"childProgress": "<PERSON><PERSON>", "easyScheduling": "<PERSON><PERSON>", "errors": {"connectionError": "Forbindelsesfejl, kontroller din internetforbindelse", "invalidCredentials": "Ugyldigt brugernavn eller adgangskode", "licenseValidationFailed": "Licensvalidering mislykkedes", "loginFailed": "<PERSON><PERSON> mislykkedes", "noActiveLicense": "Ingen aktiv licens fundet", "serverError": "<PERSON><PERSON><PERSON><PERSON>, prøv igen senere", "sessionExpired": "Session udl<PERSON><PERSON>, log venligst ind igen", "tryAgainLater": "Der opstod en fejl, prøv igen senere", "unableToVerifyLicense": "Kunne ikke verificere licens", "userNotFound": "Bruger ikke fundet", "wrongPassword": "<PERSON><PERSON>"}, "forgotPassword": "Glemt din adgangskode?", "languageSwitch": "Skift sprog", "loggingIn": "Logger ind...", "login": "Log ind", "password": "Adgangskode", "summary": "Book læringssessioner til dit barn og følg deres fremskridt på ét sted.", "title": "Log ind", "username": "Brugernavn"}, "privacyModal": {"acceptButton": "I Understand and Accept", "childInfoDesc": "Information about your children, including name, age, and learning preferences.", "childInfoTitle": "Child Information:", "childrenIntro": "Our service requires information about children to provide our core booking services. We take additional precautions to protect children's data:", "childrenLi1": "We collect only the minimum necessary information about children", "childrenLi2": "We require parental consent before collecting information from children", "childrenLi3": "We do not make children's personal information publicly available", "childrenLi4": "Parents can review, delete, or refuse further collection of their child's information", "childrenLi5": "We implement additional security measures for children's data", "childrenTitle": "Children's Privacy", "contactAddress": "Address:", "contactEmail": "Email:", "contactIntro": "If you have any questions about this Privacy Policy, please contact us:", "contactPhone": "Phone:", "contactTitle": "Contact Us", "cookiesDesc": "We use cookies and similar tracking technologies to track activity on our Platform and hold certain information.", "cookiesTitle": "Cookies and Tracking:", "howWeUseIntro": "We may use the information we collect about you for various purposes:", "howWeUseLi1": "To provide and maintain our service", "howWeUseLi2": "To notify you about changes to our service", "howWeUseLi3": "To allow you to participate in interactive features when you choose to do so", "howWeUseLi4": "To provide customer support", "howWeUseLi5": "To gather analysis or valuable information to improve our service", "howWeUseLi6": "To monitor the usage of our service", "howWeUseLi7": "To detect, prevent and address technical issues", "howWeUseLi8": "To process payments and prevent fraudulent transactions", "howWeUseLi9": "To contact you with newsletters, marketing or promotional materials and other information", "howWeUseTitle": "How We Use Your Information", "infoCollectedIntro": "We may collect information about you in various ways, including:", "infoCollectedTitle": "Information We Collect", "intro": "At Booking Parents, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.", "lastUpdated": "Last updated:", "paymentDataDesc": "We collect payment information when you make purchases through our platform, though payment card details are not stored on our servers.", "paymentDataTitle": "Payment Data:", "personalDataDesc": "While using our service, we may ask you to provide personally identifiable information that can be used to contact or identify you, including name, email address, phone number, and postal address.", "personalDataTitle": "Personal Data:", "rightsAccessDesc": "You have the right to request copies of your personal information.", "rightsAccessTitle": "Right to Access:", "rightsContact": "To exercise any of these rights, please contact <NAME_EMAIL>.", "rightsErasureDesc": "You have the right to request that we delete your personal information.", "rightsErasureTitle": "Right to Erasure:", "rightsIntro": "Depending on your location, you may have certain rights regarding your personal information:", "rightsObjectDesc": "You have the right to object to our processing of your personal information.", "rightsObjectTitle": "Right to Object:", "rightsPortabilityDesc": "You have the right to request that we transfer your information to another organization or directly to you.", "rightsPortabilityTitle": "Right to Data Portability:", "rightsRectificationDesc": "You have the right to request that we correct inaccurate information about you.", "rightsRectificationTitle": "Right to Rectification:", "rightsRestrictDesc": "You have the right to request that we restrict the processing of your information.", "rightsRestrictTitle": "Right to Restrict Processing:", "rightsTitle": "Your Privacy Rights", "securityIntro1": "The security of your data is important to us but remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.", "securityIntro2": "Our security measures include:", "securityLi1": "Encryption of sensitive data in transit and at rest", "securityLi2": "Regular security assessments and audits", "securityLi3": "Employee training on data protection", "securityLi4": "Access controls and authentication requirements", "securityLi5": "Physical security measures for our facilities", "securityTitle": "Data Security", "sharingBusinessTransfersDesc": "In connection with any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company.", "sharingBusinessTransfersTitle": "Business Transfers:", "sharingConsentDesc": "We may disclose your personal information for any other purpose with your consent.", "sharingConsentTitle": "With Your Consent:", "sharingIntro": "We may share your personal information in the following situations:", "sharingLegalDesc": "If required to do so by law or in response to valid requests by public authorities.", "sharingLegalTitle": "Legal Requirements:", "sharingServiceProvidersDesc": "We may share your information with third-party service providers to facilitate our Service, provide the Service on our behalf, or perform service-related services.", "sharingServiceProvidersTitle": "With Service Providers:", "sharingStudiosDesc": "We share necessary information with learning studios to facilitate your bookings and learning sessions.", "sharingStudiosTitle": "With Learning Studios:", "sharingTitle": "Information Sharing and Disclosure", "title": "Privacy Policy", "usageDataDesc": "Information on how the Service is accessed and used, including your computer's Internet Protocol address, browser type, pages visited, time spent on those pages, and other diagnostic data.", "usageDataTitle": "Usage Data:"}, "settingsModal": {"appearanceSectionTitle": "Appearance", "bookingRemindersLabel": "Booking Reminders", "cancelButton": "Cancel", "changePasswordButton": "Change Password", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Dark Mode", "editProfileButton": "Edit Profile", "enableNotificationsLabel": "Enable Notifications", "fontSizeLabel": "Font Size", "highContrastLabel": "High Contrast", "langArabic": "Arabic", "langChinese": "Chinese", "langDutch": "Dutch", "langEnglish": "English", "langFrench": "French", "langGerman": "German", "langHebrew": "Hebrew", "langHindi": "Hindi", "langJapanese": "Japanese", "langSpanish": "Spanish", "languageLabel": "Language", "lowBudgetAlertsLabel": "Low Budget Alerts", "marketingUpdatesLabel": "Marketing Updates", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Preferences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Your Name", "profileSectionTitle": "Profile", "saveButton": "Save Changes", "timezoneLabel": "Time Zone", "title": "Settings", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biology", "Business Studies": "Business Studies", "Chemistry": "Chemistry", "Computer Science": "Computer Science", "Design and Technology": "Design and Technology", "Drama": "Drama", "Economics": "Economics", "English": "English", "Foreign Languages": "Foreign Languages", "Geography": "Geography", "History": "History", "Mathematics": "Mathematics", "Music": "Music", "Philosophy": "Philosophy", "Physical Education": "Physical Education", "Physics": "Physics", "Psychology": "Psychology", "Religious Studies": "Religious Studies", "Social Studies": "Social Studies"}, "purchases": {"purchaseHistory": "Købshistorik", "viewAndManage": "Se og administrer alle køb og bookinger", "downloadReport": "Download rapport", "totalSpent": "Samlet forbrug", "allCompletedPurchases": "Alle gennemførte køb", "totalPurchases": "<PERSON><PERSON><PERSON> køb", "acrossAllChildren": "På tværs af alle børn", "pending": "<PERSON><PERSON><PERSON><PERSON>", "awaitingConfirmation": "A<PERSON>venter bekræftelse", "transactions": "Transaktioner", "all": "Alle", "completed": "Gennemført", "failed": "Mislykket", "id": "ID", "product": "Produkt", "child": "<PERSON>n", "date": "Da<PERSON>", "amount": "<PERSON><PERSON><PERSON>", "status": "Status", "action": "Handling", "viewDetails": "<PERSON> <PERSON><PERSON><PERSON>", "noPurchasesFound": "Ingen køb fundet", "noPurchasesYet": "Du har ikke foretaget nogen køb endnu. Gennemse vores produkter for at komme i gang.", "noPurchasesFiltered": "Du har ikke nogen {filter} køb i øjeblikket.", "selectChild": "Vælg barn", "allChildren": "<PERSON><PERSON> børn", "filterByChild": "Filtrer efter barn", "purchaseDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "markAsCompleted": "<PERSON><PERSON> som gennemført", "markAsFailed": "Marker som mislykket"}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "Sidst opdateret"}, "eventTypes": {"achievement": "Præstation", "milestone": "<PERSON><PERSON><PERSON><PERSON>", "assessment": "Vurdering", "test": "Test", "exam": "Eksamen", "report": "Rapport", "presentation": "Præsentation", "project": "Projekt", "workshop": "Workshop", "other": "And<PERSON>"}, "Certificates": {"certificateDetailsTitle": "Certifika<PERSON><PERSON><PERSON><PERSON>", "certificateImageLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "closeButton": "Luk", "imageLoadError": "<PERSON><PERSON><PERSON> kunne ikke indlæses", "titleLabel": "Certifikattitel", "issueDateLabel": "Udstedelsesdato", "issueDateISOLabel": "Udstedelsesdato (ISO)", "relatedSkillsLabel": "<PERSON><PERSON><PERSON><PERSON>", "certificateIdLabel": "Certifikat-ID"}}}