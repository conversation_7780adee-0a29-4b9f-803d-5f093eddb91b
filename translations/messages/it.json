{"common": {"appName": "App Genitori", "betaTag": "BETA", "home": "Home", "logout": "<PERSON><PERSON><PERSON>", "shop": "Negozio"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Belgium", "Brazil": "Brazil", "Bulgaria": "Bulgaria", "Canada": "Canada", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Denmark", "Finland": "Finland", "France": "France", "Germany": "Germany", "India": "India", "Italy": "Italy", "Japan": "Japan", "Luxembourg": "Luxembourg", "Mexico": "Mexico", "Netherlands": "Netherlands", "New Zealand": "New Zealand", "Norway": "Norway", "Russia": "Russia", "Singapore": "Singapore", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Sweden": "Sweden", "Switzerland": "Switzerland", "United Kingdom": "United Kingdom", "United States": "United States", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Tutti i diritti riservati.", "footerImprint": "Informazioni Legali", "footerPrivacy": "Privacy", "navDashboard": "Dashboard", "navLearningProgress": "Progressi di Apprendimento", "navLogout": "<PERSON><PERSON><PERSON>", "navPurchases": "<PERSON><PERSON><PERSON><PERSON>", "navShop": "Negozio", "navStudios": "<PERSON><PERSON>"}, "dashboardPage": {"addChildBookingPermissionLabel": "Consentire al Bambino di Prenotare Sessioni?", "addChildBudgetLabel": "Budget Iniziale (€)", "addChildBudgetPlaceholder": "es., 50.00", "addChildButton": "Aggiungi Nuovo Bambino", "addChildCancelButton": "<PERSON><PERSON><PERSON>", "addChildCardDesc": "Crea un nuovo account bambino da gestire", "addChildModalTitle": "Aggiungi Nuovo Bambino", "addChildNameLabel": "Nome del Bambino", "addChildPasswordLabel": "Password del Bambino", "addChildPasswordPlaceholder": "Crea password", "addChildSubmitButton": "Aggiungi Bambino", "addChildUsernameLabel": "Nome Utente del Bambino", "addChildUsernamePlaceholder": "Crea nome utente unico", "addFirstChildButton": "Aggiungi il Tuo Primo Bambino", "addFundsAmountLabel": "Importo (€)", "addFundsButton": "Aggiungi Fondi", "addFundsCancelButton": "<PERSON><PERSON><PERSON>", "addFundsHelpText": "Inserisci l'importo che vuoi aggiungere al budget del bambino. I fondi saranno disponibili immediatamente.", "addFundsModalTitle": "Aggiungi Fondi all'Account di {0}", "addFundsSubmitButton": "Aggiungi Fondi", "bookingAlertEventAdded": "Nuovo evento \"{0}\" aggiunto!", "bookingAlertEventMoved": "{0} è stato spostato a {1}", "bookingAlertEventResized": "{0} è stato ridimensionato per terminare alle {1}", "bookingAlertSelectionCancelled": "Selezione annullata.", "bookingConfirmViewDetails": "Visualizza dettagli per \"{0}\"? \nDescrizione: {1}", "bookingConfirmation": {"availability": "Disponibilità", "available": "Disponibile", "biweekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bookingConflictDetailed": "<PERSON><PERSON><PERSON><PERSON> conflitto di prenotazione per {childName} in {roomName}. L'orario richiesto ({requestedTime}) è in conflitto con una prenotazione esistente ({conflictingTime}). Seleziona un altro orario.", "bookingCost": "Costo Prenotazione", "bookingDetails": "Dettagli Prenotazione", "bookingFailed": "Prenotazione Fallita", "budgetInformation": "Informazioni Budget", "cancelButton": "<PERSON><PERSON><PERSON>", "checking": "Verifica in corso...", "child": "Bambino", "confirmButton": "Conferma Prenotazione", "currentBudget": "Budget Attuale", "date": "Data", "deleteSuccessDescription": "La tua prenotazione è stata eliminata.", "deleteSuccessMessage": "Prenotazione Eliminata con Successo!", "duration": "<PERSON><PERSON>", "enableRecurringBooking": "Abilita Prenotazione Ricorrente", "error": "Errore", "errorLoadingBudget": "Errore nel caricamento delle informazioni sul budget", "friday": "<PERSON>ener<PERSON><PERSON>", "hours": "ore", "insufficientBudget": "Budget Insufficiente", "insufficientBudgetMessage": "Il bambino non ha budget sufficiente per questa prenotazione. Aggiungi fondi o seleziona un orario più breve.", "monday": "Lunedì", "monthly": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "notAvailable": "Non Disponibile", "numberOfBookings": "Numero di Prenotazioni", "numberOfOccurrences": "Numero di Occorrenze", "pricePerHour": "Prezzo per ora", "processing": "Elaborazione in corso...", "recurrenceType": "Tipo di Ricorrenza", "recurringBooking": "Prenotazione Ricorrente", "remainingBudget": "Budget <PERSON><PERSON>", "room": "<PERSON><PERSON>", "saturday": "Sabato", "selectDaysOfWeek": "Seleziona Giorni della Settimana", "selectType": "Seleziona Tipo", "singleBookingCost": "Costo Singola Prenotazione", "studio": "Studio", "successDescription": "La tua prenotazione è stata confermata.", "successMessage": "Prenotazione Effettuata con Successo!", "sunday": "Domenica", "thursday": "<PERSON><PERSON><PERSON><PERSON>", "time": "Orario", "timeSlotBooked": "Questo orario è già prenotato. Seleziona un altro orario.", "title": "Conferma Prenotazione", "totalPrice": "Prezzo Totale", "tryAgainMessage": "Prova a selezionare un orario diverso o contatta l'assistenza se il problema persiste.", "tuesday": "Martedì", "wednesday": "Mercoledì", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "weeklyRecurrenceInfo": "La prenotazione si ripeterà nei giorni selezionati per il numero specificato di settimane.", "outsideOpeningHours": "Le prenotazioni sono possibili solo durante l'orario di apertura dello studio.", "recurringBookingCapacityExceeded": "Una o più delle prenotazioni ricorrenti supererebbe la capacità della stanza. Si prega di ridurre il numero di occorrenze o scegliere un altro orario.", "biweeklyRecurrenceInfo": "La prenotazione si ripeterà ogni due settimane nello stesso giorno per il numero specificato di occorrenze.", "limitedByBudget": "Limitato dal budget", "limitedByCapacity": "Limitato dallo spazio disponibile"}, "bookingDescriptionNone": "<PERSON><PERSON><PERSON>", "bookingModalTitle": "Prenota Sessione per {0}", "bookingPromptEventTitle": "Inserisci un titolo per il tuo evento:", "childBookAction": "Prenota appuntamento", "childBookDisabled": "Prenotazione disabilitata", "childBudgetAvailable": "Disponibile", "childBudgetEmpty": "<PERSON><PERSON><PERSON>", "childCardAddFundsButton": "Aggiungi Fondi", "childCardBookButton": "Prenota Sessione", "childCardBookingLabel": "Prenotazione Consentita:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Elimina", "childCardEditButton": "Modifica", "childCardNameLabel": "Nome:", "childCardUsernameLabel": "Nome Utente:", "childStatusCanBook": "Il Bambino Può Prenotare", "childStatusNoBooking": "Solo Prenotazione Genitore", "childrenCardDescPlural": "{count, plural, one {# bambino gestito} other {# bambini gestiti}}", "childrenCardDescSingular": "1 account bambino attivo", "childrenCardTitle": "Bambini", "classLabel": "Classe/Grado", "countryLabel": "<PERSON><PERSON>", "editChildAllowBookingsDescription": "Se selezionato, il bambino può accedere e prenotare sessioni autonomamente utilizzando il proprio budget. Se deselezionato, solo i genitori possono prenotare appuntamenti per questo bambino. I genitori possono sempre prenotare appuntamenti per i loro figli indipendentemente da questa impostazione.", "editChildAllowBookingsLabel": "Consentire al Bambino di Prenotare Sessioni?", "editChildCancelButtonText": "<PERSON><PERSON><PERSON>", "editChildNameLabel": "Nome del Bambino", "editChildNamePlaceholder": "Inserisci il nome del bambino", "editChildPasswordHelpText": "In<PERSON><PERSON><PERSON> solo se vuoi cambiare la password del bambino.", "editChildPasswordLabelOptional": "Nuova Password (Opzionale)", "editChildPasswordPlaceholderOptional": "<PERSON><PERSON> vuoto per mantenere quella attuale", "editChildSaveButtonText": "<PERSON><PERSON>", "editChildTitle": "Modifica Dettagli Bambino", "editChildUpdateErrorMessage": "Errore nell'aggiornamento dei dettagli del bambino. Riprova.", "editChildUpdateSuccessMessage": "Dettagli del bambino aggiornati con successo!", "editChildUpdatingButtonText": "Aggiornamento in corso...", "languageLabel": "<PERSON><PERSON>", "managePrompt": "Gestisci gli account dei tuoi bambini e prenota sessioni di apprendimento", "myChildrenTitle": "<PERSON> <PERSON><PERSON>", "noChildrenFoundDesc": "Usa il modulo qui sotto per aggiungere il tuo primo account bambino e iniziare a gestire le prenotazioni", "noChildrenFoundTitle": "<PERSON><PERSON><PERSON> bambino trovato", "paymentHistoryButton": "Cronologia Pagamenti", "paymentHistoryCloseButton": "<PERSON><PERSON>", "paymentHistoryExportBills": "Scarica <PERSON> le Fatture", "paymentHistoryExportButton": "Opzioni di Esportazione", "paymentHistoryExportCsv": "Esporta come CSV", "paymentHistoryFilterAll": "<PERSON>tte le Transazioni", "paymentHistoryFilterDeposits": "Solo Depositi", "paymentHistoryFilterWithdrawals": "Solo Prelievi", "paymentHistoryModalTitle": "Cronologia Pagamenti", "paymentHistorySearchPlaceholder": "Cerca...", "paymentHistorySummaryDeposits": "Totale Depositi", "paymentHistorySummaryNet": "Variazione Saldo Netto", "paymentHistorySummaryWithdrawals": "Totale Prelievi", "paymentHistoryTableActions": "Azioni", "paymentHistoryTableAmount": "Importo (€)", "paymentHistoryTableChild": "Bambino", "paymentHistoryTableDate": "Data", "paymentHistoryTableDesc": "Descrizione", "paymentHistoryTableType": "Tipo", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "Prelievo", "preferredStudioDesc": "Questo studio sarà preselezionato quando prenoti attività", "preferredStudioTitle": "Studio di Apprendimento Preferito", "regionDisabledText": "Regione non applicabile per il paese selezionato", "regionLabel": "Regione", "schoolTypeLabel": "Tipo di Scuola", "selectChildCurrentBudgetLabel": "Budget Attuale:", "selectChildFundsCancelButton": "<PERSON><PERSON><PERSON>", "selectChildFundsModalTitle": "Seleziona Bambino per Aggiungere Fondi", "selectChildFundsPrompt": "<PERSON><PERSON><PERSON> a quale account bambino aggiungere fondi:", "selectCountryFirst": "Seleziona prima il paese", "selectPlaceholder": "Seleziona...", "selectStudioPlaceholder": "-- Seleziona uno Studio --", "statusAddChildError": "Errore nell'aggiunta del bambino: {0}", "statusAddChildSuccess": "Bambino aggiunto con successo", "statusAddFundsError": "Errore nell'aggiunta dei fondi: {0}", "statusAddFundsSuccess": "Aggiunti con successo €{0} all'account di {1}", "statusAuthRequired": "Autenticazione richiesta.", "statusDeleteChildConfirm": "Sei sicuro di voler eliminare il bambino con ID {0}? Questa azione non può essere annullata.", "statusDeleteChildError": "Errore nell'eliminazione del bambino: {0}", "statusDeleteChildSuccess": "Bambino eliminato con successo", "statusErrorChildren": "Errore nel recupero dei bambini", "statusErrorInitialData": "Errore nel recupero dei dati iniziali: {0}", "statusErrorPreferredStudio": "Errore nel recupero dello studio preferito", "statusErrorStudios": "Errore nel recupero degli studi", "statusFindChildError": "Impossibile trovare i dettagli del bambino.", "statusLoading": "Caricamento in corso...", "statusUnexpectedError": "Si è verificato un errore imprevisto.", "statusUpdateChildSuccess": "Dettagli del bambino aggiornati con successo.", "statusUpdatePreferredError": "Errore nell'aggiornamento dello studio preferito: {0}", "statusUpdatePreferredSuccess": "Studio preferito aggiornato con successo", "subjectsLabel": "Materie", "totalBudgetCardTitle": "Budget Totale", "welcomeMessage": "<PERSON><PERSON><PERSON>"}, "helpModal": {"bookingProcessIntro": "Per prenotare una sessione di apprendimento:", "bookingProcessLi1": "Seleziona un bambino dalla dashboard", "bookingProcessLi2": "Clicca sull'icona del calendario per aprire la finestra di prenotazione", "bookingProcessLi3": "Scegli una data, un orario e un tipo di attività", "bookingProcessLi4": "Conferma la prenotazione (il budget sarà automaticamente detratto)", "bookingProcessLi5": "Tieni traccia di tutte le prenotazioni nella sezione Acquisti", "bookingProcessTitle": "Processo di Prenotazione", "contactSupportButton": "Contatta l'Assistenza", "faq1Answer": "Utilizza il pulsante \"Aggiungi Fondi\" nella dashboard per ricaricare il budget di apprendimento di tuo figlio. Puoi utilizzare carta di credito, PayPal o bonifico bancario.", "faq1Question": "Come aggiungo fondi all'account di mio figlio?", "faq2Answer": "Sì. Quando crei un account bambino, imposti un nome utente e una password. Tuo figlio può utilizzare queste credenziali per accedere alla propria dashboard limitata.", "faq2Question": "Mio figlio può accedere separatamente?", "faq3Answer": "Vai alla pagina A<PERSON>i, trova la prenotazione che desideri annullare e clicca su \"Visualizza Dettagli\". Troverai un'opzione di annullamento se la sessione è a più di 24 ore di distanza.", "faq3Question": "Come annullo una prenotazione?", "faqTitle": "<PERSON><PERSON><PERSON>", "gettingStartedLi1": "Aggiungi i tuoi bambini utilizzando il pulsante \"Aggiungi Nuovo Bambino\"", "gettingStartedLi2": "Imposta il tuo studio di apprendimento preferito nella dashboard", "gettingStartedLi3": "Gestisci il budget di ogni bambino per le attività", "gettingStartedLi4": "Prenota sessioni di apprendimento e tieni traccia dei progressi", "gettingStartedTitle": "<PERSON>", "gettingStartedWelcome": "Benvenuto nell'app Booking Parents! Questa piattaforma ti aiuta a gestire le attività di apprendimento e le prenotazioni dei tuoi figli in un unico posto.", "managingChildrenAddBudget": "Aggiungi Budget", "managingChildrenAddBudgetDesc": ": Ricarica il budget di apprendimento di tuo figlio", "managingChildrenBook": "Prenota", "managingChildrenBookDesc": ": Programma sessioni di apprendimento nel tuo studio preferito", "managingChildrenDelete": "Elimina", "managingChildrenDeleteDesc": ": <PERSON><PERSON><PERSON><PERSON> un account bambino (non può essere annullato)", "managingChildrenEdit": "Modifica", "managingChildrenEditDesc": ": Aggiorna nome, budget e permessi di prenotazione", "managingChildrenIntro": "Per <PERSON>ni bam<PERSON>, puoi:", "managingChildrenTitle": "Gestione dei Bambini", "needMoreHelpEmail": "Email:", "needMoreHelpHours": "Orari: Lunedì-Venerdì, 9:00-17:00 CET", "needMoreHelpIntro": "Se hai bisogno di ulteriore assistenza, il nostro team di supporto è qui per aiutarti:", "needMoreHelpPhone": "Telefono:", "needMoreHelpTitle": "Hai Bisogno di Ulteriore Aiuto?", "title": "Centro Assistenza"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Close", "companyInfoTitle": "Company Information", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Germany", "directorsLabel": "Managing Directors:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Despite careful content control, we assume no liability for the content of external links. The operators of the linked pages are solely responsible for their content.", "disclaimerTitle": "Disclaimer", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Phone:", "regNumLabel": "Registration Number:", "regNumValue": "HRB 123456", "registerLabel": "Commercial Register:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Regulatory Information", "responsibleLabel": "Responsible for content according to § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Imprint", "vatIdLabel": "VAT ID:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "<PERSON><PERSON>", "bn": "Bengalese", "cs": "Ceco", "da": "<PERSON><PERSON>", "de": "Tedesco", "el": "Greco", "en": "<PERSON><PERSON><PERSON>", "es": "<PERSON><PERSON><PERSON>", "fi": "Finlandese", "fr": "<PERSON><PERSON>", "gu": "Gujarati", "he": "Ebraico", "hi": "Hindi", "hu": "<PERSON><PERSON><PERSON><PERSON>", "it": "Italiano", "ja": "Giapponese", "jv": "Giavanese", "ko": "<PERSON><PERSON>", "mr": "Marathi", "nl": "<PERSON><PERSON><PERSON>", "no": "Norvegese", "pa": "Punjabi", "pl": "Polacco", "pt": "<PERSON><PERSON><PERSON>", "ro": "R<PERSON>no", "ru": "<PERSON>", "sv": "<PERSON><PERSON><PERSON>", "ta": "Tamil", "te": "Telugu", "th": "Tailandese", "tr": "<PERSON><PERSON><PERSON>", "uk": "Ucraino", "ur": "Urdu", "vi": "Vietnamita", "zh": "Cinese"}, "login": {"childProgress": "Progressi del Bambino", "easyScheduling": "Programmazione Facile", "errors": {"connectionError": "Errore di connessione, controlla la tua connessione internet", "invalidCredentials": "Nome utente o password non validi", "licenseValidationFailed": "Validazione della licenza fallita", "loginFailed": "<PERSON>o fallito", "noActiveLicense": "Nessuna licenza attiva trovata", "serverError": "Errore del server, riprova più tardi", "sessionExpired": "Sessione scaduta, effettua nuovamente l'accesso", "tryAgainLater": "Si è verificato un errore, riprova più tardi", "unableToVerifyLicense": "Impossibile verificare la licenza", "userNotFound": "Utente non trovato", "wrongPassword": "Password errata"}, "forgotPassword": "Hai dimenticato la password?", "languageSwitch": "Cambia lingua", "loggingIn": "Accesso in corso...", "login": "Accedi", "password": "Password", "summary": "Prenota sessioni di apprendimento per tuo figlio e tieni traccia dei suoi progressi in un unico posto.", "title": "Accesso", "username": "Nome utente"}, "privacyModal": {"acceptButton": "I Understand and Accept", "childInfoDesc": "Information about your children, including name, age, and learning preferences.", "childInfoTitle": "Child Information:", "childrenIntro": "Our service requires information about children to provide our core booking services. We take additional precautions to protect children's data:", "childrenLi1": "We collect only the minimum necessary information about children", "childrenLi2": "We require parental consent before collecting information from children", "childrenLi3": "We do not make children's personal information publicly available", "childrenLi4": "Parents can review, delete, or refuse further collection of their child's information", "childrenLi5": "We implement additional security measures for children's data", "childrenTitle": "Children's Privacy", "contactAddress": "Address:", "contactEmail": "Email:", "contactIntro": "If you have any questions about this Privacy Policy, please contact us:", "contactPhone": "Phone:", "contactTitle": "Contact Us", "cookiesDesc": "We use cookies and similar tracking technologies to track activity on our Platform and hold certain information.", "cookiesTitle": "Cookies and Tracking:", "howWeUseIntro": "We may use the information we collect about you for various purposes:", "howWeUseLi1": "To provide and maintain our service", "howWeUseLi2": "To notify you about changes to our service", "howWeUseLi3": "To allow you to participate in interactive features when you choose to do so", "howWeUseLi4": "To provide customer support", "howWeUseLi5": "To gather analysis or valuable information to improve our service", "howWeUseLi6": "To monitor the usage of our service", "howWeUseLi7": "To detect, prevent and address technical issues", "howWeUseLi8": "To process payments and prevent fraudulent transactions", "howWeUseLi9": "To contact you with newsletters, marketing or promotional materials and other information", "howWeUseTitle": "How We Use Your Information", "infoCollectedIntro": "We may collect information about you in various ways, including:", "infoCollectedTitle": "Information We Collect", "intro": "At Booking Parents, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.", "lastUpdated": "Last updated:", "paymentDataDesc": "We collect payment information when you make purchases through our platform, though payment card details are not stored on our servers.", "paymentDataTitle": "Payment Data:", "personalDataDesc": "While using our service, we may ask you to provide personally identifiable information that can be used to contact or identify you, including name, email address, phone number, and postal address.", "personalDataTitle": "Personal Data:", "rightsAccessDesc": "You have the right to request copies of your personal information.", "rightsAccessTitle": "Right to Access:", "rightsContact": "To exercise any of these rights, please contact <NAME_EMAIL>.", "rightsErasureDesc": "You have the right to request that we delete your personal information.", "rightsErasureTitle": "Right to Erasure:", "rightsIntro": "Depending on your location, you may have certain rights regarding your personal information:", "rightsObjectDesc": "You have the right to object to our processing of your personal information.", "rightsObjectTitle": "Right to Object:", "rightsPortabilityDesc": "You have the right to request that we transfer your information to another organization or directly to you.", "rightsPortabilityTitle": "Right to Data Portability:", "rightsRectificationDesc": "You have the right to request that we correct inaccurate information about you.", "rightsRectificationTitle": "Right to Rectification:", "rightsRestrictDesc": "You have the right to request that we restrict the processing of your information.", "rightsRestrictTitle": "Right to Restrict Processing:", "rightsTitle": "Your Privacy Rights", "securityIntro1": "The security of your data is important to us but remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.", "securityIntro2": "Our security measures include:", "securityLi1": "Encryption of sensitive data in transit and at rest", "securityLi2": "Regular security assessments and audits", "securityLi3": "Employee training on data protection", "securityLi4": "Access controls and authentication requirements", "securityLi5": "Physical security measures for our facilities", "securityTitle": "Data Security", "sharingBusinessTransfersDesc": "In connection with any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company.", "sharingBusinessTransfersTitle": "Business Transfers:", "sharingConsentDesc": "We may disclose your personal information for any other purpose with your consent.", "sharingConsentTitle": "With Your Consent:", "sharingIntro": "We may share your personal information in the following situations:", "sharingLegalDesc": "If required to do so by law or in response to valid requests by public authorities.", "sharingLegalTitle": "Legal Requirements:", "sharingServiceProvidersDesc": "We may share your information with third-party service providers to facilitate our Service, provide the Service on our behalf, or perform service-related services.", "sharingServiceProvidersTitle": "With Service Providers:", "sharingStudiosDesc": "We share necessary information with learning studios to facilitate your bookings and learning sessions.", "sharingStudiosTitle": "With Learning Studios:", "sharingTitle": "Information Sharing and Disclosure", "title": "Privacy Policy", "usageDataDesc": "Information on how the Service is accessed and used, including your computer's Internet Protocol address, browser type, pages visited, time spent on those pages, and other diagnostic data.", "usageDataTitle": "Usage Data:"}, "settingsModal": {"appearanceSectionTitle": "Appearance", "bookingRemindersLabel": "Booking Reminders", "cancelButton": "Cancel", "changePasswordButton": "Change Password", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Dark Mode", "editProfileButton": "Edit Profile", "enableNotificationsLabel": "Enable Notifications", "fontSizeLabel": "Font Size", "highContrastLabel": "Alto Contrasto", "langArabic": "<PERSON><PERSON>", "langChinese": "Cinese", "langDutch": "<PERSON><PERSON><PERSON>", "langEnglish": "<PERSON><PERSON><PERSON>", "langFrench": "<PERSON><PERSON>", "langGerman": "Tedesco", "langHebrew": "Ebraico", "langHindi": "Hindi", "langJapanese": "Giapponese", "langSpanish": "<PERSON><PERSON><PERSON>", "languageLabel": "<PERSON><PERSON>", "lowBudgetAlertsLabel": "Low Budget Alerts", "marketingUpdatesLabel": "Marketing Updates", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Preferences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Your Name", "profileSectionTitle": "Profile", "saveButton": "Save Changes", "timezoneLabel": "Time Zone", "title": "Settings", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biology", "Business Studies": "Business Studies", "Chemistry": "Chemistry", "Computer Science": "Computer Science", "Design and Technology": "Design and Technology", "Drama": "Drama", "Economics": "Economics", "English": "English", "Foreign Languages": "Foreign Languages", "Geography": "Geography", "History": "History", "Mathematics": "Mathematics", "Music": "Music", "Philosophy": "Philosophy", "Physical Education": "Physical Education", "Physics": "Physics", "Psychology": "Psychology", "Religious Studies": "Religious Studies", "Social Studies": "Social Studies"}, "purchases": {"purchaseHistory": "Cronologia Acquisti", "viewAndManage": "Visualizza e gestisci tutti gli acquisti e le prenotazioni", "downloadReport": "Scarica Report", "totalSpent": "Totale Speso", "allCompletedPurchases": "<PERSON><PERSON> gli Acquisti Completati", "totalPurchases": "Acquisti Totali", "acrossAllChildren": "<PERSON> Bambini", "pending": "In Attesa", "awaitingConfirmation": "In Attesa di Conferma", "transactions": "Transazioni", "all": "<PERSON><PERSON>", "completed": "Completate", "failed": "Fallite", "id": "ID", "product": "<PERSON><PERSON><PERSON>", "child": "Bambino", "date": "Data", "amount": "Importo", "status": "Stato", "action": "Azione", "viewDetails": "Visualizza Dettagli", "noPurchasesFound": "<PERSON><PERSON><PERSON> a<PERSON>o trovato", "noPurchasesYet": "Non hai ancora effettuato acquisti. Sfoglia i nostri prodotti per iniziare.", "noPurchasesFiltered": "Non hai acquisti {filter} al momento.", "selectChild": "Seleziona Bambino", "allChildren": "Tutti i Bambini", "filterByChild": "Filtra per Bambino", "purchaseDetails": "<PERSON><PERSON><PERSON>", "actions": "Azioni", "markAsCompleted": "Segna come Completato", "markAsFailed": "Segna come Fallito"}, "learningDocumentation": {"pageTitle": "Documentazione di Apprendimento", "timeframeAllTime": "<PERSON>tto il periodo", "timeframePastMonth": "<PERSON><PERSON><PERSON> mese", "timeframePastQuarter": "Ultimi 3 mesi", "timeframePastYear": "<PERSON><PERSON><PERSON> anno", "exportReportButton": "Esporta rapporto", "selectChildTitle": "<PERSON><PERSON><PERSON><PERSON> bam<PERSON>", "allChildrenButton": "<PERSON><PERSON> i bambini", "overallProgressTitle": "Progresso complessivo", "progressTowardMastery": "Progresso verso la padronanza", "recentAchievementsTitle": "Risultati recenti", "viewFullTimelineButton": "Visualizza cronologia completa", "skillsOverviewTitle": "Panoramica delle competenze", "skillsSummaryText": "Il riepilogo delle competenze padroneggiate e in corso sarà visualizzato qui.", "viewAllSkillsButton": "Visualizza tutte le competenze", "certificatesTitle": "Certificati", "certificatesSummaryText": "Il riepilogo dei certificati ottenuti sarà visualizzato qui.", "viewAllCertificatesButton": "Visualizza tutti i certificati", "documentationTitle": "Panoramica della Documentazione", "generalNotesLabel": "Note Generali", "learningGoalsLabel": "Obiettivi di Apprendimento", "strengthsLabel": "Punti di Forza", "areasForImprovementLabel": "Aree di Miglioramento", "noDocumentationMessage": "Non è stata ancora aggiunta documentazione per questo bambino.", "eventTypes": {"achievement": "Risultato", "milestone": "Pietra miliare", "assessment": "Valutazione", "test": "Test", "exam": "Esame", "report": "Rapporto", "presentation": "Presentazione", "project": "Progetto", "workshop": "Workshop", "other": "Altro"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "Ultimo aggiornamento"}, "Timeline": {"pageTitle": "Cronologia di Apprendimento", "timeframeAllTime": "<PERSON><PERSON> il Tempo", "timeframePastMonth": "<PERSON><PERSON><PERSON>", "timeframePastQuarter": "Ultimi 3 Mesi", "timeframePastYear": "<PERSON><PERSON><PERSON>", "exportTimelineButton": "Esporta Cronologia", "selectChildTitle": "Seleziona Bambino", "allChildrenButton": "Tutti i Bambini", "filterByEventTypeTitle": "Filtra per Tipo di Evento", "allEventsButton": "<PERSON><PERSON>", "learningEventsTitle": "Eventi di Apprendimento", "skillProgressLabel": "Progresso delle Competenze", "relatedSkillsLabel": "Competenze Correlate", "achievementLabel": "Risultato", "noEventsMatchMessage": "Nessun evento corrisponde ai filtri attuali.", "noEventsYetMessage": "Non sono stati registrati eventi di apprendimento per questo bambino.", "eventsCreatedInAssistantsMessage": "Gli eventi appariranno qui una volta creati nell'app degli assistenti.", "resetFiltersButton": "Reimposta filtri", "loading": "Caricamento eventi...", "error": "Errore durante il caricamento degli eventi. Riprova."}, "Certificates": {"pageTitle": "Certificati", "certificatesTitle": "Certificati e Risultati", "certificatesDescription": "Visualizza i certificati ottenuti dal tuo bambino", "selectChildTitle": "Seleziona Bambino", "allChildrenButton": "Tutti i Bambini", "downloadAllButton": "<PERSON><PERSON><PERSON>", "certificateLabel": "Certificato", "awardedToText": "Assegnato a", "viewDetailsButton": "Visualizza Dettagli", "downloadButton": "Scarica", "loading": "Caricamento certificati...", "error": "Errore durante il caricamento dei certificati. Riprova.", "noCertificatesFound": "<PERSON><PERSON><PERSON> certificato trovato per questo bambino.", "noCertificatesMessage": "Non sono ancora stati aggiunti certificati per questo bambino.", "certificateDetailsTitle": "Dettagli del Certificato", "certificateImageLabel": "Immagine del Certificato", "closeButton": "<PERSON><PERSON>", "imageLoadError": "Impossibile caricare l'immagine", "titleLabel": "Titolo del Certificato", "issueDateLabel": "Data di Emissione", "issueDateISOLabel": "Data di Emissione (ISO)", "relatedSkillsLabel": "Competenze Correlate", "certificateIdLabel": "ID del Certificato"}, "eventTypes": {"achievement": "Risultato", "milestone": "Pietra miliare", "assessment": "Valutazione", "test": "Test", "exam": "Esame", "report": "Rapporto", "presentation": "Presentazione", "project": "Progetto", "workshop": "Workshop", "other": "Altro"}}}