{"common": {"appName": "保護者アプリ", "betaTag": "ベータ", "home": "ホーム", "logout": "ログアウト", "shop": "ショップ"}, "countries": {"Argentina": "アルゼンチン", "Australia": "オーストラリア", "Austria": "オーストリア", "Belgium": "ベルギー", "Brazil": "ブラジル", "Bulgaria": "ブルガリア", "Canada": "カナダ", "Chile": "チリ", "China": "中国", "Colombia": "コロンビア", "Costa Rica": "コスタリカ", "Denmark": "デンマーク", "Finland": "フィンランド", "France": "フランス", "Germany": "ドイツ", "India": "インド", "Italy": "イタリア", "Japan": "日本", "Luxembourg": "ルクセンブルク", "Mexico": "メキシコ", "Netherlands": "オランダ", "New Zealand": "ニュージーランド", "Norway": "ノルウェー", "Russia": "ロシア", "Singapore": "シンガポール", "South Africa": "南アフリカ", "South Korea": "韓国", "Spain": "スペイン", "Sweden": "スウェーデン", "Switzerland": "スイス", "United Kingdom": "イギリス", "United States": "アメリカ合衆国", "Uruguay": "ウルグアイ"}, "dashboardLayout": {"footerCopyright": "© 2023 Booking Parents GmbH. All rights reserved.", "footerImprint": "奥付", "footerPrivacy": "プライバシーポリシー", "navDashboard": "ダッシュボード", "navLearningProgress": "学習進捗", "navLogout": "ログアウト", "navPurchases": "購入履歴", "navShop": "ショップ", "navStudios": "スタジオ"}, "dashboardPage": {"addChildBookingPermissionLabel": "子供にセッションの予約を許可しますか？", "addChildBudgetLabel": "初期予算 (€)", "addChildBudgetPlaceholder": "例：50.00", "addChildButton": "新しい子供を追加", "addChildCancelButton": "キャンセル", "addChildCardDesc": "管理する新しい子供アカウントを作成します", "addChildModalTitle": "新しい子供を追加", "addChildNameLabel": "子供の名前", "addChildPasswordLabel": "子供のパスワード", "addChildPasswordPlaceholder": "パスワードを作成", "addChildSubmitButton": "子供を追加", "addChildUsernameLabel": "子供のユーザー名", "addChildUsernamePlaceholder": "一意のユーザー名を作成", "addFirstChildButton": "最初の子供を追加", "addFundsAmountLabel": "金額 (€)", "addFundsButton": "資金を追加", "addFundsCancelButton": "キャンセル", "addFundsHelpText": "お子様の予算に追加する金額を入力してください。資金はすぐに利用可能になります。", "addFundsModalTitle": "{0} のアカウントに資金を追加", "addFundsSubmitButton": "資金を追加", "bookingAlertEventAdded": "新しいイベント「{0}」が追加されました！", "bookingAlertEventMoved": "{0} は {1} に移動されました", "bookingAlertEventResized": "{0} は {1} に終了するようにサイズ変更されました", "bookingAlertSelectionCancelled": "選択がキャンセルされました。", "bookingConfirmViewDetails": "「{0}」の詳細を表示しますか？\n説明：{1}", "bookingConfirmation": {"availability": "利用可能性", "available": "利用可能", "biweekly": "隔週", "bookingConflictDetailed": "{roomName}の{childName}の予約が競合しています。リクエストされた時間（{requestedTime}）は既存の予約（{conflictingTime}）と競合しています。別の時間枠を選択してください。", "bookingCost": "予約費用", "bookingDetails": "予約詳細", "bookingFailed": "予約失敗", "budgetInformation": "予算情報", "cancelButton": "キャンセル", "checking": "確認中...", "child": "子供", "confirmButton": "予約を確認", "currentBudget": "現在の予算", "date": "日付", "deleteSuccessDescription": "予約は正常に削除されました。", "deleteSuccessMessage": "予約が正常に削除されました！", "duration": "時間", "enableRecurringBooking": "定期予約を有効にする", "error": "エラー", "errorLoadingBudget": "予算情報の読み込み中にエラーが発生しました", "friday": "金曜日", "hours": "時間", "insufficientBudget": "予算不足", "insufficientBudgetMessage": "子供はこの予約に対して十分な予算がありません。資金を追加するか、短い時間枠を選択してください。", "monday": "月曜日", "monthly": "毎月", "none": "なし", "notAvailable": "利用不可", "numberOfBookings": "予約回数", "numberOfOccurrences": "繰り返し回数", "pricePerHour": "時間あたりの価格", "processing": "処理中...", "recurrenceType": "繰り返しタイプ", "recurringBooking": "定期予約", "remainingBudget": "残りの予算", "room": "部屋", "saturday": "土曜日", "selectDaysOfWeek": "曜日を選択", "selectType": "タイプを選択", "singleBookingCost": "1回あたりの予約費用", "studio": "スタジオ", "successDescription": "あなたの予約は確認されました。", "successMessage": "予約成功！", "sunday": "日曜日", "thursday": "木曜日", "time": "時間", "timeSlotBooked": "この時間枠はすでに予約されています。別の時間を選択してください。", "title": "予約確認", "totalPrice": "合計金額", "tryAgainMessage": "別の時間枠を選択してみるか、問題が解決しない場合はサポートにお問い合わせください。", "tuesday": "火曜日", "wednesday": "水曜日", "weekly": "毎週", "weeklyRecurrenceInfo": "毎週同じ曜日と時間に予約されます。", "outsideOpeningHours": "予約はスタジオの営業時間内でのみ可能です。", "recurringBookingCapacityExceeded": "繰り返し予約の1つ以上が部屋の収容人数を超えてしまいます。発生回数を減らすか、別の時間枠を選択してください。", "biweeklyRecurrenceInfo": "予約は指定された回数だけ、2週間ごとに同じ曜日に繰り返されます。", "limitedByBudget": "予算による制限", "limitedByCapacity": "利用可能なスペースによる制限"}, "bookingDescriptionNone": "なし", "bookingModalTitle": "{0} のセッションを予約", "bookingPromptEventTitle": "イベントのタイトルを入力してください：", "childBookAction": "予約する", "childBookDisabled": "予約が無効です", "childBudgetAvailable": "利用可能", "childBudgetEmpty": "空", "childCardAddFundsButton": "資金を追加", "childCardBookButton": "セッションを予約", "childCardBookingLabel": "予約許可：", "childCardBudgetLabel": "予算：", "childCardDeleteButton": "削除", "childCardEditButton": "編集", "childCardNameLabel": "名前：", "childCardUsernameLabel": "ユーザー名：", "childStatusCanBook": "予約可能", "childStatusNoBooking": "予約なし", "childrenCardDescPlural": "{count, plural, one {# 人の子供を管理中} other {# 人の子供を管理中}}", "childrenCardDescSingular": "アクティブな子供アカウント 1 件", "childrenCardTitle": "子供", "classLabel": "クラス/学年", "countryLabel": "国", "editChildAllowBookingsDescription": "チェックした場合、子供は自分の予算を使用してログインし、セッションを予約できます。", "editChildAllowBookingsLabel": "子供にセッションの予約を許可しますか？", "editChildCancelButtonText": "キャンセル", "editChildNameLabel": "子供の名前", "editChildNamePlaceholder": "子供の名前を入力", "editChildPasswordHelpText": "子供のパスワードを変更する場合のみ入力してください。", "editChildPasswordLabelOptional": "新しいパスワード（オプション）", "editChildPasswordPlaceholderOptional": "現在のパスワードを維持する場合は空白のままにします", "editChildSaveButtonText": "変更を保存", "editChildTitle": "子供の詳細を編集", "editChildUpdateErrorMessage": "子供の詳細の更新中にエラーが発生しました。もう一度お試しください。", "editChildUpdateSuccessMessage": "子供の詳細が正常に更新されました！", "editChildUpdatingButtonText": "更新中...", "languageLabel": "言語", "managePrompt": "お子様のアカウントを管理し、学習セッションを予約します", "myChildrenTitle": "私の子供", "noChildrenFoundDesc": "下のフォームを使用して最初の子供アカウントを追加し、予約の管理を開始します", "noChildrenFoundTitle": "子供が見つかりません", "paymentHistoryButton": "支払い履歴", "paymentHistoryCloseButton": "閉じる", "paymentHistoryExportBills": "すべての請求書をダウンロード", "paymentHistoryExportButton": "エクスポートオプション", "paymentHistoryExportCsv": "CSV としてエクスポート", "paymentHistoryFilterAll": "すべての取引", "paymentHistoryFilterDeposits": "入金のみ", "paymentHistoryFilterWithdrawals": "出金のみ", "paymentHistoryModalTitle": "支払い履歴", "paymentHistorySearchPlaceholder": "検索...", "paymentHistorySummaryDeposits": "合計入金額", "paymentHistorySummaryNet": "純残高変動", "paymentHistorySummaryWithdrawals": "合計出金額", "paymentHistoryTableActions": "アクション", "paymentHistoryTableAmount": "金額 (€)", "paymentHistoryTableChild": "子供", "paymentHistoryTableDate": "日付", "paymentHistoryTableDesc": "説明", "paymentHistoryTableType": "タイプ", "paymentHistoryTypeDeposit": "入金", "paymentHistoryTypeWithdrawal": "出金", "preferredStudioDesc": "アクティビティを予約する際に、このスタジオが事前に選択されます", "preferredStudioTitle": "優先学習スタジオ", "regionDisabledText": "選択された国には地域が適用されません", "regionLabel": "地域", "schoolTypeLabel": "学校タイプ", "selectChildCurrentBudgetLabel": "現在の予算：", "selectChildFundsCancelButton": "キャンセル", "selectChildFundsModalTitle": "資金を追加する子供を選択", "selectChildFundsPrompt": "補充する子供のアカウントを選択してください：", "selectCountryFirst": "まず国を選択してください", "selectPlaceholder": "選択してください...", "selectStudioPlaceholder": "-- スタジオを選択 --", "statusAddChildError": "子供の追加中にエラーが発生しました：{0}", "statusAddChildSuccess": "子供が正常に追加されました", "statusAddFundsError": "資金の追加中にエラーが発生しました：{0}", "statusAddFundsSuccess": "{1} のアカウントに €{0} が正常に追加されました", "statusAuthRequired": "認証が必要です。", "statusDeleteChildConfirm": "子供 ID {0} を削除してもよろしいですか？この操作は元に戻せません。", "statusDeleteChildError": "子供の削除中にエラーが発生しました：{0}", "statusDeleteChildSuccess": "子供が正常に削除されました", "statusErrorChildren": "子供の取得中にエラーが発生しました", "statusErrorInitialData": "初期データの取得中にエラーが発生しました：{0}", "statusErrorPreferredStudio": "優先スタジオの取得中にエラーが発生しました", "statusErrorStudios": "スタジオの取得中にエラーが発生しました", "statusFindChildError": "子供の詳細が見つかりませんでした。", "statusLoading": "読み込み中...", "statusUnexpectedError": "予期しないエラーが発生しました。", "statusUpdateChildSuccess": "子供の詳細が正常に更新されました。", "statusUpdatePreferredError": "優先スタジオの更新中にエラーが発生しました：{0}", "statusUpdatePreferredSuccess": "優先スタジオが正常に更新されました", "subjectsLabel": "科目", "totalBudgetCardTitle": "合計予算", "welcomeMessage": "ようこそ"}, "helpModal": {"bookingProcessIntro": "学習セッションを予約するには：", "bookingProcessLi1": "ダッシュボードから子供を選択します", "bookingProcessLi2": "カレンダーアイコンをクリックして予約モーダルを開きます", "bookingProcessLi3": "日付、時刻、アクティビティタイプを選択します", "bookingProcessLi4": "予約を確認します（予算は自動的に差し引かれます）", "bookingProcessLi5": "購入セクションですべての予約を追跡します", "bookingProcessTitle": "予約プロセス", "contactSupportButton": "サポートに連絡する", "faq1Answer": "ダッシュボードの「資金を追加」ボタンを使用して、お子様の学習予算を補充します。クレジットカード、PayPal、または銀行振込を使用できます。", "faq1Question": "子供のアカウントに資金を追加するにはどうすればよいですか？", "faq2Answer": "はい。子供のアカウントを作成するときに、ユーザー名とパスワードを設定します。お子様はこれらの資格情報を使用して、独自の制限付きダッシュボードにアクセスできます。", "faq2Question": "子供は別途ログインできますか？", "faq3Answer": "購入ページに移動し、キャンセルしたい予約を見つけて「詳細を表示」をクリックします。セッションまで 24 時間以上ある場合は、キャンセルオプションが表示されます。", "faq3Question": "予約をキャンセルするにはどうすればよいですか？", "faqTitle": "よくある質問", "gettingStartedLi1": "「新しい子供を追加」ボタンを使用して子供を追加します", "gettingStartedLi2": "ダッシュボードで優先学習スタジオを設定します", "gettingStartedLi3": "活動のための各子供の予算を管理します", "gettingStartedLi4": "学習セッションを予約し、進捗状況を追跡します", "gettingStartedTitle": "はじめに", "gettingStartedWelcome": "Booking Parents アプリへようこそ！このプラットフォームは、お子様の学習活動と予約を 1 か所で管理するのに役立ちます。", "managingChildrenAddBudget": "予算を追加", "managingChildrenAddBudgetDesc": "：お子様の学習予算を補充します", "managingChildrenBook": "予約", "managingChildrenBookDesc": "：優先スタジオで学習セッションをスケジュールします", "managingChildrenDelete": "削除", "managingChildrenDeleteDesc": "：子供のアカウントを削除します（元に戻せません）", "managingChildrenEdit": "編集", "managingChildrenEditDesc": "：名前、予算、予約権限を更新します", "managingChildrenIntro": "各子供について、次のことができます。", "managingChildrenTitle": "子供の管理", "needMoreHelpEmail": "メール：", "needMoreHelpHours": "営業時間：月曜日～金曜日、午前 9 時～午後 5 時（中央ヨーロッパ時間）", "needMoreHelpIntro": "追加のサポートが必要な場合は、サポートチームがお手伝いします。", "needMoreHelpPhone": "電話：", "needMoreHelpTitle": "さらにサポートが必要ですか？", "title": "ヘルプセンター"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "閉じる", "companyInfoTitle": "会社情報", "companyName": "Booking Parents GmbH", "contactTitle": "連絡先", "country": "ドイツ", "directorsLabel": "代表取締役：", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "コンテンツの慎重な管理にもかかわらず、外部リンクのコンテンツについては一切責任を負いません。リンク先のページの運営者は、そのコンテンツについて単独で責任を負います。", "disclaimerTitle": "免責事項", "emailLabel": "メール：", "legalTitle": "法的情報", "phoneLabel": "電話：", "regNumLabel": "登録番号：", "regNumValue": "HRB 123456", "registerLabel": "商業登記：", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "規制情報", "responsibleLabel": "§ 55 Abs. 2 RStV に基づくコンテンツ責任者：", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "奥付", "vatIdLabel": "VAT ID：", "vatIdValue": "DE123456789", "webLabel": "ウェブサイト："}, "languages": {"ar": "アラビア語", "bn": "ベンガル語", "cs": "チェコ語", "da": "デンマーク語", "de": "ドイツ語", "el": "ギリシャ語", "en": "英語", "es": "スペイン語", "fi": "フィンランド語", "fr": "フランス語", "gu": "グジャラート語", "he": "ヘブライ語", "hi": "ヒンディー語", "hu": "ハンガリー語", "it": "イタリア語", "ja": "日本語", "jv": "ジャワ語", "ko": "韓国語", "mr": "マラーティー語", "nl": "オランダ語", "no": "ノルウェー語", "pa": "パンジャブ語", "pl": "ポーランド語", "pt": "ポルトガル語", "ro": "ルーマニア語", "ru": "ロシア語", "sv": "スウェーデン語", "ta": "タミル語", "te": "テルグ語", "th": "タイ語", "tr": "トルコ語", "uk": "ウクライナ語", "ur": "ウルドゥー語", "vi": "ベトナム語", "zh": "中国語"}, "login": {"childProgress": "お子様の進捗", "easyScheduling": "簡単なスケジュール設定", "errors": {"connectionError": "接続エラーが発生しました。インターネット接続を確認してください", "invalidCredentials": "ユーザー名またはパスワードが無効です", "licenseValidationFailed": "ライセンス検証に失敗しました", "loginFailed": "ログインに失敗しました", "noActiveLicense": "有効なライセンスが見つかりません", "serverError": "サーバーエラーが発生しました。後でもう一度お試しください", "sessionExpired": "セッションが期限切れです。再度ログインしてください", "tryAgainLater": "エラーが発生しました。後でもう一度お試しください", "unableToVerifyLicense": "ライセンスを確認できません", "userNotFound": "ユーザーが見つかりません", "wrongPassword": "パスワードが間違っています"}, "forgotPassword": "パスワードをお忘れですか？", "languageSwitch": "言語を切り替える", "loggingIn": "ログイン中...", "login": "ログイン", "password": "パスワード", "summary": "お子様の学習セッションを予約し、進捗状況を一箇所で追跡します。", "title": "ログイン", "username": "ユーザー名"}, "privacyModal": {"acceptButton": "理解し、同意します", "childInfoDesc": "お子様の名前、年齢、学習の好みなどの情報。", "childInfoTitle": "お子様の情報：", "childrenIntro": "当社のサービスでは、中核となる予約サービスを提供するためにお子様に関する情報が必要です。当社は、お子様のデータを保護するために追加の予防措置を講じています。", "childrenLi1": "当社は、お子様に関する必要最小限の情報のみを収集します", "childrenLi2": "お子様から情報を収集する前に、保護者の同意が必要です", "childrenLi3": "当社は、お子様の個人情報を公に利用可能にしません", "childrenLi4": "保護者は、お子様の情報の確認、削除、またはさらなる収集の拒否を行うことができます", "childrenLi5": "当社は、お子様のデータに対して追加のセキュリティ対策を実施します", "childrenTitle": "お子様のプライバシー", "contactAddress": "住所：", "contactEmail": "メール：", "contactIntro": "このプライバシーポリシーについてご不明な点がございましたら、お問い合わせください。", "contactPhone": "電話：", "contactTitle": "お問い合わせ", "cookiesDesc": "当社は、<PERSON>ie および同様の追跡技術を使用して、当社のプラットフォームでのアクティビティを追跡し、特定の情報を保持します。", "cookiesTitle": "<PERSON>ie と追跡：", "howWeUseIntro": "当社は、お客様について収集した情報をさまざまな目的で使用する場合があります。", "howWeUseLi1": "当社のサービスを提供および維持するため", "howWeUseLi2": "当社のサービスの変更についてお客様に通知するため", "howWeUseLi3": "お客様が選択した場合にインタラクティブ機能に参加できるようにするため", "howWeUseLi4": "カスタマーサポートを提供するため", "howWeUseLi5": "当社のサービスを改善するための分析または貴重な情報を収集するため", "howWeUseLi6": "当社のサービスの利用状況を監視するため", "howWeUseLi7": "技術的な問題を検出、防止、対処するため", "howWeUseLi8": "支払いを処理し、不正な取引を防止するため", "howWeUseLi9": "ニュースレター、マーケティング資料、販促資料、その他の情報をお客様に連絡するため", "howWeUseTitle": "情報の利用方法", "infoCollectedIntro": "当社は、以下を含むさまざまな方法でお客様に関する情報を収集する場合があります。", "infoCollectedTitle": "収集する情報", "intro": "Booking Parents では、お客様のプライバシーを真剣に受け止めています。このプライバシーポリシーは、お客様が当社のプラットフォームを使用する際に、当社がお客様の情報をどのように収集、使用、開示、保護するかを説明します。このプライバシーポリシーを注意深くお読みください。このプライバシーポリシーの条件に同意しない場合は、アプリケーションにアクセスしないでください。", "lastUpdated": "最終更新日：", "paymentDataDesc": "お客様が当社のプラットフォームを通じて購入を行う際に支払い情報を収集しますが、支払いカードの詳細は当社のサーバーには保存されません。", "paymentDataTitle": "支払いデータ：", "personalDataDesc": "当社のサービスを利用する際、当社はお客様に連絡または特定するために使用できる個人を特定できる情報の提供をお願いする場合があります。これには、氏名、メールアドレス、電話番号、住所が含まれます。", "personalDataTitle": "個人データ：", "rightsAccessDesc": "お客様は、ご自身の個人情報のコピーを要求する権利を有します。", "rightsAccessTitle": "アクセス権：", "rightsContact": "これらの権利を行使するには、<EMAIL> までご連絡ください。", "rightsErasureDesc": "お客様は、当社がお客様の個人情報を削除するよう要求する権利を有します。", "rightsErasureTitle": "削除権：", "rightsIntro": "お客様の所在地によっては、お客様の個人情報に関して特定の権利を有する場合があります。", "rightsObjectDesc": "お客様は、当社によるお客様の個人情報の処理に異議を申し立てる権利を有します。", "rightsObjectTitle": "異議申し立て権：", "rightsPortabilityDesc": "お客様は、当社がお客様の情報を別の組織またはお客様に直接転送するよう要求する権利を有します。", "rightsPortabilityTitle": "データポータビリティ権：", "rightsRectificationDesc": "お客様は、当社がお客様に関する不正確な情報を訂正するよう要求する権利を有します。", "rightsRectificationTitle": "訂正権：", "rightsRestrictDesc": "お客様は、当社がお客様の情報の処理を制限するよう要求する権利を有します。", "rightsRestrictTitle": "処理制限権：", "rightsTitle": "お客様のプライバシー権", "securityIntro1": "お客様のデータのセキュリティは当社にとって重要ですが、インターネット経由での送信方法や電子ストレージの方法は 100% 安全ではないことを忘れないでください。当社は、お客様の個人データを保護するために商業的に許容される手段を使用するよう努めていますが、その絶対的なセキュリティを保証することはできません。", "securityIntro2": "当社のセキュリティ対策には以下が含まれます。", "securityLi1": "転送中および保存中の機密データの暗号化", "securityLi2": "定期的なセキュリティ評価と監査", "securityLi3": "データ保護に関する従業員トレーニング", "securityLi4": "アクセス制御と認証要件", "securityLi5": "当社の施設に対する物理的なセキュリティ対策", "securityTitle": "データセキュリティ", "sharingBusinessTransfersDesc": "合併、会社資産の売却、資金調達、または当社の事業の全部または一部の別の会社による買収に関連して。", "sharingBusinessTransfersTitle": "事業譲渡：", "sharingConsentDesc": "当社は、お客様の同意を得て、その他の目的でお客様の個人情報を開示する場合があります。", "sharingConsentTitle": "お客様の同意を得て：", "sharingIntro": "当社は、以下の状況でお客様の個人情報を共有する場合があります。", "sharingLegalDesc": "法律で義務付けられている場合、または公的機関からの有効な要求に応じて。", "sharingLegalTitle": "法的要件：", "sharingServiceProvidersDesc": "当社は、当社のサービスを促進するため、当社に代わってサービスを提供するため、またはサービス関連のサービスを実行するために、第三者のサービスプロバイダーとお客様の情報を共有する場合があります。", "sharingServiceProvidersTitle": "サービスプロバイダーと：", "sharingStudiosDesc": "当社は、お客様の予約と学習セッションを促進するために必要な情報を学習スタジオと共有します。", "sharingStudiosTitle": "学習スタジオと：", "sharingTitle": "情報の共有と開示", "title": "プライバシーポリシー", "usageDataDesc": "サービスへのアクセス方法と利用方法に関する情報。これには、お客様のコンピューターのインターネットプロトコルアドレス、ブラウザーの種類、アクセスしたページ、それらのページに費やした時間、その他の診断データが含まれます。", "usageDataTitle": "利用状況データ："}, "settingsModal": {"appearanceSectionTitle": "外観", "bookingRemindersLabel": "予約リマインダー", "cancelButton": "キャンセル", "changePasswordButton": "パスワードを変更", "currencyEUR": "ユーロ (€)", "currencyGBP": "英ポンド (£)", "currencyJPY": "日本円 (¥)", "currencyLabel": "通貨", "currencyUSD": "米ドル ($)", "darkModeLabel": "ダークモード", "editProfileButton": "プロフィールを編集", "enableNotificationsLabel": "通知を有効にする", "fontSizeLabel": "フォントサイズ", "highContrastLabel": "ハイコントラスト", "langArabic": "アラビア語", "langChinese": "中国語", "langDutch": "オランダ語", "langEnglish": "英語", "langFrench": "フランス語", "langGerman": "ドイツ語", "langHebrew": "ヘブライ語", "langHindi": "ヒンディー語", "langJapanese": "日本語", "langSpanish": "スペイン語", "languageLabel": "言語", "lowBudgetAlertsLabel": "低予算アラート", "marketingUpdatesLabel": "マーケティング更新", "notificationsSectionTitle": "通知", "preferencesSectionTitle": "設定", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "あなたの名前", "profileSectionTitle": "プロフィール", "saveButton": "変更を保存", "timezoneLabel": "タイムゾーン", "title": "設定", "tzLondon": "ロンドン (GMT)", "tzLosAngeles": "ロサンゼルス (PST)", "tzNewYork": "ニューヨーク (EST)", "tzParis": "パリ (CET)", "tzTokyo": "東京 (JST)"}, "subjects": {"Art": "美術", "Biology": "生物学", "Business Studies": "経営学", "Chemistry": "化学", "Computer Science": "コンピュータサイエンス", "ComputerScience": "コンピュータサイエンス", "Design and Technology": "デザインと技術", "Drama": "演劇", "Economics": "経済学", "Engineering": "工学", "English": "英語", "Foreign Languages": "外国語", "ForeignLanguages": "外国語", "Geography": "地理学", "History": "歴史", "Literature": "文学", "Mathematics": "数学", "Music": "音楽", "Philosophy": "哲学", "Physical Education": "体育", "PhysicalEducation": "体育", "Physics": "物理学", "Political Science": "政治学", "PoliticalScience": "政治学", "Psychology": "心理学", "Religious Education": "宗教教育", "Religious Studies": "宗教学", "Social Studies": "社会科", "Sociology": "社会学"}, "purchases": {"purchaseHistory": "購入履歴", "viewAndManage": "すべての購入と予約を表示および管理", "downloadReport": "レポートをダウンロード", "totalSpent": "合計支出額", "allCompletedPurchases": "すべての完了した購入", "totalPurchases": "合計購入数", "acrossAllChildren": "すべての子供合計", "pending": "保留中", "awaitingConfirmation": "確認待ち", "transactions": "取引", "all": "すべて", "completed": "完了", "failed": "失敗", "id": "ID", "product": "商品", "child": "子供", "date": "日付", "amount": "金額", "status": "ステータス", "action": "アクション", "viewDetails": "詳細を表示", "noPurchasesFound": "購入が見つかりません", "noPurchasesYet": "まだ購入していません。商品を参照して開始してください。", "noPurchasesFiltered": "現在、{filter} の購入はありません。", "selectChild": "子供を選択", "allChildren": "すべての子供", "filterByChild": "子供で絞り込む", "purchaseDetails": "購入詳細", "actions": "アクション", "markAsCompleted": "完了としてマーク", "markAsFailed": "失敗としてマーク"}, "learningDocumentation": {"pageTitle": "学習ドキュメント", "timeframeAllTime": "全期間", "timeframePastMonth": "先月", "timeframePastQuarter": "過去3ヶ月", "timeframePastYear": "昨年", "exportReportButton": "レポートをエクスポート", "selectChildTitle": "子供を選択", "allChildrenButton": "すべての子供", "overallProgressTitle": "全体的な進捗", "progressTowardMastery": "習熟度への進捗", "recentAchievementsTitle": "最近の成果", "viewFullTimelineButton": "完全なタイムラインを表示", "skillsOverviewTitle": "スキル概要", "skillsSummaryText": "習得済みおよび進行中のスキルの概要はここに表示されます。", "viewAllSkillsButton": "すべてのスキルを表示", "certificatesTitle": "証明書", "certificatesSummaryText": "取得した証明書の概要はここに表示されます。", "viewAllCertificatesButton": "すべての証明書を表示", "documentationTitle": "ドキュメント概要", "generalNotesLabel": "一般的なメモ", "learningGoalsLabel": "学習目標", "strengthsLabel": "強み", "areasForImprovementLabel": "改善分野", "noDocumentationMessage": "この子供のドキュメントはまだ追加されていません。", "eventTypes": {"achievement": "達成", "milestone": "マイルストーン", "assessment": "評価", "test": "テスト", "exam": "試験", "report": "レポート", "presentation": "プレゼンテーション", "project": "プロジェクト", "workshop": "ワークショップ", "other": "その他"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "最終更新"}, "eventTypes": {"achievement": "達成", "milestone": "マイルストーン", "assessment": "評価", "test": "テスト", "exam": "試験", "report": "レポート", "presentation": "プレゼンテーション", "project": "プロジェクト", "workshop": "ワークショップ", "other": "その他"}, "Certificates": {"certificateDetailsTitle": "証明書の詳細", "certificateImageLabel": "証明書の画像", "closeButton": "閉じる", "imageLoadError": "画像を読み込めませんでした", "titleLabel": "証明書のタイトル", "issueDateLabel": "発行日", "issueDateISOLabel": "発行日 (ISO)", "relatedSkillsLabel": "関連スキル", "certificateIdLabel": "証明書ID"}}}