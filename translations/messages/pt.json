{"cartModal": {"checkout": "Finalizar Compra", "continueShopping": "<PERSON><PERSON><PERSON><PERSON>", "emptyCart": "<PERSON><PERSON> carrinho está vazio", "emptyCartText": "Navegue em nossa loja para encontrar produtos educacionais incríveis!", "item": "item", "items": "itens", "outOfStock": "Esgotado", "processing": "Processando...", "subtotal": "Subtotal", "title": "<PERSON><PERSON>", "total": "Total", "vat": "IVA (19%)"}, "checkoutModal": {"completePurchase": "Concluir Compra", "item": "item", "items": "itens", "orderSummary": "Resumo do Pedido", "processing": "Processando...", "quantity": "Quantidade:", "subtotal": "Subtotal", "terms": "Ao concluir esta compra, você concorda com nossos termos e condições. O pagamento será processado com segurança.", "title": "Checkout", "total": "Total", "vat": "IVA (19%)"}, "common": {"appName": "Aplicativo de Pais", "betaTag": "BETA", "home": "Início", "logout": "<PERSON><PERSON>", "shop": "<PERSON><PERSON>"}, "countries": {"Argentina": "Argentina", "Australia": "Austrália", "Austria": "Áustria", "Belgium": "Bélgica", "Brazil": "Brasil", "Bulgaria": "Bulgária", "Canada": "Canadá", "Chile": "Chile", "China": "China", "Colombia": "Colômbia", "Costa Rica": "Costa Rica", "Denmark": "Dinamarca", "Finland": "Finlândia", "France": "França", "Germany": "<PERSON><PERSON><PERSON>", "India": "Índia", "Italy": "Itália", "Japan": "Japão", "Luxembourg": "Luxemburgo", "Mexico": "México", "Netherlands": "<PERSON><PERSON><PERSON>", "New Zealand": "Nova Zelândia", "Norway": "<PERSON><PERSON><PERSON>", "Russia": "Rússia", "Singapore": "Singapura", "South Africa": "África do Sul", "South Korea": "Coreia do Sul", "Spain": "Espanha", "Sweden": "Suécia", "Switzerland": "Suíça", "United Kingdom": "Reino Unido", "United States": "Estados Unidos", "Uruguay": "Uruguai"}, "dashboardLayout": {"footerCopyright": "© 2023 Booking Parents GmbH. Todos os direitos reservados.", "footerImprint": "Impressão", "footerPrivacy": "Privacidade", "navDashboard": "<PERSON><PERSON>", "navLearningProgress": "Progresso de Aprendizado", "navLogout": "<PERSON><PERSON>", "navPurchases": "Compras", "navShop": "<PERSON><PERSON>", "navStudios": "Estúdios"}, "dashboardPage": {"addChildBookingPermissionLabel": "Permitir que a Criança Reserve Sessões?", "addChildBudgetLabel": "Orçamento Inicial (€)", "addChildBudgetPlaceholder": "ex.: 50,00", "addChildButton": "Adicionar Nova Criança", "addChildCancelButton": "<PERSON><PERSON><PERSON>", "addChildCardDesc": "Crie uma nova conta infantil para gerenciar", "addChildModalTitle": "Adicionar Nova Criança", "addChildNameLabel": "Nome da Criança", "addChildPasswordLabel": "<PERSON><PERSON> da Criança", "addChildPasswordPlaceholder": "<PERSON><PERSON> uma senha", "addChildSubmitButton": "<PERSON><PERSON><PERSON><PERSON>", "addChildUsernameLabel": "Nome de Usuário da Criança", "addChildUsernamePlaceholder": "Crie um nome de usuário ú<PERSON>o", "addFirstChildButton": "Adicione Se<PERSON> Primeiro Filho", "addFundsAmountLabel": "Valor (€)", "addFundsButton": "Adicionar <PERSON>", "addFundsCancelButton": "<PERSON><PERSON><PERSON>", "addFundsHelpText": "Insira o valor que deseja adicionar ao orçamento da criança. Os fundos estarão disponíveis imediatamente.", "addFundsModalTitle": "Adicionar Fundos à Conta de {0}", "addFundsSubmitButton": "Adicionar <PERSON>", "bookingAlertEventAdded": "Novo evento \"{0}\" adicionado!", "bookingAlertEventMoved": "{0} foi movido para {1}", "bookingAlertEventResized": "{0} foi redimensionado para terminar em {1}", "bookingAlertSelectionCancelled": "Seleção cancelada.", "bookingConfirmViewDetails": "Ver detalhes para \"{0}\"? \nDescrição: {1}", "bookingConfirmation": {"availability": "Disponibilidade", "available": "Disponível", "biweekly": "<PERSON><PERSON><PERSON><PERSON>", "bookingConflictDetailed": "Há um conflito na reserva de {childName} em {roomName}. O horário solicitado ({requestedTime}) conflita com uma reserva existente ({conflictingTime}). Por favor, escolha outro hor<PERSON>rio.", "bookingCost": "<PERSON><PERSON><PERSON> da Reserva", "bookingDetails": "<PERSON><PERSON><PERSON> da Reserva", "bookingFailed": "Falha na Reserva", "budgetInformation": "Informações de Orçamento", "cancelButton": "<PERSON><PERSON><PERSON>", "checking": "Verificando...", "child": "Criança", "confirmButton": "Confirma<PERSON>", "currentBudget": "Orçamento Atual", "date": "Data", "deleteSuccessDescription": "Sua reserva foi excluída com sucesso.", "deleteSuccessMessage": "Reserva excluída com sucesso!", "duration": "Duração", "enableRecurringBooking": "Habilitar Reserva Recorrente", "error": "Erro", "errorLoadingBudget": "Erro ao carregar informações de orçamento", "friday": "Sexta-feira", "hours": "horas", "insufficientBudget": "Orçamento Insuficiente", "insufficientBudgetMessage": "A criança não tem orçamento suficiente para esta reserva. Por favor, adicione fundos ou selecione um horário mais curto.", "monday": "Segunda-feira", "monthly": "Mensal", "none": "<PERSON><PERSON><PERSON>", "notAvailable": "Não Disponível", "numberOfBookings": "Número de Reservas", "numberOfOccurrences": "Número de Ocorrências", "pricePerHour": "Preço por hora", "processing": "Processando...", "recurrenceType": "Tipo de Recorrência", "recurringBooking": "Reserva Recorrente", "remainingBudget": "Orçamento Restante", "room": "Sala", "saturday": "Sábado", "selectDaysOfWeek": "Selecione os dias da semana", "selectType": "Selecionar Tipo", "singleBookingCost": "Custo por Reserva", "studio": "Estúdio", "successDescription": "Sua reserva foi confirmada.", "successMessage": "Reserva Bem-sucedida!", "sunday": "Domingo", "thursday": "Quin<PERSON>-f<PERSON>", "time": "<PERSON><PERSON>", "timeSlotBooked": "Este horário já está reservado. Por favor, selecione outro horário.", "title": "Confirma<PERSON>", "totalPrice": "Preço Total", "tryAgainMessage": "Por favor, tente selecionar um horário diferente ou entre em contato com o suporte se o problema persistir.", "tuesday": "Terça-feira", "wednesday": "Quarta-feira", "weekly": "<PERSON><PERSON><PERSON>", "weeklyRecurrenceInfo": "Será reservado no mesmo dia e hora toda semana.", "outsideOpeningHours": "As reservas só são possíveis dentro do horário de funcionamento do estúdio.", "recurringBookingCapacityExceeded": "Uma ou mais das reservas recorrentes excederia a capacidade da sala. Por favor, reduza o número de ocorrências ou escolha outro horário.", "biweeklyRecurrenceInfo": "A reserva será repetida a cada duas semanas no mesmo dia pelo número especificado de ocorrências.", "limitedByBudget": "Limitado pelo orçamento", "limitedByCapacity": "Limitado pelo espaço disponível"}, "bookingDescriptionNone": "<PERSON><PERSON><PERSON><PERSON>", "bookingModalTitle": "<PERSON><PERSON><PERSON> para {0}", "bookingPromptEventTitle": "Insira um título para seu evento:", "childBookAction": "Agendar compromisso", "childBookDisabled": "Reserva desativada", "childBudgetAvailable": "Disponível", "childBudgetEmpty": "<PERSON><PERSON><PERSON>", "childCardAddFundsButton": "Adicionar <PERSON>", "childCardBookButton": "<PERSON><PERSON><PERSON>", "childCardBookingLabel": "Reserva Permitida:", "childCardBudgetLabel": "Orçamento:", "childCardDeleteButton": "Excluir", "childCardEditButton": "<PERSON><PERSON>", "childCardNameLabel": "Nome:", "childCardUsernameLabel": "Nome de Usuário:", "childStatusCanBook": "<PERSON><PERSON>", "childStatusNoBooking": "<PERSON><PERSON>", "childrenCardDescPlural": "{count, plural, one {# criança gerenciada} other {# crianças gerenciadas}}", "childrenCardDescSingular": "1 conta infantil ativa", "childrenCardTitle": "Crian<PERSON><PERSON>", "classLabel": "Classe", "countryLabel": "<PERSON><PERSON>", "editChildAllowBookingsDescription": "Se marcado, a criança pode fazer login e reservar sessões usando seu orçamento.", "editChildAllowBookingsLabel": "Permitir que a Criança Reserve Sessões?", "editChildCancelButtonText": "<PERSON><PERSON><PERSON>", "editChildNameLabel": "Nome da Criança", "editChildNamePlaceholder": "Insira o nome da criança", "editChildPasswordHelpText": "Apenas insira se deseja alterar a senha da criança.", "editChildPasswordLabelOptional": "Nova Senha (Opcional)", "editChildPasswordPlaceholderOptional": "Deixe em branco para manter a atual", "editChildSaveButtonText": "<PERSON><PERSON>", "editChildTitle": "<PERSON><PERSON>", "editChildUpdateErrorMessage": "<PERSON>rro ao atual<PERSON>r de<PERSON>hes da criança. Por favor, tente novamente.", "editChildUpdateSuccessMessage": "Detalhes da criança atualizados com sucesso!", "editChildUpdatingButtonText": "Atualizando...", "languageLabel": "Idioma", "managePrompt": "<PERSON><PERSON><PERSON><PERSON> as contas de seus filhos e reserve sessões de aprendizado", "myChildrenTitle": "<PERSON><PERSON>", "noChildrenFoundDesc": "Use o formulário abaixo para adicionar sua primeira conta infantil e começar a gerenciar reservas", "noChildrenFoundTitle": "Nenhuma criança encontrada", "paymentHistoryButton": "Históric<PERSON> de Pagamentos", "paymentHistoryCloseButton": "<PERSON><PERSON><PERSON>", "paymentHistoryExportBills": "<PERSON><PERSON><PERSON> as <PERSON><PERSON>", "paymentHistoryExportButton": "Opções de Exportação", "paymentHistoryExportCsv": "Exportar como CSV", "paymentHistoryFilterAll": "Todas as Transações", "paymentHistoryFilterDeposits": "Apenas Depósitos", "paymentHistoryFilterWithdrawals": "Apenas Retiradas", "paymentHistoryModalTitle": "Históric<PERSON> de Pagamentos", "paymentHistorySearchPlaceholder": "Pesquisar...", "paymentHistorySummaryDeposits": "Depósitos <PERSON>", "paymentHistorySummaryNet": "Variação Líquida do Saldo", "paymentHistorySummaryWithdrawals": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTableActions": "Ações", "paymentHistoryTableAmount": "Valor (€)", "paymentHistoryTableChild": "Criança", "paymentHistoryTableDate": "Data", "paymentHistoryTableDesc": "Descrição", "paymentHistoryTableType": "Tipo", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "Re<PERSON><PERSON>", "preferredStudioDesc": "Este estúdio será pré-selecionado ao reservar atividades", "preferredStudioTitle": "Estúdio de Aprendizado Preferido", "regionDisabledText": "Região não aplicável para o país selecionado", "regionLabel": "Região", "schoolTypeLabel": "Tipo de Escola", "selectChildCurrentBudgetLabel": "Orçamento Atual:", "selectChildFundsCancelButton": "<PERSON><PERSON><PERSON>", "selectChildFundsModalTitle": "Selecione a Criança para Adicionar Fundos", "selectChildFundsPrompt": "Escolha qual conta infantil recarregar:", "selectCountryFirst": "Por favor, selecione um país primeiro", "selectPlaceholder": "Selecione...", "selectStudioPlaceholder": "-- Selecione um Estúdio --", "statusAddChildError": "<PERSON>rro ao adicionar <PERSON>: {0}", "statusAddChildSuccess": "Criança adicionada com sucesso", "statusAddFundsError": "Erro ao adicionar fundos: {0}", "statusAddFundsSuccess": "Adicionado com sucesso €{0} à conta de {1}", "statusAuthRequired": "Autenticação necessária.", "statusDeleteChildConfirm": "Tem certeza que deseja excluir a criança ID {0}? <PERSON>so não pode ser desfeito.", "statusDeleteChildError": "Erro ao excluir criança: {0}", "statusDeleteChildSuccess": "Criança excluída com sucesso", "statusErrorChildren": "Erro ao buscar crianças", "statusErrorInitialData": "Erro ao buscar dados iniciais: {0}", "statusErrorPreferredStudio": "Erro ao buscar estúdio preferido", "statusErrorStudios": "Erro ao buscar estúdios", "statusFindChildError": "Não foi possível encontrar detalhes da criança.", "statusLoading": "Carregando...", "statusUnexpectedError": "Ocorreu um erro inesperado.", "statusUpdateChildSuccess": "Detalhes da criança atualizados com sucesso.", "statusUpdatePreferredError": "Erro ao atualizar estúdio preferido: {0}", "statusUpdatePreferredSuccess": "Estúdio preferido atualizado com sucesso", "subjectsLabel": "Disciplinas", "totalBudgetCardTitle": "Orçamento Total", "welcomeMessage": "<PERSON><PERSON>-vindo"}, "favoritesPage": {"browseShop": "Navegar na Loja", "noFavorites": "Você ainda não adicionou nenhum produto aos favoritos.", "pageTitle": "<PERSON><PERSON>"}, "helpModal": {"bookingProcessIntro": "Para reservar uma sessão de aprendizado:", "bookingProcessLi1": "Selecione uma criança no painel", "bookingProcessLi2": "Clique no ícone do calendário para abrir o modal de reserva", "bookingProcessLi3": "Escolha data, hora e tipo de atividade", "bookingProcessLi4": "Confirme a reserva (o orçamento será deduzido automaticamente)", "bookingProcessLi5": "Acompanhe todas as reservas na seção de Compras", "bookingProcessTitle": "Processo de reserva", "contactSupportButton": "Con<PERSON>r <PERSON>", "faq1Answer": "Use o botão \"Adicionar Fundos\" no painel para recarregar o orçamento de aprendizado do seu filho. Você pode usar cartão de crédito, PayPal ou transferência bancária.", "faq1Question": "Como adiciono fundos à conta do meu filho?", "faq2Answer": "Sim. Ao criar uma conta infantil, você define um nome de usuário e senha. Seu filho pode usar essas credenciais para acessar seu próprio painel limitado.", "faq2Question": "Meu filho pode fazer login separadamente?", "faq3Answer": "Navegue até a página de Compras, encontre a reserva que deseja cancelar e clique em \"Ver Detalhes\". Você encontrará uma opção de cancelamento se a sessão estiver a mais de 24 horas de distância.", "faq3Question": "Como cancelo uma reserva?", "faqTitle": "<PERSON><PERSON><PERSON> frequentes", "gettingStartedLi1": "Adicione seus filhos usando o botão \"Adicionar Novo Filho\"", "gettingStartedLi2": "Defina seu estúdio de aprendizado preferido no painel", "gettingStartedLi3": "Gerencie o orçamento de cada filho para atividades", "gettingStartedLi4": "Agende sessões de aprendizado e acompanhe o progresso", "gettingStartedTitle": "Prime<PERSON>s passos", "gettingStartedWelcome": "Bem-vindo ao aplicativo Booking Parents! Esta plataforma ajuda você a gerenciar as atividades de aprendizado e reservas de seus filhos em um só lugar.", "managingChildrenAddBudget": "<PERSON><PERSON><PERSON><PERSON>", "managingChildrenAddBudgetDesc": ": Recarregar o orçamento de aprendizado do seu filho", "managingChildrenBook": "<PERSON><PERSON><PERSON>", "managingChildrenBookDesc": ": Agendar sessões de aprendizado no seu estúdio preferido", "managingChildrenDelete": "Excluir", "managingChildrenDeleteDesc": ": Remover conta da criança (não pode ser desfeito)", "managingChildrenEdit": "<PERSON><PERSON>", "managingChildrenEditDesc": ": Atualizar nome, orçamento e permissões de reserva", "managingChildrenIntro": "Para cada criança, você pode:", "managingChildrenTitle": "Gerenciando crianças", "needMoreHelpEmail": "Email:", "needMoreHelpHours": "Horário: Segunda a sexta, 9h às 17h CET", "needMoreHelpIntro": "Se precisar de assistência adicional, nossa equipe de suporte está aqui para ajudar:", "needMoreHelpPhone": "Telefone:", "needMoreHelpTitle": "Precisa de mais ajuda?", "title": "Central de Ajuda"}, "imprintModal": {"city": "10115 Berlim", "closeButton": "<PERSON><PERSON><PERSON>", "companyInfoTitle": "Informações da Empresa", "companyName": "Booking Parents GmbH", "contactTitle": "Contato", "country": "<PERSON><PERSON><PERSON>", "directorsLabel": "Diretores:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Apesar do controle cuidadoso do conteúdo, não assumimos responsabilidade pelo conteúdo de links externos. Os operadores das páginas vinculadas são exclusivamente responsáveis por seu conteúdo.", "disclaimerTitle": "Aviso Legal", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Telefone:", "regNumLabel": "Número de Registro:", "regNumValue": "HRB 123456", "registerLabel": "Registro Comercial:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Informações Regulatórias", "responsibleLabel": "Responsável pelo conteúdo de acordo com § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Impresso", "vatIdLabel": "ID do IVA:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "bn": "Bengali", "cs": "Tcheco", "da": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "de": "Alemão", "el": "<PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es": "Espanhol", "fi": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gu": "Gujarati", "he": "Hebraico", "hi": "Hindi", "hu": "<PERSON><PERSON><PERSON><PERSON>", "it": "Italiano", "ja": "<PERSON><PERSON><PERSON><PERSON>", "jv": "<PERSON><PERSON><PERSON><PERSON>", "ko": "<PERSON><PERSON>", "mr": "Marathi", "nl": "<PERSON><PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pa": "Punjabi", "pl": "<PERSON><PERSON><PERSON><PERSON>", "pt": "Português", "ro": "Romeno", "ru": "<PERSON>", "sv": "Sueco", "ta": "Tâmil", "te": "Telugu", "th": "Tailandês", "tr": "<PERSON><PERSON><PERSON>", "uk": "Ucraniano", "ur": "Urdu", "vi": "Vietnamita", "zh": "<PERSON><PERSON><PERSON>"}, "learningStudios": {"allStudios": "Todos os Estúdios", "errorLoading": "Erro ao carregar estúdios", "fields": {"Digital Art": "Arte Digital", "Game Design": "Design de Jogos", "Robotics": "Robótica", "Web Development": "Desenvolvimento Web"}, "foundTryDifferent": "existente. Tente selecionar um campo diferente.", "noMatchForSearch": "Nenhum estúdio corresponde à sua pesquisa", "noStudiosAvailable": "Nenhum estúdio de aprendizagem disponível no sistema no momento.", "noStudiosFound": "Nenhum estúdio de aprendizagem encontrado", "noStudiosWithField": "Nenhum estúdio no campo", "searchStudios": "Procurar estúdios...", "tryAgain": "Tentar Novamente", "tryDifferentKeywords": "Tente palavras-chave diferentes."}, "login": {"childProgress": "Progresso da criança", "easyScheduling": "Agendamento fácil", "errors": {"connectionError": "Erro de conexão, verifique sua internet", "invalidCredentials": "Nome de usuário ou senha inválid<PERSON>", "licenseValidationFailed": "Falha na validação da licença", "loginFailed": "Falha no login", "noActiveLicense": "Nenhuma licença ativa encontrada", "serverError": "<PERSON>rro do servidor, por favor tente novamente mais tarde", "sessionExpired": "<PERSON><PERSON><PERSON> expirada, faça login novamente", "tryAgainLater": "Ocorreu um erro, por favor tente novamente mais tarde", "unableToVerifyLicense": "Não foi possível verificar a licença", "userNotFound": "Usuário não encontrado", "wrongPassword": "Senha incorreta"}, "forgotPassword": "Esqueceu sua senha?", "languageSwitch": "<PERSON><PERSON> idioma", "loggingIn": "Fazendo login...", "login": "Entrar", "password": "<PERSON><PERSON>", "summary": "Agende sessões de aprendizado para seu filho e acompanhe seu progresso em um só lugar.", "title": "<PERSON><PERSON>", "username": "Nome de usuário"}, "privacyModal": {"acceptButton": "Eu Entendo e Aceito", "childInfoDesc": "Informações sobre seus filhos, incluindo nome, idade e preferências de aprendizado.", "childInfoTitle": "Informações da Criança:", "childrenIntro": "Nosso serviço requer informações sobre crianças para fornecer nossos serviços principais de reserva. Tomamos precauções adicionais para proteger os dados das crianças:", "childrenLi1": "<PERSON><PERSON><PERSON> apenas as informações mínimas necessárias sobre crianças", "childrenLi2": "Exigimos consentimento dos pais antes de coletar informações de crianças", "childrenLi3": "Não disponibilizamos publicamente informações pessoais de crianças", "childrenLi4": "Os pais podem revisar, excluir ou recusar a coleta adicional de informações de seus filhos", "childrenLi5": "Implementamos medidas de segurança adicionais para dados de crianças", "childrenTitle": "Privacidade das Crianças", "contactAddress": "Endereço:", "contactEmail": "Email:", "contactIntro": "Se você tiver alguma dúvida sobre esta Política de Privacidade, entre em contato conosco:", "contactPhone": "Telefone:", "contactTitle": "Contate-nos", "cookiesDesc": "Usamos cookies e tecnologias de rastreamento semelhantes para rastrear a atividade em nossa Plataforma e armazenar certas informações.", "cookiesTitle": "Cookies e Rastreamento:", "howWeUseIntro": "Podemos usar as informações que coletamos sobre você para vários propósitos:", "howWeUseLi1": "Fornecer e manter nosso serviço", "howWeUseLi2": "Notificá-lo sobre alterações em nosso serviço", "howWeUseLi3": "Permitir que você participe de recursos interativos quando escolher", "howWeUseLi4": "Fornecer suporte ao cliente", "howWeUseLi5": "Coletar análises ou informações valiosas para melhorar nosso serviço", "howWeUseLi6": "Monitorar o uso de nosso serviço", "howWeUseLi7": "Detectar, prevenir e resolver problemas técnicos", "howWeUseLi8": "Processar pagamentos e prevenir transações fraudulentas", "howWeUseLi9": "Contatá-lo com newsletters, materiais de marketing ou promocionais e outras informações", "howWeUseTitle": "Como Usamos Suas Informações", "infoCollectedIntro": "Podemos coletar informações sobre você de várias maneiras, incluindo:", "infoCollectedTitle": "Informações que Coletamos", "intro": "Na Booking Parents, levamos sua privacidade a sério. Esta Política de Privacidade explica como coletamos, usamos, divulgamos e protegemos suas informações quando você usa nossa plataforma. Por favor, leia esta política de privacidade cuidadosamente. Se você não concordar com os termos desta política de privacidade, não acesse o aplicativo.", "lastUpdated": "Última atualização:", "paymentDataDesc": "Coletamos informações de pagamento quando você faz compras através de nossa plataforma, embora de<PERSON><PERSON> do cartão de crédito não sejam armazenados em nossos servidores.", "paymentDataTitle": "Dados de Pagamento:", "personalDataDesc": "Ao usar nosso serviço, podemos solicitar que você forneça informações pessoalmente identificáveis que podem ser usadas para entrar em contato ou identificá-lo, incluindo nome, endereço de email, número de telefone e endereço postal.", "personalDataTitle": "<PERSON><PERSON>:", "rightsAccessDesc": "Você tem o direito de solicitar cópias de suas informações pessoais.", "rightsAccessTitle": "<PERSON><PERSON><PERSON> Acesso:", "rightsContact": "Para exercer qualquer um desses direitos, entre em contato <NAME_EMAIL>.", "rightsErasureDesc": "Você tem o direito de solicitar que excluamos suas informações pessoais.", "rightsErasureTitle": "Direito de Exclusão:", "rightsIntro": "Dependendo da sua localização, você pode ter certos direitos sobre suas informações pessoais:", "rightsObjectDesc": "Você tem o direito de se opor ao nosso processamento de suas informações pessoais.", "rightsObjectTitle": "Direito de Oposição:", "rightsPortabilityDesc": "Você tem o direito de solicitar que transfiramos suas informações para outra organização ou diretamente para você.", "rightsPortabilityTitle": "Direito à Portabilidade de Dados:", "rightsRectificationDesc": "Você tem o direito de solicitar que corrijamos informações imprecisas sobre você.", "rightsRectificationTitle": "Direito de Retificação:", "rightsRestrictDesc": "Você tem o direito de solicitar que restrinjamos o processamento de suas informações.", "rightsRestrictTitle": "Direito de Restringir o Processamento:", "rightsTitle": "Seus Direitos de Privacidade", "securityIntro1": "A segurança de seus dados é importante para nós, mas lembre-se de que nenhum método de transmissão pela Internet ou método de armazenamento eletrônico é 100% seguro. Embora nos esforcemos para usar meios comercialmente aceitáveis para proteger seus Dados Pessoais, não podemos garantir sua segurança absoluta.", "securityIntro2": "Nossas medidas de segurança incluem:", "securityLi1": "Criptografia de dados sensíveis em trânsito e em repouso", "securityLi2": "Avaliações e auditorias de segurança regulares", "securityLi3": "Treinamento de funcionários em proteção de dados", "securityLi4": "Controles de acesso e requisitos de autenticação", "securityLi5": "Medidas de segurança física para nossas instalações", "securityTitle": "Segurança de Dados", "sharingBusinessTransfersDesc": "Em conexão com qualquer fusão, venda de ativos da empresa, financiamento ou aquisição de toda ou parte de nosso negócio por outra empresa.", "sharingBusinessTransfersTitle": "Transferências de Negócios:", "sharingConsentDesc": "Podemos divulgar suas informações pessoais para qualquer outro propósito com seu consentimento.", "sharingConsentTitle": "Com Seu Consentimento:", "sharingIntro": "Podemos compartilhar suas informações pessoais nas seguintes situações:", "sharingLegalDesc": "Se exigido por lei ou em resposta a solicitações válidas de autoridades públicas.", "sharingLegalTitle": "Requis<PERSON><PERSON>:", "sharingServiceProvidersDesc": "Podemos compartilhar suas informações com provedores de serviços terceirizados para facilitar nosso Serviço, fornecer o Serviço em nosso nome ou executar serviços relacionados ao serviço.", "sharingServiceProvidersTitle": "Com Provedores de Serviços:", "sharingStudiosDesc": "Compartilhamos informações necessárias com estúdios de aprendizado para facilitar suas reservas e sessões de aprendizado.", "sharingStudiosTitle": "Com Estúdios de Aprendizado:", "sharingTitle": "Compartilhamento e Divulgação de Informações", "title": "Política de Privacidade", "usageDataDesc": "Informações sobre como o Serviço é acessado e usado, incluindo o endereço de Protocolo de Internet do seu computador, tipo de navegador, páginas visitadas, tempo gasto nessas páginas e outros dados de diagnóstico.", "usageDataTitle": "Dados de Uso:"}, "productCard": {"addToCart": "Adicionar a<PERSON>", "favorites": {"add": "Adicionar a<PERSON>", "remove": "Remover dos Favoritos"}, "outOfStock": "Esgotado", "productType": {"internal": "Interno", "license": "Licença", "physical": "Físico"}, "stock": "Estoque:"}, "productDetail": {"addToCart": "Adicionar a<PERSON>", "addToFavorites": "Adicionar a<PERSON>", "addedToCart": "Adicionado(s) {quantity} {itemText} ao car<PERSON>ho", "available": "Disponível", "checkoutError": "Erro no checkout. Por favor, tente novamente.", "inStock": "Em estoque:", "itemPlural": "itens", "itemSingular": "item", "outOfStock": "Esgotado", "productDescription": {"license": "Licença digital", "physical": "Produto educacional"}, "productNotFound": "Produto não encontrado", "productTypes": {"internal": "Interno", "license": "Licença", "physical": "Físico"}, "purchaseComplete": "Compra concluída com sucesso!", "quantity": "Quantidade", "removeFromFavorites": "Remover dos Favoritos", "returnToShop": "Voltar à Loja"}, "roomModal": {"close": "<PERSON><PERSON><PERSON>", "pricePerSession": "Preço por Sessão", "roomDescription": "Descrição da Sala", "roomViews": "Vistas da Sala", "seatsAvailable": "Lu<PERSON><PERSON> Disponíveis"}, "settingsModal": {"appearanceSectionTitle": "Aparência", "bookingRemindersLabel": "Lembretes de Reserva", "cancelButton": "<PERSON><PERSON><PERSON>", "changePasswordButton": "<PERSON><PERSON><PERSON>", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "<PERSON><PERSON>", "editProfileButton": "<PERSON><PERSON>", "enableNotificationsLabel": "Ativar Notificações", "fontSizeLabel": "<PERSON><PERSON><PERSON>", "highContrastLabel": "Alto Contraste", "langArabic": "<PERSON><PERSON><PERSON>", "langChinese": "<PERSON><PERSON><PERSON>", "langDutch": "<PERSON><PERSON><PERSON><PERSON>", "langEnglish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "langFrench": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "langGerman": "Alemão", "langHebrew": "Hebraico", "langHindi": "Hindi", "langJapanese": "<PERSON><PERSON><PERSON><PERSON>", "langSpanish": "Espanhol", "languageLabel": "Idioma", "lowBudgetAlertsLabel": "Alertas de Orçamento Baixo", "marketingUpdatesLabel": "Atualizações de Marketing", "notificationsSectionTitle": "Notificações", "preferencesSectionTitle": "Preferências", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "<PERSON><PERSON>", "profileSectionTitle": "Perfil", "saveButton": "<PERSON><PERSON>", "timezoneLabel": "<PERSON><PERSON>", "title": "Configurações", "tzLondon": "Londres (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "Nova York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tóquio (JST)"}, "shopLayout": {"favoritesLink": "<PERSON><PERSON><PERSON><PERSON>", "shopTitle": "<PERSON><PERSON>"}, "shopPage": {"allCategories": "<PERSON><PERSON> as Categoria<PERSON>", "filterByCategory": "Filtrar por Categoria", "noProductsFound": "Nenhum produto encontrado.", "pageTitle": "Loja de Produtos", "searchPlaceholder": "Procurar produtos...", "sortBy": "Ordenar por", "sortNameAsc": "Nome: A-Z", "sortNameDesc": "Nome: Z-A", "sortPriceAsc": "Preço: <PERSON>or para <PERSON>", "sortPriceDesc": "Preço: <PERSON><PERSON>"}, "studioCard": {"defaultDescription": "Um lugar maravilhoso para aprender e explorar.", "featuredRooms": "Salas em Destaque", "rooms": "Salas", "viewAllRooms": "<PERSON><PERSON>", "viewStudioDetails": "Ver Detalhes do Estúdio"}, "studiosLayout": {"backToStudios": "Voltar aos Estúdios", "defaultTitle": "Estúdios de Aprendizagem"}, "subjects": {"Art": "Arte", "Biology": "Biologia", "Business Studies": "<PERSON><PERSON><PERSON><PERSON>", "Chemistry": "Química", "Computer Science": "Ciência da Computação", "ComputerScience": "Ciência da Computação", "Design and Technology": "Design e Tecnologia", "Drama": "Teatro", "Economics": "Economia", "Engineering": "Engenharia", "English": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Foreign Languages": "Línguas <PERSON>", "ForeignLanguages": "Línguas <PERSON>", "Geography": "Geografia", "History": "História", "Literature": "Literatura", "Mathematics": "Matemática", "Music": "Música", "Philosophy": "Filosofia", "Physical Education": "Educação Física", "PhysicalEducation": "Educação Física", "Physics": "Física", "Political Science": "Ciência Política", "PoliticalScience": "Ciência Política", "Psychology": "Psicologia", "Religious Education": "Educação Religiosa", "Religious Studies": "<PERSON><PERSON><PERSON><PERSON>", "Social Studies": "<PERSON><PERSON><PERSON><PERSON>", "Sociology": "Sociologia"}, "purchases": {"purchaseHistory": "Histórico de Compras", "viewAndManage": "Visualize e gerencie todas as compras e reservas", "downloadReport": "Baixar <PERSON>", "totalSpent": "Total Gasto", "allCompletedPurchases": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "totalPurchases": "Total de Compras", "acrossAllChildren": "<PERSON> as <PERSON><PERSON><PERSON><PERSON>", "pending": "Pendente", "awaitingConfirmation": "Aguardando <PERSON>fi<PERSON>", "transactions": "Transações", "all": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "id": "ID", "product": "Produ<PERSON>", "child": "Criança", "date": "Data", "amount": "Valor", "status": "Status", "action": "Ação", "viewDetails": "<PERSON><PERSON>", "noPurchasesFound": "Nenhuma compra encontrada", "noPurchasesYet": "Você ainda não fez nenhuma compra. Navegue em nossos produtos para começar.", "noPurchasesFiltered": "Você não tem compras {filter} no momento.", "selectChild": "Selecionar Criança", "allChildren": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "filterByChild": "Filtrar por Criança", "purchaseDetails": "<PERSON><PERSON><PERSON> da Compra", "actions": "Ações", "markAsCompleted": "Marcar como Concluída", "markAsFailed": "Marcar como Falha"}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "Última atualização"}, "eventTypes": {"achievement": "Conquista", "milestone": "<PERSON>", "assessment": "Avaliação", "test": "<PERSON>e", "exam": "Exame", "report": "Relat<PERSON><PERSON>", "presentation": "Apresentação", "project": "Projeto", "workshop": "Workshop", "other": "Outro"}, "Certificates": {"certificateDetailsTitle": "Detalhes do Certificado", "certificateImageLabel": "Imagem do Certificado", "closeButton": "<PERSON><PERSON><PERSON>", "imageLoadError": "Não foi possível carregar a imagem", "titleLabel": "Título do Certificado", "issueDateLabel": "Data de Emissão", "issueDateISOLabel": "Data de Emissão (ISO)", "relatedSkillsLabel": "Habilidades Relacionadas", "certificateIdLabel": "ID do Certificado"}}}