{"common": {"appName": "Εφαρμογή <PERSON>", "betaTag": "BETA", "home": "Αρχική", "logout": "Αποσύνδεση", "shop": "Κατάστημα"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Belgium", "Brazil": "Brazil", "Bulgaria": "Bulgaria", "Canada": "Canada", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Denmark", "Finland": "Finland", "France": "France", "Germany": "Germany", "India": "India", "Italy": "Italy", "Japan": "Japan", "Luxembourg": "Luxembourg", "Mexico": "Mexico", "Netherlands": "Netherlands", "New Zealand": "New Zealand", "Norway": "Norway", "Russia": "Russia", "Singapore": "Singapore", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Sweden": "Sweden", "Switzerland": "Switzerland", "United Kingdom": "United Kingdom", "United States": "United States", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Με επιφύλαξη παντός δικαιώματος.", "footerImprint": "Στοιχεία Έκδοσης", "footerPrivacy": "Απόρρητο", "navDashboard": "Πίνα<PERSON><PERSON><PERSON>λ<PERSON>γχου", "navLearningProgress": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>ς", "navLogout": "Αποσύνδεση", "navPurchases": "Αγορ<PERSON>ς", "navShop": "Κατάστημα", "navStudios": "Στούντιο"}, "dashboardPage": {"addChildBookingPermissionLabel": "Allow Child to Book Sessions?", "addChildBudgetLabel": "Initial Budget (€)", "addChildBudgetPlaceholder": "e.g., 50.00", "addChildButton": "Add New Child", "addChildCancelButton": "Cancel", "addChildCardDesc": "Create a new child account to manage", "addChildModalTitle": "Add New Child", "addChildNameLabel": "Child's Name", "addChildPasswordLabel": "Child's Password", "addChildPasswordPlaceholder": "Create password", "addChildSubmitButton": "Add Child", "addChildUsernameLabel": "<PERSON>'s <PERSON><PERSON><PERSON>", "addChildUsernamePlaceholder": "Create unique username", "addFirstChildButton": "Add Your First Child", "addFundsAmountLabel": "Amount (€)", "addFundsButton": "Add Funds", "addFundsCancelButton": "Cancel", "addFundsHelpText": "Enter the amount you want to add to the child's budget. The funds will be available immediately.", "addFundsModalTitle": "Add Funds to {0}'s Account", "addFundsSubmitButton": "Add Funds", "bookingAlertEventAdded": "New event \"{0}\" added!", "bookingAlertEventMoved": "{0} was moved to {1}", "bookingAlertEventResized": "{0} was resized to end at {1}", "bookingAlertSelectionCancelled": "Selection cancelled.", "bookingConfirmViewDetails": "View details for \"{0}\"? \nDescription: {1}", "bookingConfirmation": {"availability": "Availability", "available": "Available", "biweekly": "Bi-weekly", "bookingConflictDetailed": "Booking conflict detected for {childName} in {roomName}. The requested time ({requestedTime}) conflicts with an existing booking ({conflictingTime}). Please select a different time slot.", "bookingCost": "Booking Cost", "bookingDetails": "Booking Details", "bookingFailed": "Booking Failed", "budgetInformation": "Budget Information", "cancelButton": "Cancel", "checking": "Checking...", "child": "Child", "confirmButton": "Confirm Booking", "currentBudget": "Current Budget", "date": "Date", "deleteSuccessDescription": "Your booking has been deleted.", "deleteSuccessMessage": "Booking Successfully Deleted!", "duration": "Duration", "enableRecurringBooking": "Enable Recurring Booking", "error": "Error", "errorLoadingBudget": "Error loading budget information", "friday": "Friday", "hours": "hours", "insufficientBudget": "Insufficient Budget", "insufficientBudgetMessage": "The child does not have enough budget for this booking. Please add funds or select a shorter time slot.", "monday": "Monday", "monthly": "Monthly", "none": "None", "notAvailable": "Not Available", "numberOfBookings": "Number of Bookings", "numberOfOccurrences": "Number of Occurrences", "pricePerHour": "Price per hour", "processing": "Processing...", "recurrenceType": "Recurrence Type", "recurringBooking": "Recurring Booking", "remainingBudget": "Remaining Budget", "room": "Room", "saturday": "Saturday", "selectDaysOfWeek": "Select Days of Week", "selectType": "Select Type", "singleBookingCost": "Single Booking Cost", "studio": "Studio", "successDescription": "Your booking has been confirmed.", "successMessage": "Booking Successful!", "sunday": "Sunday", "thursday": "Thursday", "time": "Time", "timeSlotBooked": "This time slot is already booked. Please select another time.", "title": "Confirm Booking", "totalPrice": "Total Price", "tryAgainMessage": "Please try selecting a different time slot or contact support if the problem persists.", "tuesday": "Tuesday", "wednesday": "Wednesday", "weekly": "Weekly", "weeklyRecurrenceInfo": "The booking will repeat on the selected days for the specified number of weeks.", "outsideOpeningHours": "Οι κρατήσεις είναι δυνατές μόνο εντός των ωρών λειτουργίας του στούντιο.", "recurringBookingCapacityExceeded": "Μία ή περισσότερες από τις επαναλαμβανόμενες κρατήσεις θα υπερβούν τη χωρητικότητα του δωματίου. Μειώστε τον αριθμό των επαναλήψεων ή επιλέξτε διαφορετική χρονική θυρίδα.", "biweeklyRecurrenceInfo": "Η κράτηση θα επαναλαμβάνεται κάθε δύο εβδομάδες την ίδια ημέρα για τον καθορισμένο αριθμό επαναλήψεων.", "limitedByBudget": "Περιορισμένο από τον προϋπολογισμό", "limitedByCapacity": "Περιορισμένο από τον διαθέσιμο χώρο"}, "bookingDescriptionNone": "None", "bookingModalTitle": "Book Session for {0}", "bookingPromptEventTitle": "Enter a title for your event:", "childBookAction": "Book appointment", "childBookDisabled": "Booking disabled", "childBudgetAvailable": "Available", "childBudgetEmpty": "Empty", "childCardAddFundsButton": "Add Funds", "childCardBookButton": "Book Session", "childCardBookingLabel": "Booking Allowed:", "childCardBudgetLabel": "Budget:", "childCardDeleteButton": "Delete", "childCardEditButton": "Edit", "childCardNameLabel": "Name:", "childCardUsernameLabel": "Username:", "childStatusCanBook": "Child Can Book", "childStatusNoBooking": "Parent Only Booking", "childrenCardDescPlural": "{count, plural, one {# child managed} other {# children managed}}", "childrenCardDescSingular": "1 active child account", "childrenCardTitle": "Children", "classLabel": "Class/Grade", "countryLabel": "Country", "editChildAllowBookingsDescription": "If checked, the child can log in and book sessions themselves using their budget. If unchecked, only parents can book appointments for this child. Parents can always book appointments for their children regardless of this setting.", "editChildAllowBookingsLabel": "Allow Child to Book Sessions?", "editChildCancelButtonText": "Cancel", "editChildNameLabel": "Child's Name", "editChildNamePlaceholder": "Enter child's name", "editChildPasswordHelpText": "Only enter if you want to change the child's password.", "editChildPasswordLabelOptional": "New Password (Optional)", "editChildPasswordPlaceholderOptional": "Leave blank to keep current", "editChildSaveButtonText": "Save Changes", "editChildTitle": "Edit Child Details", "editChildUpdateErrorMessage": "Error updating child details. Please try again.", "editChildUpdateSuccessMessage": "Child details updated successfully!", "editChildUpdatingButtonText": "Updating...", "languageLabel": "Language", "managePrompt": "Manage your children's accounts and book learning sessions", "myChildrenTitle": "My Children", "noChildrenFoundDesc": "Use the form below to add your first child account to start managing bookings", "noChildrenFoundTitle": "No children found", "paymentHistoryButton": "Payment History", "paymentHistoryCloseButton": "Close", "paymentHistoryExportBills": "Download All Bills", "paymentHistoryExportButton": "Export Options", "paymentHistoryExportCsv": "Export as CSV", "paymentHistoryFilterAll": "All Transactions", "paymentHistoryFilterDeposits": "Deposits Only", "paymentHistoryFilterWithdrawals": "Withdrawals Only", "paymentHistoryModalTitle": "Payment History", "paymentHistorySearchPlaceholder": "Search...", "paymentHistorySummaryDeposits": "Total Deposits", "paymentHistorySummaryNet": "Net Balance Change", "paymentHistorySummaryWithdrawals": "Total Withdrawals", "paymentHistoryTableActions": "Actions", "paymentHistoryTableAmount": "Amount (€)", "paymentHistoryTableChild": "Child", "paymentHistoryTableDate": "Date", "paymentHistoryTableDesc": "Description", "paymentHistoryTableType": "Type", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "<PERSON><PERSON><PERSON>", "preferredStudioDesc": "This studio will be pre-selected when booking activities", "preferredStudioTitle": "Preferred Learning Studio", "regionDisabledText": "Region not applicable for selected country", "regionLabel": "Region", "schoolTypeLabel": "School Type", "selectChildCurrentBudgetLabel": "Current Budget:", "selectChildFundsCancelButton": "Cancel", "selectChildFundsModalTitle": "Select Child to Add Funds", "selectChildFundsPrompt": "Choose which child's account to top up:", "selectCountryFirst": "Select country first", "selectPlaceholder": "Select...", "selectStudioPlaceholder": "-- Select a Studio --", "statusAddChildError": "Error adding child: {0}", "statusAddChildSuccess": "Child added successfully", "statusAddFundsError": "Error adding funds: {0}", "statusAddFundsSuccess": "Successfully added €{0} to {1}'s account", "statusAuthRequired": "Authentication required.", "statusDeleteChildConfirm": "Are you sure you want to delete child ID {0}? This cannot be undone.", "statusDeleteChildError": "Error deleting child: {0}", "statusDeleteChildSuccess": "Child deleted successfully", "statusErrorChildren": "Error fetching children", "statusErrorInitialData": "Error fetching initial data: {0}", "statusErrorPreferredStudio": "Error fetching preferred studio", "statusErrorStudios": "Error fetching studios", "statusFindChildError": "Could not find child details.", "statusLoading": "Loading...", "statusUnexpectedError": "An unexpected error occurred.", "statusUpdateChildSuccess": "Child details updated successfully.", "statusUpdatePreferredError": "Error updating preferred studio: {0}", "statusUpdatePreferredSuccess": "Preferred studio updated successfully", "subjectsLabel": "Subjects", "totalBudgetCardTitle": "Total Budget", "welcomeMessage": "Welcome"}, "helpModal": {"bookingProcessIntro": "Για να κλείσετε μια συνεδρία μάθησης:", "bookingProcessLi1": "Επιλέξτε ένα παιδί από τον πίνακα ελέγχου", "bookingProcessLi2": "Κάντε κλικ στο εικονίδιο ημερολογίου για να ανοίξετε το παράθυρο κράτησης", "bookingProcessLi3": "Επιλέξτε ημερομηνία, ώρα και τύπο δραστηριότητας", "bookingProcessLi4": "Επιβεβαιώστε την κράτηση (ο προϋπολογισμός θα αφαιρεθεί αυτόματα)", "bookingProcessLi5": "Παρακολουθήστε όλες τις κρατήσεις στην ενότητα Αγορές", "bookingProcessTitle": "Διαδικα<PERSON><PERSON>α Κράτησης", "contactSupportButton": "Επικοινωνία με την Υποστήριξη", "faq1Answer": "Χρησιμοποιήστε το κουμπί \"Προσθήκη Κεφαλαίων\" στον πίνακα ελέγχου για να αυξήσετε τον προϋπολογισμό μάθησης του παιδιού σας. Μπορείτε να χρησιμοποιήσετε πιστωτική κάρτα, PayPal ή τραπεζικό έμβασμα.", "faq1Question": "<PERSON><PERSON><PERSON> προσθέτω κεφάλα<PERSON>α στον λογαριασμό του παιδιού μου;", "faq2Answer": "Ναι. Κατά τη δημιουργία λογαριασμού παιδιού, ορί<PERSON><PERSON>τ<PERSON> ένα όνομα χρήστη και έναν κωδικό πρόσβασης. Το παιδί σας μπορεί να χρησιμοποιήσει αυτά τα διαπιστευτήρια για να αποκτήσει πρόσβαση στον δικό του περιορισμένο πίνακα ελέγχου.", "faq2Question": "Μπορεί το παιδί μου να συνδεθεί ξεχωριστά;", "faq3Answer": "Μεταβείτε στη σελίδα Αγορές, βρείτε την κράτηση που θέλετε να ακυρώσετε και κάντε κλικ στο \"Προβολή Λεπτομερειών\". Θα βρείτε μια επιλογή ακύρωσης εάν η συνεδρία απέχει περισσότερο από 24 ώρες.", "faq3Question": "<PERSON><PERSON><PERSON> ακυρώνω μια κράτηση;", "faqTitle": "Συχνές Ερωτήσεις", "gettingStartedLi1": "Προσθέστε τα παιδιά σας χρησιμοποιώντας το κουμπί \"Προσθήκη Νέου Παιδιού\"", "gettingStartedLi2": "Ορίστε το προτιμώμενο στούντιο μάθησης στον πίνακα ελέγχου", "gettingStartedLi3": "Διαχειριστείτε τον προϋπολογισμό κάθε παιδιού για δραστηριότητες", "gettingStartedLi4": "Κλείστε συνεδρίες μάθησης και παρακολουθήστε την πρόοδο", "gettingStartedTitle": "Ξεκινώντας", "gettingStartedWelcome": "Καλώς ήρθατε στην εφαρμογή Booking Parents! Αυτή η πλατφόρμα σας βοηθά να διαχειριστείτε τις μαθησιακές δραστηριότητες και τις κρατήσεις των παιδιών σας σε ένα μέρος.", "managingChildrenAddBudget": "Προσθήκη Προϋπολογισμού", "managingChildrenAddBudgetDesc": ": Αυξήστε τον προϋπολογισμό μάθησης του παιδιού σας", "managingChildrenBook": "Κράτηση", "managingChildrenBookDesc": ": Προγραμματίστε συνεδρίες μάθησης στο προτιμώμενο στούντιο σας", "managingChildrenDelete": "Διαγραφή", "managingChildrenDeleteDesc": ": Αφα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> έναν λογαριασμό παιδιού (δεν μπορεί να αναιρεθεί)", "managingChildrenEdit": "Επεξεργασία", "managingChildrenEditDesc": ": Ενημερώστε όνομα, προϋπολογισμό και δικαιώματα κράτησης", "managingChildrenIntro": "Για κάθε παιδί, μπορείτε να:", "managingChildrenTitle": "Διαχείριση Παιδιών", "needMoreHelpEmail": "Email:", "needMoreHelpHours": "Ώρες: Δευτέρα-Παρασκευή, 9πμ-5μμ CET", "needMoreHelpIntro": "Εάν χρειάζεστε επιπλέον βοήθεια, η ομάδα υποστήριξής μας είναι εδώ για να βοηθήσει:", "needMoreHelpPhone": "Τηλέφωνο:", "needMoreHelpTitle": "Χρειά<PERSON><PERSON><PERSON>τε Περισσότερη Βοήθεια;", "title": "Κέντρο Βοήθειας"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Close", "companyInfoTitle": "Company Information", "companyName": "Booking Parents GmbH", "contactTitle": "Contact", "country": "Germany", "directorsLabel": "Managing Directors:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Despite careful content control, we assume no liability for the content of external links. The operators of the linked pages are solely responsible for their content.", "disclaimerTitle": "Disclaimer", "emailLabel": "Email:", "legalTitle": "Legal", "phoneLabel": "Phone:", "regNumLabel": "Registration Number:", "regNumValue": "HRB 123456", "registerLabel": "Commercial Register:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Regulatory Information", "responsibleLabel": "Responsible for content according to § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "Imprint", "vatIdLabel": "VAT ID:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "Αραβικά", "bn": "Βεγγαλικά", "cs": "Τσεχικά", "da": "Δανικά", "de": "Γερμανικά", "el": "Ελληνικά", "en": "Αγγλικ<PERSON>", "es": "Ισπανικά", "fi": "Φινλανδικά", "fr": "Γαλλικά", "gu": "Γκουτ<PERSON><PERSON><PERSON><PERSON><PERSON>ικά", "he": "Εβραϊκά", "hi": "Χίντι", "hu": "Ουγγρικά", "it": "Ιταλικά", "ja": "Ιαπωνικά", "jv": "Ιαβανικά", "ko": "Κορεατικά", "mr": "Μαραθικά", "nl": "Ολλανδικά", "no": "Νορβηγικά", "pa": "Παντζαμπικά", "pl": "Πολωνικά", "pt": "Πορτογαλικά", "ro": "Ρουμανικά", "ru": "Ρωσικά", "sv": "Σουηδικά", "ta": "Ταμιλικά", "te": "Τελουγκουικά", "th": "Ταϊλανδικά", "tr": "Τουρκικά", "uk": "Ουκρανικά", "ur": "Ουρντού", "vi": "Βιετναμικά", "zh": "Κινεζικά"}, "login": {"childProgress": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>ιδιού", "easyScheduling": "<PERSON>ύκ<PERSON><PERSON><PERSON> Προγραμματισμός", "errors": {"connectionError": "Σφάλμα σύνδεσης, ελέγξτε το διαδίκτυό σας", "invalidCredentials": "Μη έγκυρο όνομα χρήστη ή κωδικός πρόσβασης", "licenseValidationFailed": "Αποτυχία επικύρωσης άδειας", "loginFailed": "Αποτυχία σύνδεσης", "noActiveLicense": "Δεν βρέθηκε ενεργή άδεια", "serverError": "Σφάλμα διακομιστή, δοκιμάστε ξανά αργότερα", "sessionExpired": "Η συνεδρία έληξε, συνδεθείτε ξανά", "tryAgainLater": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα, δοκιμάστε ξανά αργότερα", "unableToVerifyLicense": "Αδυναμία επαλήθευσης άδειας", "userNotFound": "Ο χρήστης δεν βρέθηκε", "wrongPassword": "<PERSON><PERSON><PERSON><PERSON> κωδι<PERSON><PERSON>ς πρόσβασης"}, "forgotPassword": "Ξεχάσατε τον κωδικό σας;", "languageSwitch": "Αλλαγ<PERSON> γλώσσας", "loggingIn": "Σύνδεση...", "login": "Σύνδεση", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "summary": "Κλείστε συνεδρίες μάθησης για το παιδί σας και παρακολουθήστε την πρόοδό του σε ένα μέρος.", "title": "Σύνδεση", "username": "Όνομα χρήστη"}, "privacyModal": {"acceptButton": "I Understand and Accept", "childInfoDesc": "Information about your children, including name, age, and learning preferences.", "childInfoTitle": "Child Information:", "childrenIntro": "Our service requires information about children to provide our core booking services. We take additional precautions to protect children's data:", "childrenLi1": "We collect only the minimum necessary information about children", "childrenLi2": "We require parental consent before collecting information from children", "childrenLi3": "We do not make children's personal information publicly available", "childrenLi4": "Parents can review, delete, or refuse further collection of their child's information", "childrenLi5": "We implement additional security measures for children's data", "childrenTitle": "Children's Privacy", "contactAddress": "Address:", "contactEmail": "Email:", "contactIntro": "If you have any questions about this Privacy Policy, please contact us:", "contactPhone": "Phone:", "contactTitle": "Contact Us", "cookiesDesc": "We use cookies and similar tracking technologies to track activity on our Platform and hold certain information.", "cookiesTitle": "Cookies and Tracking:", "howWeUseIntro": "We may use the information we collect about you for various purposes:", "howWeUseLi1": "To provide and maintain our service", "howWeUseLi2": "To notify you about changes to our service", "howWeUseLi3": "To allow you to participate in interactive features when you choose to do so", "howWeUseLi4": "To provide customer support", "howWeUseLi5": "To gather analysis or valuable information to improve our service", "howWeUseLi6": "To monitor the usage of our service", "howWeUseLi7": "To detect, prevent and address technical issues", "howWeUseLi8": "To process payments and prevent fraudulent transactions", "howWeUseLi9": "To contact you with newsletters, marketing or promotional materials and other information", "howWeUseTitle": "How We Use Your Information", "infoCollectedIntro": "We may collect information about you in various ways, including:", "infoCollectedTitle": "Information We Collect", "intro": "At Booking Parents, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.", "lastUpdated": "Last updated:", "paymentDataDesc": "We collect payment information when you make purchases through our platform, though payment card details are not stored on our servers.", "paymentDataTitle": "Payment Data:", "personalDataDesc": "While using our service, we may ask you to provide personally identifiable information that can be used to contact or identify you, including name, email address, phone number, and postal address.", "personalDataTitle": "Personal Data:", "rightsAccessDesc": "You have the right to request copies of your personal information.", "rightsAccessTitle": "Right to Access:", "rightsContact": "To exercise any of these rights, please contact <NAME_EMAIL>.", "rightsErasureDesc": "You have the right to request that we delete your personal information.", "rightsErasureTitle": "Right to Erasure:", "rightsIntro": "Depending on your location, you may have certain rights regarding your personal information:", "rightsObjectDesc": "You have the right to object to our processing of your personal information.", "rightsObjectTitle": "Right to Object:", "rightsPortabilityDesc": "You have the right to request that we transfer your information to another organization or directly to you.", "rightsPortabilityTitle": "Right to Data Portability:", "rightsRectificationDesc": "You have the right to request that we correct inaccurate information about you.", "rightsRectificationTitle": "Right to Rectification:", "rightsRestrictDesc": "You have the right to request that we restrict the processing of your information.", "rightsRestrictTitle": "Right to Restrict Processing:", "rightsTitle": "Your Privacy Rights", "securityIntro1": "The security of your data is important to us but remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.", "securityIntro2": "Our security measures include:", "securityLi1": "Encryption of sensitive data in transit and at rest", "securityLi2": "Regular security assessments and audits", "securityLi3": "Employee training on data protection", "securityLi4": "Access controls and authentication requirements", "securityLi5": "Physical security measures for our facilities", "securityTitle": "Data Security", "sharingBusinessTransfersDesc": "In connection with any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company.", "sharingBusinessTransfersTitle": "Business Transfers:", "sharingConsentDesc": "We may disclose your personal information for any other purpose with your consent.", "sharingConsentTitle": "With Your Consent:", "sharingIntro": "We may share your personal information in the following situations:", "sharingLegalDesc": "If required to do so by law or in response to valid requests by public authorities.", "sharingLegalTitle": "Legal Requirements:", "sharingServiceProvidersDesc": "We may share your information with third-party service providers to facilitate our Service, provide the Service on our behalf, or perform service-related services.", "sharingServiceProvidersTitle": "With Service Providers:", "sharingStudiosDesc": "We share necessary information with learning studios to facilitate your bookings and learning sessions.", "sharingStudiosTitle": "With Learning Studios:", "sharingTitle": "Information Sharing and Disclosure", "title": "Privacy Policy", "usageDataDesc": "Information on how the Service is accessed and used, including your computer's Internet Protocol address, browser type, pages visited, time spent on those pages, and other diagnostic data.", "usageDataTitle": "Usage Data:"}, "settingsModal": {"appearanceSectionTitle": "Appearance", "bookingRemindersLabel": "Booking Reminders", "cancelButton": "Cancel", "changePasswordButton": "Change Password", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "currencyUSD": "USD ($)", "darkModeLabel": "Dark Mode", "editProfileButton": "Edit Profile", "enableNotificationsLabel": "Enable Notifications", "fontSizeLabel": "Font Size", "highContrastLabel": "Υψηλή Αντίθεση", "langArabic": "Αραβικά", "langChinese": "Κινεζικά", "langDutch": "Ολλανδικά", "langEnglish": "Αγγλικ<PERSON>", "langFrench": "Γαλλικά", "langGerman": "Γερμανικά", "langHebrew": "Εβραϊκά", "langHindi": "Χίντι", "langJapanese": "Ιαπωνικά", "langSpanish": "Ισπανικά", "languageLabel": "Γλώσσα", "lowBudgetAlertsLabel": "Low Budget Alerts", "marketingUpdatesLabel": "Marketing Updates", "notificationsSectionTitle": "Notifications", "preferencesSectionTitle": "Preferences", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Your Name", "profileSectionTitle": "Profile", "saveButton": "Save Changes", "timezoneLabel": "Time Zone", "title": "Settings", "tzLondon": "London (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Art", "Biology": "Biology", "Business Studies": "Business Studies", "Chemistry": "Chemistry", "Computer Science": "Computer Science", "Design and Technology": "Design and Technology", "Drama": "Drama", "Economics": "Economics", "English": "English", "Foreign Languages": "Foreign Languages", "Geography": "Geography", "History": "History", "Mathematics": "Mathematics", "Music": "Music", "Philosophy": "Philosophy", "Physical Education": "Physical Education", "Physics": "Physics", "Psychology": "Psychology", "Religious Studies": "Religious Studies", "Social Studies": "Social Studies"}, "purchases": {"purchaseHistory": "Ιστορικ<PERSON> Αγορών", "viewAndManage": "Προβολή και διαχείριση όλων των αγορών και κρατήσεων", "downloadReport": "Λήψη Αναφοράς", "totalSpent": "Συνολικά Έξοδα", "allCompletedPurchases": "Όλες οι Ολοκληρωμένες Αγορές", "totalPurchases": "Συνολικές Αγορές", "acrossAllChildren": "Για Όλα τα Παιδιά", "pending": "Σε Εκκρεμότητα", "awaitingConfirmation": "Αναμονή Επιβεβαίωσης", "transactions": "Συναλλαγ<PERSON>ς", "all": "Όλες", "completed": "Ολοκληρωμένες", "failed": "Αποτυχημένες", "id": "ID", "product": "Προϊόν", "child": "Παιδί", "date": "Ημερομηνία", "amount": "Ποσό", "status": "Κατάσταση", "action": "Ενέργεια", "viewDetails": "Προβολ<PERSON> Λεπτομερειών", "noPurchasesFound": "Δεν βρέθηκαν αγορές", "noPurchasesYet": "Δεν έχετε κάνει ακόμα αγορές. Περιηγηθείτε στα προϊόντα μας για να ξεκινήσετε.", "noPurchasesFiltered": "Δεν έχετε {filter} αγορές αυτή τη στιγμή.", "selectChild": "Επιλογή Παιδιού", "allChildren": "Όλα τα Παιδιά", "filterByChild": "Φιλτράρισμα ανά Παιδί", "purchaseDetails": "Λεπτομέρει<PERSON>ς Αγοράς", "actions": "Ενέργειες", "markAsCompleted": "Σήμανση ως <PERSON>ο<PERSON>ληρωμένη", "markAsFailed": "Σήμανση ως Αποτυχημένη"}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "Τελευτα<PERSON>α ενημέρωση"}, "eventTypes": {"achievement": "Επίτευγμα", "milestone": "Ορόσημο", "assessment": "Αξιολόγηση", "test": "Τεστ", "exam": "Εξέταση", "report": "Αναφορά", "presentation": "Παρουσίαση", "project": "Έργο", "workshop": "Εργαστήριο", "other": "Άλλο"}, "Certificates": {"certificateDetailsTitle": "Λεπτομέρ<PERSON><PERSON><PERSON>ς Πιστοποιητικού", "certificateImageLabel": "Εικόνα Πιστοποιητικού", "closeButton": "Κλείσιμο", "imageLoadError": "Η εικόνα δεν μπόρεσε να φορτωθεί", "titleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>οποιητικού", "issueDateLabel": "Ημερομηνία Έκδοσης", "issueDateISOLabel": "Ημερομηνία Έκδοσης (ISO)", "relatedSkillsLabel": "Σχετικ<PERSON>ς Δεξιότητες", "certificateIdLabel": "Αναγνωριστι<PERSON><PERSON> Πιστοποιητικού"}}}