{"common": {"appName": "Parents App", "betaTag": "BETA", "home": "<PERSON><PERSON>o", "logout": "<PERSON><PERSON><PERSON>", "shop": "Tienda"}, "countries": {"Argentina": "Argentina", "Australia": "Australia", "Austria": "Austria", "Belgium": "Bélgica", "Brazil": "Brasil", "Bulgaria": "Bulgaria", "Canada": "Canadá", "Chile": "Chile", "China": "China", "Colombia": "Colombia", "Costa Rica": "Costa Rica", "Denmark": "Dinamarca", "Finland": "Finlandia", "France": "Francia", "Germany": "Alemania", "India": "India", "Italy": "Italia", "Japan": "Japón", "Luxembourg": "Luxemburgo", "Mexico": "México", "Netherlands": "País<PERSON>", "New Zealand": "Nueva Zelanda", "Norway": "<PERSON><PERSON><PERSON>", "Russia": "Rusia", "Singapore": "Singapur", "South Africa": "Sudáfrica", "South Korea": "Corea del Sur", "Spain": "España", "Sweden": "<PERSON><PERSON>", "Switzerland": "<PERSON><PERSON>", "United Kingdom": "Reino Unido", "United States": "Estados Unidos", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Todos los derechos reservados.", "footerImprint": "Aviso legal", "footerPrivacy": "Privacidad", "help": "<PERSON><PERSON><PERSON>", "navDashboard": "Panel de control", "navLearningProgress": "Progreso de aprendizaje", "navLogout": "<PERSON><PERSON><PERSON>", "navPurchases": "Compras", "navShop": "Tienda", "navStudios": "Est<PERSON><PERSON>", "privacy": "Privacidad", "selectLanguage": "Seleccionar idioma", "settings": "Configuración", "toggleMenu": "Alternar <PERSON>"}, "dashboardPage": {"addChildBookingPermissionLabel": "¿Permitir al Niño Reservar Sesiones?", "addChildBudgetLabel": "Presupuesto Inicial (€)", "addChildBudgetPlaceholder": "ej., 50.00", "addChildButton": "<PERSON><PERSON>dir <PERSON> Ni<PERSON>", "addChildCancelButton": "<PERSON><PERSON><PERSON>", "addChildCardDesc": "<PERSON><PERSON>r una nueva cuenta de niño para administrar", "addChildModalTitle": "<PERSON><PERSON>dir <PERSON> Ni<PERSON>", "addChildNameLabel": "Nombre del Niño", "addChildPasswordLabel": "Contraseña del Niño", "addChildPasswordPlaceholder": "<PERSON><PERSON><PERSON> con<PERSON>", "addChildSubmitButton": "<PERSON><PERSON><PERSON>", "addChildUsernameLabel": "Nombre de Usuario del Niño", "addChildUsernamePlaceholder": "Crear nombre de usuario único", "addFirstChildButton": "<PERSON><PERSON><PERSON>r Ni<PERSON>", "addFundsAmountLabel": "Cantidad (€)", "addFundsButton": "<PERSON><PERSON><PERSON>", "addFundsCancelButton": "<PERSON><PERSON><PERSON>", "addFundsHelpText": "Ingrese la cantidad que desea añadir al presupuesto del niño. Los fondos estarán disponibles inmediatamente.", "addFundsModalTitle": "<PERSON><PERSON><PERSON> a la Cuenta de {0}", "addFundsSubmitButton": "<PERSON><PERSON><PERSON>", "bookingAlertEventAdded": "¡Nuevo evento \"{0}\" añadido!", "bookingAlertEventMoved": "{0} fue movido a {1}", "bookingAlertEventResized": "{0} fue redimensionado para terminar a las {1}", "bookingAlertSelectionCancelled": "Selección cancelada.", "bookingConfirmViewDetails": "¿Ver detalles para \"{0}\"? \nDescripción: {1}", "bookingConfirmation": {"availability": "Disponibilidad", "available": "Disponible", "biweekly": "Quin<PERSON>nal", "bookingConflictDetailed": "Conflicto de reserva detectado para {childName} en {roomName}. El horario solicitado ({requestedTime}) entra en conflicto con una reserva existente ({conflictingTime}). Por favor, seleccione un horario diferente.", "bookingCost": "Costo de Reserva", "bookingDetails": "Detalles de la Reserva", "bookingFailed": "Reserva Fallida", "budgetInformation": "Información de Presupuesto", "cancelButton": "<PERSON><PERSON><PERSON>", "checking": "Verificando...", "child": "<PERSON><PERSON>", "confirmButton": "Confirma<PERSON>", "currentBudget": "Presupuesto Actual", "date": "<PERSON><PERSON>", "deleteSuccessDescription": "Su reserva ha sido eliminada.", "deleteSuccessMessage": "¡Reserva eliminada con éxito!", "duration": "Duración", "enableRecurringBooking": "Habilitar Reserva Recurrente", "error": "Error", "errorLoadingBudget": "Error al cargar la información del presupuesto", "friday": "Viernes", "hours": "horas", "insufficientBudget": "Presupuesto Insuficiente", "insufficientBudgetMessage": "El niño no tiene suficiente presupuesto para esta reserva. Por favor, añada fondos o seleccione un horario más corto.", "monday": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "notAvailable": "No Disponible", "numberOfBookings": "Número de Reservas", "numberOfOccurrences": "Número de Ocurrencias", "pricePerHour": "<PERSON>cio por hora", "processing": "Procesando...", "recurrenceType": "Tipo de Recurrencia", "recurringBooking": "Reserva Recurrente", "remainingBudget": "Presupuesto Restante", "room": "Sala", "saturday": "Sábado", "selectDaysOfWeek": "Seleccionar Días de la Semana", "selectType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singleBookingCost": "Costo por Reserva", "studio": "Estudio", "successDescription": "Su reserva ha sido confirmada.", "successMessage": "¡Reserva Exitosa!", "sunday": "Domingo", "thursday": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "timeSlotBooked": "Este horario ya está reservado. Por favor, seleccione otro horario.", "title": "Confirma<PERSON>", "totalPrice": "Precio Total", "tryAgainMessage": "Por favor, intente seleccionar un horario diferente o contacte con soporte si el problema persiste.", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON>", "weeklyRecurrenceInfo": "La reserva se repetirá en los días seleccionados durante el número de semanas especificado.", "outsideOpeningHours": "Las reservas solo son posibles dentro del horario de apertura del estudio.", "recurringBookingCapacityExceeded": "Una o más de las reservas recurrentes excederían la capacidad de la sala. Por favor, reduzca el número de ocurrencias o elija otro horario.", "biweeklyRecurrenceInfo": "La reserva se repetirá cada dos semanas el mismo día durante el número especificado de ocurrencias.", "limitedByBudget": "Limitado por presupuesto", "limitedByCapacity": "Limitado por espacio disponible"}, "bookingDescriptionNone": "Ninguna", "bookingModalTitle": "<PERSON><PERSON><PERSON> sesi<PERSON> para {0}", "bookingPromptEventTitle": "Ingrese un título para su evento:", "childBookAction": "Reservar cita", "childBookDisabled": "Reserva deshabilitada", "childBudgetAvailable": "Disponible", "childBudgetEmpty": "Vacío", "childCardAddFundsButton": "<PERSON><PERSON><PERSON>", "childCardBookButton": "<PERSON><PERSON><PERSON>", "childCardBookingLabel": "Reserva Permitida:", "childCardBudgetLabel": "Presupuesto:", "childCardDeleteButton": "Eliminar", "childCardEditButton": "<PERSON><PERSON>", "childCardNameLabel": "Nombre:", "childCardUsernameLabel": "Nombre de usuario:", "childStatusCanBook": "El Niño Puede Reservar", "childStatusNoBooking": "Solo Padres <PERSON><PERSON>", "childrenCardDescPlural": "{count, plural, one {# niño gestionado} other {# niños gestionados}}", "childrenCardDescSingular": "1 cuenta de niño activa", "childrenCardTitle": "<PERSON><PERSON><PERSON>", "classLabel": "Clase/Grado", "countryLabel": "<PERSON><PERSON>", "editChildAllowBookingsDescription": "Si está marcado, el niño puede iniciar sesión y reservar sesiones usando su presupuesto. Si no está marcado, solo los padres pueden reservar citas para este niño. Los padres siempre pueden reservar citas para sus hijos, independientemente de esta configuración.", "editChildAllowBookingsLabel": "¿Permitir al Niño Reservar Sesiones?", "editChildCancelButtonText": "<PERSON><PERSON><PERSON>", "editChildNameLabel": "Nombre del Niño", "editChildNamePlaceholder": "Ingrese el nombre del niño", "editChildPasswordHelpText": "Solo ingrese si desea cambiar la contraseña del niño.", "editChildPasswordLabelOptional": "Nueva Contraseña (Opcional)", "editChildPasswordPlaceholderOptional": "Dejar en blanco para mantener la actual", "editChildSaveButtonText": "Guardar Cambios", "editChildTitle": "Editar Det<PERSON> del Niño", "editChildUpdateErrorMessage": "Error al actualizar los detalles del niño. Inténtelo de nuevo.", "editChildUpdateSuccessMessage": "¡Detalles del niño actualizados correctamente!", "editChildUpdatingButtonText": "Actualizando...", "languageLabel": "Idioma", "managePrompt": "Administre las cuentas de sus hijos y reserve sesiones de aprendizaje", "myChildrenTitle": "<PERSON><PERSON>", "noChildrenFoundDesc": "Use el formulario a continuación para añadir su primera cuenta de niño para comenzar a administrar reservas", "noChildrenFoundTitle": "No se encontraron niños", "paymentHistoryButton": "Historial de Pagos", "paymentHistoryCloseButton": "<PERSON><PERSON><PERSON>", "paymentHistoryExportBills": "<PERSON><PERSON><PERSON> Todas las Facturas", "paymentHistoryExportButton": "Opciones de Exportación", "paymentHistoryExportCsv": "Exportar como CSV", "paymentHistoryFilterAll": "Todas las Transacciones", "paymentHistoryFilterDeposits": "Solo Depósitos", "paymentHistoryFilterWithdrawals": "Solo Retiros", "paymentHistoryModalTitle": "Historial de Pagos", "paymentHistorySearchPlaceholder": "Buscar...", "paymentHistorySummaryDeposits": "Total de Depósitos", "paymentHistorySummaryNet": "Cambio Neto de Balance", "paymentHistorySummaryWithdrawals": "Total de Retiros", "paymentHistoryTableActions": "Acciones", "paymentHistoryTableAmount": "Cantidad (€)", "paymentHistoryTableChild": "<PERSON><PERSON>", "paymentHistoryTableDate": "<PERSON><PERSON>", "paymentHistoryTableDesc": "Descripción", "paymentHistoryTableType": "Tipo", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "<PERSON><PERSON><PERSON>", "preferredStudioDesc": "Este estudio será preseleccionado al reservar actividades", "preferredStudioTitle": "Estudio de Aprendizaje Preferido", "regionDisabledText": "Región no aplicable para el país seleccionado", "regionLabel": "Región", "schoolTypeLabel": "Tipo de Escuela", "selectChildCurrentBudgetLabel": "Presupuesto Actual:", "selectChildFundsCancelButton": "<PERSON><PERSON><PERSON>", "selectChildFundsModalTitle": "Seleccionar Ni<PERSON> para Añadir <PERSON>", "selectChildFundsPrompt": "Elija a qué cuenta de niño recargar:", "selectCountryFirst": "Seleccione primero un país", "selectPlaceholder": "Seleccionar...", "selectStudioPlaceholder": "-- Seleccione un Estudio --", "statusAddChildError": "<PERSON>rro<PERSON> al añadir niño: {0}", "statusAddChildSuccess": "<PERSON><PERSON>", "statusAddFundsError": "Error al añadir fondos: {0}", "statusAddFundsSuccess": "Se añadieron €{0} a la cuenta de {1} correctamente", "statusAuthRequired": "Autenticación requerida.", "statusDeleteChildConfirm": "¿Está seguro de que desea eliminar al niño ID {0}? Esto no se puede deshacer.", "statusDeleteChildError": "Error al eliminar niño: {0}", "statusDeleteChildSuccess": "<PERSON><PERSON> eliminado correctamente", "statusErrorChildren": "Error al obtener los niños", "statusErrorInitialData": "Error al obtener datos iniciales: {0}", "statusErrorPreferredStudio": "Error al obtener el estudio preferido", "statusErrorStudios": "Error al obtener los estudios", "statusFindChildError": "No se pudieron encontrar los datos del niño.", "statusLoading": "Cargando...", "statusUnexpectedError": "Ocurrió un error inesperado.", "statusUpdateChildSuccess": "Datos del niño actualizados correctamente.", "statusUpdatePreferredError": "Error al actualizar el estudio preferido: {0}", "statusUpdatePreferredSuccess": "Estudio preferido actualizado correctamente", "subjectsLabel": "Asignaturas", "totalBudgetCardTitle": "Presupuesto Total", "welcomeMessage": "Bienvenido"}, "helpModal": {"bookingProcessIntro": "Para reservar una sesión de aprendizaje:", "bookingProcessLi1": "Seleccione un niño desde el panel de control", "bookingProcessLi2": "Haga clic en el icono del calendario para abrir el modal de reserva", "bookingProcessLi3": "<PERSON><PERSON> fecha, hora y tipo de actividad", "bookingProcessLi4": "Confirme la reserva (el presupuesto se deducirá automáticamente)", "bookingProcessLi5": "Siga todas las reservas en la sección \"Compras\"", "bookingProcessTitle": "Proceso de reserva", "contactSupportButton": "<PERSON>ar so<PERSON>e", "faq1Answer": "Use el botón \"Añadir fondos\" en el panel de control para recargar el presupuesto de aprendizaje de su hijo. Puede usar tarjeta de crédito, PayPal o transferencia bancaria.", "faq1Question": "¿Cómo añado fondos a la cuenta de mi hijo?", "faq2Answer": "Sí. Al crear una cuenta de niño, configura un nombre de usuario y contraseña. Su hijo puede usar estas credenciales para acceder a su propio panel de control limitado.", "faq2Question": "¿Puede mi hijo iniciar sesión por separado?", "faq3Answer": "Navegue a la página \"Compras\", encuentre la reserva que desea cancelar y haga clic en \"Ver detalles\". Encontrará una opción de cancelación si faltan más de 24 horas para la sesión.", "faq3Question": "¿Cómo cancelo una reserva?", "faqTitle": "Preguntas frecuentes", "gettingStartedLi1": "Añada a sus hijos usando el botón \"Añadir nuevo hijo\"", "gettingStartedLi2": "Establezca su estudio de aprendizaje preferido en el panel de control", "gettingStartedLi3": "Gestione el presupuesto de cada niño para las actividades", "gettingStartedLi4": "Reserve sesiones de aprendizaje y siga el progreso", "gettingStartedTitle": "Primeros pasos", "gettingStartedWelcome": "¡Bienvenido a la App Booking Parents! Esta plataforma le ayuda a gestionar las actividades de aprendizaje y las reservas de sus hijos en un solo lugar.", "managingChildrenAddBudget": "<PERSON><PERSON><PERSON> presupuesto", "managingChildrenAddBudgetDesc": ": Recargar el presupuesto de aprendizaje de su hijo", "managingChildrenBook": "<PERSON><PERSON><PERSON>", "managingChildrenBookDesc": ": Programar sesiones de aprendizaje en su estudio preferido", "managingChildrenDelete": "Eliminar", "managingChildrenDeleteDesc": ": Eliminar cuenta del niño (no se puede deshacer)", "managingChildrenEdit": "<PERSON><PERSON>", "managingChildrenEditDesc": ": Actualizar nombre, presupuesto y permisos de reserva", "managingChildrenIntro": "Para cada niño, puede:", "managingChildrenTitle": "Gestionar niños", "needMoreHelpEmail": "Correo electrónico:", "needMoreHelpHours": "Horario: Lunes a Viernes, 9am-5pm CET", "needMoreHelpIntro": "Si necesita asistencia adicional, nuestro equipo de soporte está aquí para ayudar:", "needMoreHelpPhone": "Teléfono:", "needMoreHelpTitle": "¿Necesita más ayuda?", "title": "Centro de ayuda"}, "imprintModal": {"city": "10115 Berlín", "closeButton": "<PERSON><PERSON><PERSON>", "companyInfoTitle": "Información de la empresa", "companyName": "Booking Parents GmbH", "contactTitle": "Contacto", "country": "Alemania", "directorsLabel": "Directores generales:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "A pesar del cuidadoso control del contenido, no asumimos ninguna responsabilidad por el contenido de los enlaces externos. Los operadores de las páginas enlazadas son los únicos responsables de su contenido.", "disclaimerTitle": "Descargo de responsabilidad", "emailLabel": "Correo electrónico:", "legalTitle": "Legal", "phoneLabel": "Teléfono:", "regNumLabel": "Número de registro:", "regNumValue": "HRB 123456", "registerLabel": "Registro mercantil:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Información de la autoridad reguladora", "responsibleLabel": "Responsable del contenido según § 55 Abs. 2 RStV:", "responsibleName": "<PERSON>", "street": "Calle Ejemplo 123", "title": "Aviso legal", "vatIdLabel": "NIF-IVA:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "bn": "Bengalí", "cs": "Checo", "da": "<PERSON><PERSON>", "de": "Alemán", "el": "Griego", "en": "Inglés", "es": "Español", "fi": "Finés", "fr": "<PERSON><PERSON><PERSON><PERSON>", "gu": "Gujarati", "he": "<PERSON><PERSON><PERSON>", "hi": "Hindi", "hu": "<PERSON><PERSON><PERSON><PERSON>", "it": "Italiano", "ja": "Japonés", "jv": "Javanés", "ko": "<PERSON><PERSON>", "mr": "Marathi", "nl": "Neerlandés", "no": "Noruego", "pa": "Panyabí", "pl": "Polaco", "pt": "Portugués", "ro": "<PERSON><PERSON><PERSON>", "ru": "<PERSON><PERSON><PERSON>", "sv": "Sueco", "ta": "Tamil", "te": "Telugu", "th": "Tailandés", "tr": "<PERSON><PERSON><PERSON>", "uk": "Ucraniano", "ur": "Urdu", "vi": "Vietnamita", "zh": "Chino"}, "login": {"childProgress": "Progreso del Niño", "easyScheduling": "Programación Fácil", "errors": {"connectionError": "Error de conexión, compruebe su conexión a Internet", "invalidCredentials": "Nombre de usuario o contraseña no válidos", "licenseValidationFailed": "Falló la validación de la licencia", "loginFailed": "Error de inicio de sesión", "noActiveLicense": "No se encontró licencia activa", "serverError": "Error del servidor, inténtelo de nuevo más tarde", "sessionExpired": "Sesión expirada, inicie sesión de nuevo", "tryAgainLater": "Ha ocurrido un error, inténtelo de nuevo más tarde", "unableToVerifyLicense": "No se pudo verificar la licencia", "userNotFound": "Usuario no encontrado", "wrongPassword": "Contrase<PERSON>"}, "forgotPassword": "¿Olvidó su contraseña?", "languageSwitch": "Cambiar idioma", "loggingIn": "Iniciando se<PERSON>...", "login": "In<PERSON><PERSON>", "password": "Contraseña", "summary": "Reserve sesiones de aprendizaje para su hijo y haga seguimiento de su progreso en un solo lugar.", "title": "In<PERSON><PERSON>", "username": "Nombre de usuario"}, "privacyModal": {"acceptButton": "Entiendo y acepto", "childInfoDesc": "Información sobre sus hijos, incluyendo nombre, edad y preferencias de aprendizaje.", "childInfoTitle": "Información del niño:", "childrenIntro": "Nuestro servicio requiere información sobre niños para proporcionar nuestros servicios principales de reserva. Tomamos precauciones adicionales para proteger los datos de los niños:", "childrenLi1": "Recopilamos solo la información mínima necesaria sobre los niños", "childrenLi2": "Requerimos el consentimiento de los padres antes de recopilar información de los niños", "childrenLi3": "No hacemos pública la información personal de los niños", "childrenLi4": "Los padres pueden revisar, eliminar o negarse a la recopilación adicional de la información de sus hijos", "childrenLi5": "Implementamos medidas de seguridad adicionales para los datos de los niños", "childrenTitle": "Privacidad de los niños", "contactAddress": "Dirección:", "contactEmail": "Correo electrónico:", "contactIntro": "Si tiene alguna pregunta sobre esta Política de privacidad, contáctenos:", "contactPhone": "Teléfono:", "contactTitle": "Cont<PERSON><PERSON><PERSON>s", "cookiesDesc": "Utilizamos cookies y tecnologías de seguimiento similares para rastrear la actividad en nuestra Plataforma y mantener cierta información.", "cookiesTitle": "Cookies y seguimiento:", "howWeUseIntro": "Podemos utilizar la información que recopilamos sobre usted para diversos fines:", "howWeUseLi1": "Para proporcionar y mantener nuestro servicio", "howWeUseLi2": "Para notificarle sobre cambios en nuestro servicio", "howWeUseLi3": "Para permitirle participar en funciones interactivas cuando elija hacerlo", "howWeUseLi4": "Para proporcionar soporte al cliente", "howWeUseLi5": "Para recopilar análisis o información valiosa para mejorar nuestro servicio", "howWeUseLi6": "Para monitorear el uso de nuestro servicio", "howWeUseLi7": "Para detectar, prevenir y abordar problemas técnicos", "howWeUseLi8": "Para procesar pagos y prevenir transacciones fraudulentas", "howWeUseLi9": "Para contactarlo con boletines informativos, materiales de marketing o promocionales y otra información", "howWeUseTitle": "Cómo usamos su información", "infoCollectedIntro": "Podemos recopilar información sobre usted de varias maneras, incluyendo:", "infoCollectedTitle": "Información que recopilamos", "intro": "En Booking Parents, nos tomamos su privacidad muy en serio. Esta Política de privacidad explica cómo recopilamos, usamos, divulgamos y protegemos su información cuando utiliza nuestra plataforma. Lea atentamente esta política de privacidad. Si no está de acuerdo con los términos de esta política de privacidad, no acceda a la aplicación.", "lastUpdated": "Última actualización:", "paymentDataDesc": "Recopilamos información de pago cuando realiza compras a través de nuestra plataforma, aunque los detalles de la tarjeta de pago no se almacenan en nuestros servidores.", "paymentDataTitle": "Datos de pago:", "personalDataDesc": "Mientras utiliza nuestro servicio, podemos pedirle que proporcione información de identificación personal que pueda usarse para contactarlo o identificarlo, incluyendo nombre, dirección de correo electrónico, número de teléfono y dirección postal.", "personalDataTitle": "Datos personales:", "rightsAccessDesc": "Tiene derecho a solicitar copias de su información personal.", "rightsAccessTitle": "Derecho de acceso:", "rightsContact": "Para ejercer cualquiera de estos derechos, contá<NAME_EMAIL>.", "rightsErasureDesc": "Tiene derecho a solicitar que eliminemos su información personal.", "rightsErasureTitle": "Derecho de supresión:", "rightsIntro": "Dependiendo de su ubicación, puede tener ciertos derechos con respecto a su información personal:", "rightsObjectDesc": "Tiene derecho a oponerse a nuestro procesamiento de su información personal.", "rightsObjectTitle": "Derecho de oposición:", "rightsPortabilityDesc": "Tiene derecho a solicitar que transfiramos su información a otra organización o directamente a usted.", "rightsPortabilityTitle": "Derecho a la portabilidad de los datos:", "rightsRectificationDesc": "Tiene derecho a solicitar que corrijamos información inexacta sobre usted.", "rightsRectificationTitle": "Derecho de rectificación:", "rightsRestrictDesc": "Tiene derecho a solicitar que restrinjamos el procesamiento de su información.", "rightsRestrictTitle": "Derecho a limitar el procesamiento:", "rightsTitle": "Sus derechos de privacidad", "securityIntro1": "La seguridad de sus datos es importante para nosotros, pero recuerde que ningún método de transmisión por Internet o método de almacenamiento electrónico es 100% seguro. Si bien nos esforzamos por utilizar medios comercialmente aceptables para proteger sus Datos personales, no podemos garantizar su seguridad absoluta.", "securityIntro2": "Nuestras medidas de seguridad incluyen:", "securityLi1": "Cifrado de datos sensibles en tránsito y en reposo", "securityLi2": "Evaluaciones y auditorías de seguridad periódicas", "securityLi3": "Capacitación de empleados sobre protección de datos", "securityLi4": "Controles de acceso y requisitos de autenticación", "securityLi5": "Medidas de seguridad física para nuestras instalaciones", "securityTitle": "Seguridad de los datos", "sharingBusinessTransfersDesc": "En relación con cualquier fusión, venta de activos de la empresa, financiación o adquisición de todo o una parte de nuestro negocio por otra empresa.", "sharingBusinessTransfersTitle": "Transferencias de negocios:", "sharingConsentDesc": "Podemos divulgar su información personal para cualquier otro propósito con su consentimiento.", "sharingConsentTitle": "Con su consentimiento:", "sharingIntro": "Podemos compartir su información personal en las siguientes situaciones:", "sharingLegalDesc": "Si así lo exige la ley o en respuesta a solicitudes válidas de las autoridades públicas.", "sharingLegalTitle": "Requisitos legales:", "sharingServiceProvidersDesc": "Podemos compartir su información con proveedores de servicios externos para facilitar nuestro Servicio, proporcionar el Servicio en nuestro nombre o realizar servicios relacionados con el servicio.", "sharingServiceProvidersTitle": "Con proveedores de servicios:", "sharingStudiosDesc": "Compartimos la información necesaria con los estudios de aprendizaje para facilitar sus reservas y sesiones de aprendizaje.", "sharingStudiosTitle": "Con estudios de aprendizaje:", "sharingTitle": "Intercambio y divulgación de información", "title": "Política de privacidad", "usageDataDesc": "Información sobre cómo se accede y utiliza el Servicio, incluida la dirección de Protocolo de Internet de su computadora, tipo de navegador, páginas visitadas, tiempo dedicado a esas páginas y otros datos de diagnóstico.", "usageDataTitle": "Datos de uso:"}, "settingsModal": {"appearanceSectionTitle": "Apariencia", "bookingRemindersLabel": "Recordatorios de reserva", "cancelButton": "<PERSON><PERSON><PERSON>", "changePasswordButton": "Cambiar contraseña", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "Moneda", "currencyUSD": "USD ($)", "darkModeLabel": "<PERSON><PERSON> oscuro", "editProfileButton": "<PERSON><PERSON> perfil", "enableNotificationsLabel": "Activar notificaciones", "fontSizeLabel": "Tamaño de fuente", "highContrastLabel": "<PERSON> contraste", "langArabic": "<PERSON><PERSON><PERSON>", "langChinese": "Chino", "langDutch": "Ho<PERSON><PERSON>", "langEnglish": "Inglés", "langFrench": "<PERSON><PERSON><PERSON><PERSON>", "langGerman": "Alemán", "langHebrew": "<PERSON><PERSON><PERSON>", "langHindi": "Hindi", "langJapanese": "Japonés", "langSpanish": "Español", "languageLabel": "Idioma", "lowBudgetAlertsLabel": "Alertas de presupuesto bajo", "marketingUpdatesLabel": "Actualizaciones de marketing", "notificationsSectionTitle": "Notificaciones", "preferencesSectionTitle": "Preferencias", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Su nombre", "profileSectionTitle": "Perfil", "saveButton": "Guardar cambios", "timezoneLabel": "Zona horaria", "title": "Configuración", "tzLondon": "Londres (GMT)", "tzLosAngeles": "Los Ángeles (PST)", "tzNewYork": "Nueva York (EST)", "tzParis": "<PERSON><PERSON><PERSON> (CET)", "tzTokyo": "Tokio (JST)"}, "subjects": {"Art": "Arte", "Biology": "Biología", "Business Studies": "Estudios Empresariales", "Chemistry": "Química", "Computer Science": "Informática", "Design and Technology": "Diseño y Tecnología", "Drama": "Teatro", "Economics": "Economía", "English": "Inglés", "Foreign Languages": "Lenguas Extranjeras", "Geography": "Geografía", "History": "Historia", "Mathematics": "Matemáticas", "Music": "Música", "Philosophy": "Filosofía", "Physical Education": "Educación Física", "Physics": "Física", "Psychology": "Psicología", "Religious Studies": "<PERSON><PERSON><PERSON><PERSON>", "Social Studies": "Estudios Sociales"}, "purchases": {"purchaseHistory": "Historial de Compras", "viewAndManage": "Ver y gestionar todas las compras y reservas", "downloadReport": "Descargar Informe", "totalSpent": "Gasto Total", "allCompletedPurchases": "Todas las compras completadas", "totalPurchases": "Compras Totales", "acrossAllChildren": "De todos los niños/as", "pending": "Pendiente", "awaitingConfirmation": "Esperando confirmación", "transactions": "Transacciones", "all": "<PERSON><PERSON>", "completed": "Completado", "failed": "Fallido", "id": "ID", "product": "Producto", "child": "Niño/a", "date": "<PERSON><PERSON>", "amount": "Importe", "status": "Estado", "action": "Acción", "viewDetails": "<PERSON><PERSON>", "noPurchasesFound": "No se encontraron compras", "noPurchasesYet": "Aún no has realizado ninguna compra. Explora nuestros productos para empezar.", "noPurchasesFiltered": "No tienes ninguna compra {filter} en este momento.", "selectChild": "Se<PERSON><PERSON><PERSON><PERSON>/a", "allChildren": "Todos los Niños/as", "filterByChild": "Filtrar por niño/a", "purchaseDetails": "Detalles de Compra", "actions": "Acciones", "markAsCompleted": "Marcar como Completado", "markAsFailed": "Marcar como Fallido"}, "learningDocumentation": {"pageTitle": "Documentación de Aprendizaje", "timeframeAllTime": "Todo el tiempo", "timeframePastMonth": "<PERSON><PERSON><PERSON>", "timeframePastQuarter": "Últimos 3 Meses", "timeframePastYear": "<PERSON>lt<PERSON>", "exportReportButton": "Exportar Informe", "selectChildTitle": "Se<PERSON><PERSON><PERSON><PERSON>/a", "allChildrenButton": "Todos los Niños/as", "overallProgressTitle": "Progreso General", "progressTowardMastery": "Progreso hacia la Maestría", "recentAchievementsTitle": "Logros Recientes", "viewFullTimelineButton": "Ver Cronología Completa", "skillsOverviewTitle": "Resumen de Habilidades", "skillsSummaryText": "Aquí irá un resumen de las habilidades dominadas y en progreso.", "viewAllSkillsButton": "Ver Todas las Habilidades", "certificatesTitle": "Certificados", "certificatesSummaryText": "Aquí irá un resumen de los certificados obtenidos.", "viewAllCertificatesButton": "Ver Todos los Certificados", "documentationTitle": "Resumen de Documentación", "generalNotesLabel": "Notas Generales", "learningGoalsLabel": "Objetivos de Aprendizaje", "strengthsLabel": "Fortalezas", "areasForImprovementLabel": "<PERSON><PERSON><PERSON>", "noDocumentationMessage": "Aún no se ha añadido documentación para este niño/a.", "eventTypes": {"achievement": "Logro", "milestone": "<PERSON><PERSON>", "assessment": "Evaluación", "test": "Prueba", "exam": "Examen", "report": "Informe", "presentation": "Presentación", "project": "Proyecto", "workshop": "Taller", "other": "<PERSON><PERSON>"}}, "LearningDocumentation": {"Progress": {"pageTitle": "Documentación de Progreso", "skillsTitle": "Progreso de Habilidades", "skillsDescription": "Ver progreso en varias habilidades", "childSelectLabel": "Se<PERSON><PERSON><PERSON><PERSON>/a", "skillAreaLabel": "Área de Habilidad", "skillLabel": "Habilidad", "skillLevelLabel": "Nivel de Habilidad", "progressLabel": "Progreso (%)", "notesLabel": "Notas", "optionalText": "Opcional", "saveButton": "Guardar", "cancelButton": "<PERSON><PERSON><PERSON>", "closeButton": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "-- Se<PERSON>cci<PERSON>r --", "successMessage": "¡Documentación de progreso guardada con éxito!", "errorMessage": "Error al guardar la documentación de progreso. Por favor, inténtelo de nuevo.", "errorFetchingHistory": "Error al obtener el historial de progreso", "existingSkillsTitle": "Registros de Progreso", "selectChildFirst": "Por favor, seleccione primero un niño/a", "loadingProgressMessage": "Cargando progreso...", "noExistingSkillsMessage": "No se encontró documentación de progreso para este niño/a", "selectSkillAreaFirst": "Por favor, seleccione primero un área de habilidad", "noSkillLevelsMessage": "No hay niveles de habilidad disponibles", "errorFetchingSkillLevels": "Error al obtener los niveles de habilidad", "errorFetchingSkills": "Error al obtener las habilidades", "errorFetchingProgress": "Error al obtener el progreso", "noLevelAssigned": "<PERSON><PERSON><PERSON> nivel asignado", "noSkillAreasMessage": "No hay áreas de habilidad disponibles", "historyTitle": "Historial de Progreso", "historyEmpty": "No se encontraron entradas de progreso anteriores", "historyDate": "<PERSON><PERSON>", "levelPrefix": "<PERSON><PERSON>", "progressPrefix": "Progreso", "notesPrefix": "Notas", "loadingMessage": "Cargando...", "lastUpdatedPrefix": "Última actualización"}, "Timeline": {"pageTitle": "Línea de Tiempo de Aprendizaje", "timeframeAllTime": "Todo el Tiempo", "timeframePastMonth": "<PERSON><PERSON><PERSON>", "timeframePastQuarter": "Últimos 3 Meses", "timeframePastYear": "<PERSON>lt<PERSON>", "exportTimelineButton": "Exportar Línea de Tiempo", "selectChildTitle": "Se<PERSON><PERSON><PERSON><PERSON>/a", "allChildrenButton": "Todos los Niños", "filterByEventTypeTitle": "Filtrar por Tipo de Evento", "allEventsButton": "Todos los Eventos", "learningEventsTitle": "Eventos de Aprendizaje", "skillProgressLabel": "Progreso de Habilidades", "relatedSkillsLabel": "Habilidades Relacionadas", "achievementLabel": "Logro", "noEventsMatchMessage": "Ningún evento coincide con sus filtros actuales.", "noEventsYetMessage": "No se han registrado eventos de aprendizaje para este niño/a todavía.", "eventsCreatedInAssistantsMessage": "Los eventos aparecerán aquí una vez que se creen en la aplicación de asistentes.", "resetFiltersButton": "Restablecer filtros", "loading": "Cargando eventos...", "error": "Error al cargar eventos. Por favor, inténtelo de nuevo."}, "Certificates": {"pageTitle": "Certificados", "certificatesTitle": "Certificados y Logros", "certificatesDescription": "Ver certificados obtenidos por su hijo/a", "selectChildTitle": "Se<PERSON><PERSON><PERSON><PERSON>/a", "allChildrenButton": "Todos los Niños", "downloadAllButton": "<PERSON><PERSON><PERSON>", "certificateLabel": "Certificado", "awardedToText": "<PERSON><PERSON><PERSON> a", "viewDetailsButton": "<PERSON><PERSON>", "downloadButton": "<PERSON><PERSON><PERSON>", "loading": "Cargando certificados...", "error": "Error al cargar certificados. Por favor, inténtelo de nuevo.", "noCertificatesFound": "No se encontraron certificados para este niño/a.", "noCertificatesMessage": "Aún no se han añadido certificados para este niño/a.", "certificateDetailsTitle": "Detalles del Certificado", "certificateImageLabel": "Imagen del Certificado", "closeButton": "<PERSON><PERSON><PERSON>", "imageLoadError": "No se pudo cargar la imagen", "titleLabel": "Título del Certificado", "issueDateLabel": "<PERSON><PERSON> de Emisión", "issueDateISOLabel": "<PERSON><PERSON> Em<PERSON> (ISO)", "relatedSkillsLabel": "Habilidades Relacionadas", "certificateIdLabel": "ID del Certificado"}, "eventTypes": {"achievement": "Logro", "milestone": "<PERSON><PERSON>", "assessment": "Evaluación", "test": "Prueba", "exam": "Examen", "report": "Informe", "presentation": "Presentación", "project": "Proyecto", "workshop": "Taller", "other": "<PERSON><PERSON>"}}}