{"common": {"appName": "Ebeveyn <PERSON>ı", "betaTag": "BETA", "home": "<PERSON>", "logout": "Çıkış", "shop": "Mağaza"}, "countries": {"Argentina": "<PERSON><PERSON><PERSON><PERSON>", "Australia": "Avustralya", "Austria": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Belgium": "Belçika", "Brazil": "<PERSON><PERSON><PERSON><PERSON>", "Bulgaria": "Bulgaristan", "Canada": "Ka<PERSON><PERSON>", "Chile": "Şili", "China": "<PERSON><PERSON>", "Colombia": "Kolombiya", "Costa Rica": "<PERSON><PERSON>", "Denmark": "Danimark<PERSON>", "Finland": "Finlandiya", "France": "<PERSON><PERSON><PERSON>", "Germany": "Almanya", "India": "Hindistan", "Italy": "İtalya", "Japan": "Japonya", "Luxembourg": "Lüksemburg", "Mexico": "<PERSON><PERSON><PERSON>", "Netherlands": "Hollanda", "New Zealand": "<PERSON><PERSON>", "Norway": "Norveç", "Russia": "<PERSON><PERSON><PERSON>", "Singapore": "Singapur", "South Africa": "G<PERSON>ney Afrika", "South Korea": "<PERSON><PERSON><PERSON>", "Spain": "İspanya", "Sweden": "İsveç", "Switzerland": "İsviçre", "United Kingdom": "Birleşik Krallık", "United States": "Amerika Birleşik Devletleri", "Uruguay": "Uruguay"}, "dashboardLayout": {"footerCopyright": "© {year} Booking Parents. Tüm hakları saklıdır.", "footerImprint": "K<PERSON><PERSON><PERSON>", "footerPrivacy": "Gizlilik", "navDashboard": "Ko<PERSON>rol <PERSON>i", "navLearningProgress": "<PERSON><PERSON><PERSON><PERSON>", "navLogout": "Çıkış", "navPurchases": "<PERSON><PERSON>ı<PERSON>", "navShop": "Mağaza", "navStudios": "Stüdyolar"}, "dashboardPage": {"addChildBookingPermissionLabel": "Çocuğun Seans Ayarlamasına İzin Verilsin mi?", "addChildBudgetLabel": "Başlangıç Bütçesi (€)", "addChildBudgetPlaceholder": "örn., 50.00", "addChildButton": "<PERSON><PERSON>", "addChildCancelButton": "İptal", "addChildCardDesc": "Yönetmek için yeni bir çocuk hesabı oluşturun", "addChildModalTitle": "<PERSON><PERSON>", "addChildNameLabel": "Çocuğun Adı", "addChildPasswordLabel": "Çocuğun Şifresi", "addChildPasswordPlaceholder": "<PERSON><PERSON><PERSON>", "addChildSubmitButton": "Çocuk <PERSON>", "addChildUsernameLabel": "Çocuğun Kullanıcı Adı", "addChildUsernamePlaceholder": "Benzersiz kullanıcı adı oluştur", "addFirstChildButton": "İlk Çocuğunuzu Ekleyin", "addFundsAmountLabel": "Miktar (€)", "addFundsButton": "Bakiye <PERSON>kle", "addFundsCancelButton": "İptal", "addFundsHelpText": "Çocuğun bütçesine eklemek istediğiniz miktarı girin. Bakiye hemen kullanılabilir olacaktır.", "addFundsModalTitle": "{0}'in Hesabına Bakiye Ekle", "addFundsSubmitButton": "Bakiye <PERSON>kle", "bookingAlertEventAdded": "<PERSON><PERSON> \"{0}\" eklendi!", "bookingAlertEventMoved": "{0}, {1} tari<PERSON> ta<PERSON>ı", "bookingAlertEventResized": "{0}, {1} ta<PERSON><PERSON>de bitecek şekilde yeniden boyutlandırıldı", "bookingAlertSelectionCancelled": "<PERSON><PERSON><PERSON> iptal edildi.", "bookingConfirmViewDetails": "\"{0}\" i<PERSON><PERSON> görüntülemek istiyor musunuz? \nAçıklama: {1}", "bookingConfirmation": {"availability": "Uygunluk", "available": "<PERSON><PERSON>gun", "biweekly": "İki Haftalık", "bookingConflictDetailed": "{childName} için {roomName} odasında rezervasyon çakışması tespit edildi. <PERSON><PERSON><PERSON> zaman ({requestedTime}) mevcut bir rezervasyonla ({conflictingTime}) çakışıyor. Lütfen farklı bir zaman dilimi seçin.", "bookingCost": "Rezervasyon Ücreti", "bookingDetails": "Rezervasyon Detayları", "bookingFailed": "Rezervasyon Başarısız", "budgetInformation": "Bütçe Bilgisi", "cancelButton": "İptal", "checking": "<PERSON><PERSON><PERSON> edili<PERSON>...", "child": "Çocuk", "confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentBudget": "Mevcut Bütçe", "date": "<PERSON><PERSON><PERSON>", "deleteSuccessDescription": "Rezervasyonunuz silindi.", "deleteSuccessMessage": "Rezervasyon Başarıyla Silindi!", "duration": "<PERSON><PERSON><PERSON>", "enableRecurringBooking": "Tekrarlanan <PERSON>kinleştir", "error": "<PERSON><PERSON>", "errorLoadingBudget": "Bütçe bilgisi yüklenirken hata oluştu", "friday": "<PERSON><PERSON>", "hours": "saat", "insufficientBudget": "<PERSON><PERSON><PERSON>", "insufficientBudgetMessage": "Çocuğun bu rezervasyon için yeterli bütçesi yok. Lütfen bakiye ekleyin veya daha kısa bir zaman dilimi seçin.", "monday": "<PERSON><PERSON><PERSON>", "monthly": "Aylık", "none": "Hiç<PERSON>i", "notAvailable": "<PERSON><PERSON><PERSON>", "numberOfBookings": "Rezervasyon Sayısı", "numberOfOccurrences": "Tekrarlama Sayısı", "pricePerHour": "Saat başına ücret", "processing": "İşleniyor...", "recurrenceType": "Tekrarlama Türü", "recurringBooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingBudget": "<PERSON><PERSON>", "room": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON><PERSON><PERSON>", "selectDaysOfWeek": "Haftanın Günlerini Seçin", "selectType": "<PERSON><PERSON><PERSON>", "singleBookingCost": "Tek Rezervasyon Ücreti", "studio": "Stü<PERSON><PERSON>", "successDescription": "Rezervasyonunuz onaylandı.", "successMessage": "Rezervasyon Başarılı!", "sunday": "Pazar", "thursday": "Perşembe", "time": "Zaman", "timeSlotBooked": "Bu zaman dilimi zaten rezerve edilmiş. Lütfen başka bir zaman seçin.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalPrice": "Toplam Ücret", "tryAgainMessage": "Lütfen farklı bir zaman dilimi seçmeyi deneyin veya sorun devam ederse destek ekibiyle iletişime geçin.", "tuesday": "Salı", "wednesday": "Çarşamba", "weekly": "Haftalık", "weeklyRecurrenceInfo": "<PERSON><PERSON><PERSON><PERSON>, belirtilen hafta sayısı boyunca seçilen günlerde tekrarlanacaktır.", "outsideOpeningHours": "Rezervasyonlar yalnızca stüdyo açılış saatleri içinde mümkündür.", "recurringBookingCapacityExceeded": "Tekrarlanan rezervasyonlardan bir veya daha fazlası oda kapasitesini aşacaktır. Lütfen tekrar sayısını azaltın veya başka bir zaman dilimi seçin.", "biweeklyRecurrenceInfo": "<PERSON><PERSON><PERSON><PERSON>, beli<PERSON><PERSON>n sayıda tekrar için iki haftada bir aynı günde tekrarlanacaktır.", "limitedByBudget": "Bütçe ile sınırlı", "limitedByCapacity": "Mevcut alan ile sınırlı"}, "bookingDescriptionNone": "Hiç<PERSON>i", "bookingModalTitle": "{0} i<PERSON><PERSON>", "bookingPromptEventTitle": "Etkinliğiniz için bir başlık girin:", "childBookAction": "<PERSON><PERSON><PERSON>", "childBookDisabled": "Rezervasyon devre dışı", "childBudgetAvailable": "Mevcut", "childBudgetEmpty": "Boş", "childCardAddFundsButton": "Bakiye <PERSON>kle", "childCardBookButton": "<PERSON><PERSON>", "childCardBookingLabel": "Rezervasyona İzin Veriliyor:", "childCardBudgetLabel": "Bütçe:", "childCardDeleteButton": "Sil", "childCardEditButton": "<PERSON><PERSON><PERSON><PERSON>", "childCardNameLabel": "İsim:", "childCardUsernameLabel": "Kullanıcı Adı:", "childStatusCanBook": "Çocuk Rezervasyon Yapabilir", "childStatusNoBooking": "<PERSON><PERSON><PERSON>", "childrenCardDescPlural": "{count, plural, one {# çocuk yönetiliyor} other {# çocuk yönetiliyor}}", "childrenCardDescSingular": "1 aktif çocuk hesabı", "childrenCardTitle": "Çocuklar", "classLabel": "Sınıf/Seviye", "countryLabel": "<PERSON><PERSON><PERSON>", "editChildAllowBookingsDescription": "<PERSON><PERSON><PERSON><PERSON>irse, çocuk giriş yapabilir ve kendi bütçesini kullanarak seans ayarlayabilir. <PERSON><PERSON><PERSON><PERSON>mez<PERSON>, sadece ebe<PERSON><PERSON><PERSON> bu çocuk için randevu ayarlayabilir. Ebeveynler bu ayardan bağıms<PERSON>z olarak her zaman çocukları için randevu ayarlayabilirler.", "editChildAllowBookingsLabel": "Çocuğun Seans Ayarlamasına İzin Verilsin mi?", "editChildCancelButtonText": "İptal", "editChildNameLabel": "Çocuğun Adı", "editChildNamePlaceholder": "Çocuğun adını girin", "editChildPasswordHelpText": "Sadece ç<PERSON>uğun şifresini değiştirmek istiyorsanız girin.", "editChildPasswordLabelOptional": "<PERSON><PERSON> (İsteğe Bağlı)", "editChildPasswordPlaceholderOptional": "Mevcut şifreyi korumak için boş bırakın", "editChildSaveButtonText": "Değişiklikleri Kaydet", "editChildTitle": "Çocuk Bilgilerini Düzenle", "editChildUpdateErrorMessage": "Çocuk bilgileri güncellenirken hata oluştu. Lütfen tekrar deneyin.", "editChildUpdateSuccessMessage": "Çocuk bilgileri başarıyla güncellendi!", "editChildUpdatingButtonText": "Güncelleniyor...", "languageLabel": "Dil", "managePrompt": "Çocuklarınızın hesaplarını yönetin ve öğrenme seansları ayarlayın", "myChildrenTitle": "Çocuklarım", "noChildrenFoundDesc": "İlk çocuk hesabınızı eklemek ve rezervasyonları yönetmeye başlamak için aşağıdaki formu kullanın", "noChildrenFoundTitle": "Çocuk bulunamadı", "paymentHistoryButton": "Ödeme Geçmişi", "paymentHistoryCloseButton": "Ka<PERSON><PERSON>", "paymentHistoryExportBills": "<PERSON><PERSON><PERSON>arı İndir", "paymentHistoryExportButton": "Dışa Aktarma Seçenekleri", "paymentHistoryExportCsv": "CSV olarak dışa aktar", "paymentHistoryFilterAll": "<PERSON><PERSON><PERSON>", "paymentHistoryFilterDeposits": "<PERSON><PERSON><PERSON>", "paymentHistoryFilterWithdrawals": "<PERSON><PERSON><PERSON>", "paymentHistoryModalTitle": "Ödeme Geçmişi", "paymentHistorySearchPlaceholder": "Ara...", "paymentHistorySummaryDeposits": "Toplam Yatırılanlar", "paymentHistorySummaryNet": "Net Bakiye Değişimi", "paymentHistorySummaryWithdrawals": "Toplam Çekilenler", "paymentHistoryTableActions": "İşlemler", "paymentHistoryTableAmount": "Miktar (€)", "paymentHistoryTableChild": "Çocuk", "paymentHistoryTableDate": "<PERSON><PERSON><PERSON>", "paymentHistoryTableDesc": "<PERSON><PERSON>ı<PERSON><PERSON>", "paymentHistoryTableType": "<PERSON><PERSON><PERSON>", "paymentHistoryTypeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "paymentHistoryTypeWithdrawal": "Çek<PERSON>", "preferredStudioDesc": "<PERSON><PERSON><PERSON>, aktivite rezervasyonu yaparken önceden seçilecektir", "preferredStudioTitle": "<PERSON><PERSON><PERSON> Öğrenme Stüdy<PERSON>u", "regionDisabledText": "Seçilen ülke için bölge geçerli <PERSON>ğil", "regionLabel": "<PERSON><PERSON><PERSON>", "schoolTypeLabel": "Okul Türü", "selectChildCurrentBudgetLabel": "Mevcut Bütçe:", "selectChildFundsCancelButton": "İptal", "selectChildFundsModalTitle": "Bakiye Eklemek İçin Çocuk Seçin", "selectChildFundsPrompt": "Hangi çocuğun hesabına bakiye eklemek istediğinizi seçin:", "selectCountryFirst": "<PERSON><PERSON> ülk<PERSON> se<PERSON>", "selectPlaceholder": "<PERSON><PERSON><PERSON>...", "selectStudioPlaceholder": "-- <PERSON><PERSON> --", "statusAddChildError": "Çocuk eklenirken hata: {0}", "statusAddChildSuccess": "Çocuk başarıyla eklendi", "statusAddFundsError": "Bakiye eklenirken hata: {0}", "statusAddFundsSuccess": "{1}'in hesabına başarıyla €{0} eklendi", "statusAuthRequired": "Kimlik doğrulama gerekli.", "statusDeleteChildConfirm": "{0} ID'li ç<PERSON>ğ<PERSON> silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "statusDeleteChildError": "Çocuk silinirken hata: {0}", "statusDeleteChildSuccess": "Çocuk başar<PERSON><PERSON> si<PERSON>", "statusErrorChildren": "Çocuklar getirilirken hata", "statusErrorInitialData": "<PERSON><PERSON> veriler getirilirken hata: {0}", "statusErrorPreferredStudio": "<PERSON><PERSON><PERSON> edilen stüdyo getirilirken hata", "statusErrorStudios": "Stüdyolar getirilirken hata", "statusFindChildError": "Çocuk bilgileri bulunamadı.", "statusLoading": "Yükleniyor...", "statusUnexpectedError": "Beklenmeyen bir hata <PERSON>.", "statusUpdateChildSuccess": "Çocuk bilgileri başarıyla güncellendi.", "statusUpdatePreferredError": "<PERSON><PERSON><PERSON> edilen stüdyo güncellenirken hata: {0}", "statusUpdatePreferredSuccess": "Tercih edilen stüdyo başarıyla güncellendi", "subjectsLabel": "<PERSON><PERSON><PERSON>", "totalBudgetCardTitle": "Toplam Bütçe", "welcomeMessage": "<PERSON><PERSON> Geldiniz"}, "helpModal": {"bookingProcessIntro": "Öğrenme seansı ayarlamak için:", "bookingProcessLi1": "Kontrol panelinden bir çocuk seçin", "bookingProcessLi2": "Rezervasyon modalını açmak için takvim simgesine tıklayın", "bookingProcessLi3": "<PERSON><PERSON> tarih, saat ve aktivite tür<PERSON> seçin", "bookingProcessLi4": "Rezervasyonu on<PERSON> (bütçe otomatik olarak düşülecektir)", "bookingProcessLi5": "Tüm rezervasyonları Satın Alımlar bölümünde takip edin", "bookingProcessTitle": "Rezervasyon Süreci", "contactSupportButton": "Destek Ekibiyle İletişime Geçin", "faq1Answer": "Çocuğunuzun öğrenme bütçesini artırmak için kontrol panelindeki \"Bakiye Ekle\" düğ<PERSON>ini kullanın. K<PERSON>i kart<PERSON>, PayPal veya banka havalesi kullanabilirsiniz.", "faq1Question": "Çocuğumun hesabına nasıl bakiye eklerim?", "faq2Answer": "Evet. Bir çocuk hesabı oluştururken, bir kullanıcı adı ve şifre belirlersiniz. Çocuğunuz kendi sınırlı kontrol paneline erişmek için bu kimlik bilgilerini kullanabilir.", "faq2Question": "Çocuğum ayrı olarak giriş yapabilir mi?", "faq3Answer": "Satın Alımlar say<PERSON><PERSON> gid<PERSON>, iptal etmek istediğiniz rezervasyonu bulun ve \"Ayrıntıları Görüntüle\"ye tıklayın. Seans 24 saatten daha uzak bir zamandaysa iptal seçeneği bulacaksınız.", "faq3Question": "Bir rezervasyonu nasıl iptal ederim?", "faqTitle": "Sık Sorulan Sorular", "gettingStartedLi1": "\"Yeni Çocuk Ekle\" düğmesini kullanarak çocuklarınızı ekleyin", "gettingStartedLi2": "Kontrol panelinde tercih ettiğiniz öğrenme stüdyosunu ayarlayın", "gettingStartedLi3": "Her çocuğun aktiviteler için bütçesini yönetin", "gettingStartedLi4": "Öğrenme seansları ayarlayın ve ilerlemeyi takip edin", "gettingStartedTitle": "Başlarken", "gettingStartedWelcome": "Booking Parents uygulamasına hoş geldiniz! Bu platform, çocuklarınızın öğrenme aktivitelerini ve rezervasyonlarını tek bir yerden yönetmenize yardımcı olur.", "managingChildrenAddBudget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "managingChildrenAddBudgetDesc": ": Çocuğunuzun öğrenme bütçesini artırın", "managingChildrenBook": "Rezervasyon", "managingChildrenBookDesc": ": Tercih ettiğiniz stüdyoda öğrenme seansları planlayın", "managingChildrenDelete": "Sil", "managingChildrenDeleteDesc": ": Bir çocuk hesabını kaldırın (geri alınamaz)", "managingChildrenEdit": "<PERSON><PERSON><PERSON><PERSON>", "managingChildrenEditDesc": ": <PERSON><PERSON><PERSON>, bütçe ve rezervasyon izinlerini güncelleyin", "managingChildrenIntro": "Her çocuk için <PERSON> yapabilirsiniz:", "managingChildrenTitle": "Çocukları Yönetme", "needMoreHelpEmail": "E-posta:", "needMoreHelpHours": "Çalışma Saatleri: Pazartesi-Cuma, 09:00-17:00 CET", "needMoreHelpIntro": "Ek yardıma ihtiyacınız varsa, destek ekibimiz size yardımcı olmak için burada:", "needMoreHelpPhone": "Telefon:", "needMoreHelpTitle": "Daha Fazla Yardıma mı İhtiyacınız Var?", "title": "<PERSON><PERSON><PERSON>"}, "imprintModal": {"city": "10115 Berlin", "closeButton": "Ka<PERSON><PERSON>", "companyInfoTitle": "Şirket Bilgileri", "companyName": "Booking Parents GmbH", "contactTitle": "İletişim", "country": "Almanya", "directorsLabel": "<PERSON><PERSON>:", "directorsValue": "<PERSON>, <PERSON>", "disclaimerText": "Dikkatli içerik kontrolüne rağmen, ha<PERSON><PERSON> bağlantıların içeriği için hiçbir sorumluluk kabul etmiyoruz. Bağlantılı sayfaların operatörleri, içeriklerinden yalnızca kendileri sorumludur.", "disclaimerTitle": "Sorumluluk Reddi", "emailLabel": "E-posta:", "legalTitle": "<PERSON><PERSON>", "phoneLabel": "Telefon:", "regNumLabel": "<PERSON><PERSON><PERSON>:", "regNumValue": "HRB 123456", "registerLabel": "<PERSON><PERSON><PERSON>:", "registerValue": "Amtsgericht Berlin-Charlottenburg", "regulatoryTitle": "Düzenleyici Bilgiler", "responsibleLabel": "§ 55 Abs. 2 RStV uyarınca içerikten sorumlu:", "responsibleName": "<PERSON>", "street": "Musterstraße 123", "title": "K<PERSON><PERSON><PERSON>", "vatIdLabel": "KDV Kimlik No:", "vatIdValue": "DE123456789", "webLabel": "Web:"}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "bn": "<PERSON><PERSON>", "cs": "Çekçe", "da": "Danca", "de": "Almanca", "el": "Yunanca", "en": "İngilizce", "es": "İspanyolca", "fi": "Fince", "fr": "Fransızca", "gu": "<PERSON><PERSON><PERSON><PERSON>", "he": "İbranice", "hi": "<PERSON><PERSON><PERSON><PERSON>", "hu": "Macarca", "it": "İtalyanca", "ja": "Japonca", "jv": "Cavaca", "ko": "<PERSON><PERSON><PERSON>", "mr": "<PERSON><PERSON>", "nl": "Hollandaca", "no": "Norveççe", "pa": "Pencapça", "pl": "Lehçe", "pt": "Portekizce", "ro": "Romence", "ru": "Rus<PERSON>", "sv": "İsveççe", "ta": "<PERSON><PERSON>", "te": "Teluguca", "th": "Tayca", "tr": "Türkçe", "uk": "Ukraynaca", "ur": "Urduca", "vi": "Vietnamca", "zh": "<PERSON><PERSON><PERSON>"}, "login": {"childProgress": "Çocuk İlerlemesi", "easyScheduling": "<PERSON><PERSON>", "errors": {"connectionError": "Bağlantı hatası, lütfen internet bağlantınızı kontrol edin", "invalidCredentials": "Geçersiz kullanıcı adı veya şifre", "licenseValidationFailed": "Lisans doğrulaması başarısız oldu", "loginFailed": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "noActiveLicense": "Aktif lisans bulunamadı", "serverError": "<PERSON><PERSON><PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin", "sessionExpired": "Oturum süresi doldu, lütfen tekrar giriş yapın", "tryAgainLater": "<PERSON>ir hata <PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "unableToVerifyLicense": "Lisans doğru<PERSON>ı", "userNotFound": "Kullanıcı bulunamadı", "wrongPassword": "Yanlış şifre"}, "forgotPassword": "Şifrenizi mi unuttunuz?", "languageSwitch": "<PERSON><PERSON>", "loggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "login": "<PERSON><PERSON><PERSON>", "password": "Şifre", "summary": "Çocuğunuz için öğrenme seansları ayarlayın ve ilerlemelerini tek bir yerden takip edin.", "title": "<PERSON><PERSON><PERSON>", "username": "Kullanıcı adı"}, "privacyModal": {"acceptButton": "Anlıyorum ve Kabul Ediyorum", "childInfoDesc": "Çocukların<PERSON><PERSON> hakkında isim, yaş ve öğrenme tercihleri gibi bilgiler.", "childInfoTitle": "Çocuk Bilgileri:", "childrenIntro": "<PERSON>zmet<PERSON><PERSON>, temel rezervasyon hizmetlerimizi sağlamak için çocuklar hakkında bilgi gerektirir. Çocukların verilerini korumak için ek önlemler alıyoruz:", "childrenLi1": "Çocuklar hakkında yalnızca gerekli minimum bilgiyi topluyoruz", "childrenLi2": "Çocuklardan bilgi toplamadan önce ebeveyn izni istiyoruz", "childrenLi3": "Çocukların kişisel bilgilerini kamuya açık hale getirmiyoruz", "childrenLi4": "Ebeveynler çocuklarının bilgilerini inceleyebilir, silebilir veya daha fazla toplanmasını reddedebilir", "childrenLi5": "Çocukların verileri için ek güvenlik önlemleri uyguluyoruz", "childrenTitle": "Çocukların Gizliliği", "contactAddress": "Adres:", "contactEmail": "E-posta:", "contactIntro": "Bu Gizlilik Politikası hakkında herhangi bir sorunuz varsa, lütfen bizimle iletişime geçin:", "contactPhone": "Telefon:", "contactTitle": "Bize Ulaşın", "cookiesDesc": "Platformumuzdaki etkinliği izlemek ve belirli bilgileri tutmak için çerezler ve benzer izleme teknolojileri kullanıyoruz.", "cookiesTitle": "Çerezler ve İzleme:", "howWeUseIntro": "Hakkınızda topladığımız bilgileri çeşitli amaçlar için kullanabiliriz:", "howWeUseLi1": "Hizmetimizi sağlamak ve sürdürmek için", "howWeUseLi2": "Hizmetimizdeki değişiklikler hakkında sizi bilgilendirmek için", "howWeUseLi3": "Seçtiğinizde etkileşimli özelliklere katılmanıza izin vermek için", "howWeUseLi4": "Müşteri desteği sağlamak için", "howWeUseLi5": "Hizmetimizi geliştirmek için değerli analiz veya bilgi toplamak için", "howWeUseLi6": "Hizmetimizin kullanımını izlemek için", "howWeUseLi7": "Teknik sorunları tespit etmek, önlemek ve ele almak için", "howWeUseLi8": "Ödemeleri işlemek ve dolandırıcılık işlemleri önlemek için", "howWeUseLi9": "<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON>a veya tanıtım materyalleri ve diğer bilgilerle sizinle iletişim kurmak için", "howWeUseTitle": "Bilgilerinizi Nasıl Kullanıyoruz", "infoCollectedIntro": "Hakkınızda çeşitli şekillerde bilgi toplayabiliriz:", "infoCollectedTitle": "Topladığımız Bilgiler", "intro": "Booking Parents'ta gizliliğinizi ciddiye alıyoruz. Bu Gizlilik Politikası, platformumuzu kullandığınızda bilgilerinizi nasıl topladığımızı, kullandığımızı, açıkladığımızı ve koruduğumuzu açıklar. Lütfen bu gizlilik politikasını dikkatlice okuyun. Bu gizlilik politikasının şartlarını kabul etmiyorsanız, lütfen uygulamaya erişmeyin.", "lastUpdated": "<PERSON> günce<PERSON><PERSON>:", "paymentDataDesc": "Platformumuz aracılığıyla alışveriş yaptığınızda ödeme bilgilerini topluyoruz, ancak ödeme kartı bilgileri sunucularımızda saklanmaz.", "paymentDataTitle": "Ödeme Verileri:", "personalDataDesc": "Hizmetimizi kull<PERSON>ı<PERSON>en, sizinle iletişim kurmak veya sizi tanımlamak için kullanılabilecek kişisel olarak tanımlanabilir bilgiler sağlamanızı isteyebiliriz, bunlar aras<PERSON>nda isim, e-posta adresi, telefon numarası ve posta adresi bulunur.", "personalDataTitle": "<PERSON><PERSON><PERSON><PERSON>:", "rightsAccessDesc": "Kişisel bilgilerinizin kopyalarını talep etme hakkına sa<PERSON>iniz.", "rightsAccessTitle": "<PERSON><PERSON><PERSON><PERSON>:", "rightsContact": "<PERSON><PERSON> haklardan herhangi birini kullanmak için lütfen <EMAIL> adresinden bizimle iletişime geçin.", "rightsErasureDesc": "Kişisel bilgilerinizi silmemizi talep etme hakkına sa<PERSON>.", "rightsErasureTitle": "<PERSON><PERSON><PERSON>:", "rightsIntro": "Bulunduğunuz yere ba<PERSON><PERSON><PERSON>, kiş<PERSON>l bilgilerinizle ilgili belirli haklara sahip olabilirsiniz:", "rightsObjectDesc": "Kişisel bilgilerinizin işlenmesine itiraz etme hakkına sa<PERSON>iniz.", "rightsObjectTitle": "İtiraz Hakkı:", "rightsPortabilityDesc": "Bilgilerinizi başka bir kuruluşa veya doğrudan size aktarmamızı talep etme hakkına sa<PERSON>iniz.", "rightsPortabilityTitle": "Veri Taşınabilirliği Hakkı:", "rightsRectificationDesc": "Hakkınızdaki yanlış bilgileri düzeltmemizi talep etme hakkına sa<PERSON>z.", "rightsRectificationTitle": "Düzeltme Hakkı:", "rightsRestrictDesc": "Bilgilerinizin işlenmesini kısıtlamamızı talep etme hakkına sa<PERSON>iniz.", "rightsRestrictTitle": "İşlemeyi Kısıtlama Hakkı:", "rightsTitle": "Gizlilik Haklarınız", "securityIntro1": "Verilerinizin güvenliği bizim için önemlidir, ancak İnternet üzerinden hiçbir iletim yönteminin veya elektronik depolama yönteminin %100 güvenli olmadığını unutmayın. Kişisel Verilerinizi korumak için ticari olarak kabul edilebilir araçlar kullanmaya çalışırken, mutlak güvenliğini garanti edemeyiz.", "securityIntro2": "Güvenlik önlemlerimiz şunları içerir:", "securityLi1": "<PERSON><PERSON>s verilerin aktarım sırasında ve beklerken şifrelenmesi", "securityLi2": "Düzenli güvenlik değerlendirmeleri ve denetimler", "securityLi3": "Veri koruma konusunda çalışan eğitimi", "securityLi4": "Erişim kontrolleri ve kimlik doğrulama gereksinimleri", "securityLi5": "Tesislerimiz için fiziksel güvenlik önlemleri", "securityTitle": "<PERSON><PERSON>", "sharingBusinessTransfersDesc": "<PERSON><PERSON><PERSON> bir bi<PERSON><PERSON>, şirket varlıklarının satışı, finansman veya işimizin tamamının veya bir kısmının başka bir şirket tarafından satın alınması ile bağlantılı olarak.", "sharingBusinessTransfersTitle": "İş Transferleri:", "sharingConsentDesc": "İzninizle kişisel bilgilerinizi başka herhangi bir amaç için açıklayabiliriz.", "sharingConsentTitle": "İzninizle:", "sharingIntro": "Kişisel bilgilerinizi aşağıdaki durumlarda paylaşabiliriz:", "sharingLegalDesc": "<PERSON><PERSON>n tarafından gerekli görülürse veya kamu makamlarının geçerli taleplerine yanıt o<PERSON>.", "sharingLegalTitle": "<PERSON><PERSON>:", "sharingServiceProvidersDesc": "Hizmetimizi kolaylaştırmak, adımıza Hizmet sağlamak veya hizmetle ilgili hizmetleri gerçekleştirmek için kişisel bilgilerinizi üçüncü taraf hizmet sağlayıcılarla paylaşabiliriz.", "sharingServiceProvidersTitle": "Hizmet Sağlayıcılarla:", "sharingStudiosDesc": "Rezervasyonlarınızı ve öğrenme seanslarınızı kolaylaştırmak için öğrenme stüdyolarıyla gerekli bilgileri paylaşıyoruz.", "sharingStudiosTitle": "Öğrenme Stüdyolarıyla:", "sharingTitle": "Bilgi Paylaşımı ve Açıklama", "title": "Gizlilik Politikası", "usageDataDesc": "Hizmetin nasıl erişildiği ve kullanıldığı hakkında bilgiler, bilgisayarınızın İnternet Protokolü adresi, ta<PERSON><PERSON><PERSON><PERSON> türü, ziyaret edilen say<PERSON>, bu sayfalarda geçirilen süre ve diğer tanılama verileri dahil.", "usageDataTitle": "Kullanım Verileri:"}, "settingsModal": {"appearanceSectionTitle": "G<PERSON>rü<PERSON><PERSON><PERSON>", "bookingRemindersLabel": "Rezervasyon Hatırlatıcıları", "cancelButton": "İptal", "changePasswordButton": "<PERSON><PERSON><PERSON>", "currencyEUR": "EUR (€)", "currencyGBP": "GBP (£)", "currencyJPY": "JPY (¥)", "currencyLabel": "Para Birimi", "currencyUSD": "USD ($)", "darkModeLabel": "Karanlık Mod", "editProfileButton": "<PERSON><PERSON>", "enableNotificationsLabel": "Bildirimleri Etkinleştir", "fontSizeLabel": "Yazı Boyutu", "highContrastLabel": "<PERSON><PERSON><PERSON><PERSON>", "langArabic": "<PERSON><PERSON><PERSON>", "langChinese": "<PERSON><PERSON><PERSON>", "langDutch": "Hollandaca", "langEnglish": "İngilizce", "langFrench": "Fransızca", "langGerman": "Almanca", "langHebrew": "İbranice", "langHindi": "<PERSON><PERSON><PERSON><PERSON>", "langJapanese": "Japonca", "langSpanish": "İspanyolca", "languageLabel": "Dil", "lowBudgetAlertsLabel": "Düşük Bütçe Uyarıları", "marketingUpdatesLabel": "<PERSON><PERSON><PERSON><PERSON>", "notificationsSectionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preferencesSectionTitle": "<PERSON><PERSON><PERSON><PERSON>", "profileEmailPlaceholder": "<EMAIL>", "profileNamePlaceholder": "Adınız", "profileSectionTitle": "Profil", "saveButton": "Değişiklikleri Kaydet", "timezoneLabel": "Saat Dilimi", "title": "<PERSON><PERSON><PERSON>", "tzLondon": "<PERSON>ndra (GMT)", "tzLosAngeles": "Los Angeles (PST)", "tzNewYork": "New York (EST)", "tzParis": "Paris (CET)", "tzTokyo": "Tokyo (JST)"}, "subjects": {"Art": "Sanat", "Biology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Business Studies": "İşletme", "Chemistry": "<PERSON><PERSON>", "Computer Science": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Design and Technology": "Tasarım ve Teknoloji", "Drama": "Drama", "Economics": "Ekonomi", "English": "İngilizce", "Foreign Languages": "Yabancı Diller", "Geography": "Coğrafya", "History": "<PERSON><PERSON><PERSON>", "Mathematics": "Matematik", "Music": "Müzik", "Philosophy": "Felsefe", "Physical Education": "<PERSON><PERSON>", "Physics": "<PERSON><PERSON><PERSON>", "Psychology": "Psikoloji", "Religious Studies": "<PERSON>", "Social Studies": "<PERSON><PERSON><PERSON>"}, "purchases": {"purchaseHistory": "Satın Alma Geçmişi", "viewAndManage": "Tüm satın alımları ve rezervasyonları görüntüleyin ve yönetin", "downloadReport": "<PERSON><PERSON><PERSON>", "totalSpent": "<PERSON><PERSON>rca<PERSON>", "allCompletedPurchases": "<PERSON><PERSON><PERSON>lanan Sa<PERSON>ı<PERSON>", "totalPurchases": "Toplam Satın <PERSON>", "acrossAllChildren": "<PERSON><PERSON><PERSON>", "pending": "Beklemede", "awaitingConfirmation": "<PERSON><PERSON>", "transactions": "İşlemler", "all": "Tümü", "completed": "Tamamlandı", "failed": "Başarısız", "id": "ID", "product": "<PERSON><PERSON><PERSON><PERSON>", "child": "Çocuk", "date": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "status": "Durum", "action": "İşlem", "viewDetails": "Detayları Görüntüle", "noPurchasesFound": "Sa<PERSON>ın alım bulunamadı", "noPurchasesYet": "Henüz satın alım yapmadınız. Başlamak için ürünlerimize göz atın.", "noPurchasesFiltered": "<PERSON><PERSON> anda {filter} satın alımınız bulunmamaktadır.", "selectChild": "Çocuk Seçin", "allChildren": "<PERSON><PERSON><PERSON>", "filterByChild": "Çocuğa Göre Filtrele", "purchaseDetails": "Satın <PERSON>ları", "actions": "İşlemler", "markAsCompleted": "Tamamlandı Olarak İşaretle", "markAsFailed": "Başarısız Olarak İşaretle"}, "LearningDocumentation": {"Progress": {"pageTitle": "Progress Documentation", "skillsTitle": "Skills Progress", "skillsDescription": "View progress on various skills", "childSelectLabel": "Select Child", "skillAreaLabel": "Skill Area", "skillLabel": "Skill", "skillLevelLabel": "Skill Level", "progressLabel": "Progress (%)", "notesLabel": "Notes", "optionalText": "Optional", "saveButton": "Save", "cancelButton": "Cancel", "closeButton": "Close", "selectPlaceholder": "-- Select --", "successMessage": "Progress documentation saved successfully!", "errorMessage": "Failed to save progress documentation. Please try again.", "errorFetchingHistory": "Error fetching progress history", "existingSkillsTitle": "Progress Records", "selectChildFirst": "Please select a child first", "loadingProgressMessage": "Loading progress...", "noExistingSkillsMessage": "No progress documentation found for this child", "selectSkillAreaFirst": "Please select a skill area first", "noSkillLevelsMessage": "No skill levels available", "errorFetchingSkillLevels": "Error fetching skill levels", "errorFetchingSkills": "Error fetching skills", "errorFetchingProgress": "Error fetching progress", "noLevelAssigned": "No level assigned", "noSkillAreasMessage": "No skill areas available", "historyTitle": "Progress History", "historyEmpty": "No previous progress entries found", "historyDate": "Date", "levelPrefix": "Level", "progressPrefix": "Progress", "notesPrefix": "Notes", "loadingMessage": "Loading...", "lastUpdatedPrefix": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>"}, "eventTypes": {"achievement": "Başarı", "milestone": "Kilometre Taşı", "assessment": "Değerlendirme", "test": "Test", "exam": "Sınav", "report": "<PERSON><PERSON>", "presentation": "<PERSON><PERSON>", "project": "<PERSON><PERSON>", "workshop": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "Certificates": {"certificateDetailsTitle": "Sertifika Detayları", "certificateImageLabel": "<PERSON><PERSON><PERSON><PERSON>", "closeButton": "Ka<PERSON><PERSON>", "imageLoadError": "<PERSON><PERSON><PERSON>", "titleLabel": "Sertifika Başlığı", "issueDateLabel": "Veriliş Tarihi", "issueDateISOLabel": "<PERSON><PERSON><PERSON><PERSON> (ISO)", "relatedSkillsLabel": "<PERSON><PERSON><PERSON><PERSON>", "certificateIdLabel": "<PERSON><PERSON><PERSON><PERSON>"}}}